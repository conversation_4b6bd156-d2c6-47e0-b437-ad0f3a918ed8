<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Freeligo - Run Your Freelance Business Like a Pro')</title>
    <meta name="description" content="@yield('description', 'Professional freelance management platform with invoicing, contracts, client management, and AI-powered document generation.')">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700&family=poppins:400,500,600,700&family=manrope:400,500,600,700&family=open-sans:400,500,600,700&family=mulish:400,500,600,700&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-body antialiased bg-white">
    <!-- Navigation -->
    <nav x-data="{ open: false }" class="bg-white shadow-sm border-b border-slate-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <!-- Logo -->
                    <a href="{{ route('home') }}">
                        <x-freeligo-logo />
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="{{ route('home') }}" class="text-gray-700 hover:text-emerald-600 font-medium transition-colors {{ request()->routeIs('home') ? 'text-emerald-600' : '' }}">Home</a>
                    <a href="{{ route('features') }}" class="text-gray-700 hover:text-emerald-600 font-medium transition-colors {{ request()->routeIs('features') ? 'text-emerald-600' : '' }}">Features</a>
                    <a href="{{ route('pricing') }}" class="text-gray-700 hover:text-emerald-600 font-medium transition-colors {{ request()->routeIs('pricing') ? 'text-emerald-600' : '' }}">Pricing</a>
                    <a href="{{ route('about') }}" class="text-gray-700 hover:text-emerald-600 font-medium transition-colors {{ request()->routeIs('about') ? 'text-emerald-600' : '' }}">About</a>
                    <a href="{{ route('contact') }}" class="text-gray-700 hover:text-emerald-600 font-medium transition-colors {{ request()->routeIs('contact') ? 'text-emerald-600' : '' }}">Contact</a>
                    
                    @auth
                        <a href="{{ route('dashboard') }}" class="bg-gradient-to-r from-emerald-600 to-teal-600 text-white px-4 py-2 rounded-lg font-medium hover:from-emerald-700 hover:to-teal-700 transition-all duration-300 transform hover:scale-105">Dashboard</a>
                    @else
                        <a href="{{ route('login') }}" class="text-gray-700 hover:text-emerald-600 font-medium transition-colors">Login</a>
                        <a href="{{ route('register') }}" class="bg-gradient-to-r from-emerald-600 to-teal-600 text-white px-4 py-2 rounded-lg font-medium hover:from-emerald-700 hover:to-teal-700 transition-all duration-300 transform hover:scale-105">Get Started</a>
                    @endauth
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden flex items-center">
                    <button @click="open = !open" class="text-gray-700 hover:text-emerald-600 focus:outline-none p-2 touch-manipulation" aria-label="Toggle menu">
                        <svg class="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path x-show="!open" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            <path x-show="open" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div x-show="open" x-transition class="md:hidden bg-white border-t border-gray-100">
            <div class="px-4 py-3 space-y-2">
                <a href="{{ route('home') }}" class="block px-4 py-3 text-gray-700 hover:text-emerald-600 font-medium rounded-lg hover:bg-emerald-50 transition-colors touch-manipulation {{ request()->routeIs('home') ? 'text-emerald-600 bg-emerald-50' : '' }}">Home</a>
                <a href="{{ route('features') }}" class="block px-4 py-3 text-gray-700 hover:text-emerald-600 font-medium rounded-lg hover:bg-emerald-50 transition-colors touch-manipulation {{ request()->routeIs('features') ? 'text-emerald-600 bg-emerald-50' : '' }}">Features</a>
                <a href="{{ route('pricing') }}" class="block px-4 py-3 text-gray-700 hover:text-emerald-600 font-medium rounded-lg hover:bg-emerald-50 transition-colors touch-manipulation {{ request()->routeIs('pricing') ? 'text-emerald-600 bg-emerald-50' : '' }}">Pricing</a>
                <a href="{{ route('about') }}" class="block px-4 py-3 text-gray-700 hover:text-emerald-600 font-medium rounded-lg hover:bg-emerald-50 transition-colors touch-manipulation {{ request()->routeIs('about') ? 'text-emerald-600 bg-emerald-50' : '' }}">About</a>
                <a href="{{ route('contact') }}" class="block px-4 py-3 text-gray-700 hover:text-emerald-600 font-medium rounded-lg hover:bg-emerald-50 transition-colors touch-manipulation {{ request()->routeIs('contact') ? 'text-emerald-600 bg-emerald-50' : '' }}">Contact</a>

                <div class="border-t border-gray-100 pt-3 mt-3">
                    @auth
                        <a href="{{ route('dashboard') }}" class="block px-4 py-3 bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-xl font-medium hover:from-emerald-700 hover:to-teal-700 transition-all duration-300 text-center touch-manipulation">Dashboard</a>
                    @else
                        <a href="{{ route('login') }}" class="block px-4 py-3 text-gray-700 hover:text-emerald-600 font-medium rounded-lg hover:bg-emerald-50 transition-colors touch-manipulation mb-2">Login</a>
                        <a href="{{ route('register') }}" class="block px-4 py-3 bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-xl font-medium hover:from-emerald-700 hover:to-teal-700 transition-all duration-300 text-center touch-manipulation">Get Started</a>
                    @endauth
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div class="col-span-1 md:col-span-2">
                    <div class="mb-4">
                        <x-freeligo-logo theme="dark" />
                    </div>
                    <p class="text-gray-300 mb-4 max-w-md">Run Your Freelance Business Like a Pro. Professional invoicing, contract management, and client relationships made simple.</p>
                    <p class="text-sm text-gray-400">Smart Docs, Smarter Decisions — Powered by AI</p>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="font-heading font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="{{ route('features') }}" class="text-gray-300 hover:text-white transition-colors">Features</a></li>
                        <li><a href="{{ route('pricing') }}" class="text-gray-300 hover:text-white transition-colors">Pricing</a></li>
                        <li><a href="{{ route('about') }}" class="text-gray-300 hover:text-white transition-colors">About Us</a></li>
                        <li><a href="{{ route('contact') }}" class="text-gray-300 hover:text-white transition-colors">Contact</a></li>
                    </ul>
                </div>

                <!-- Legal -->
                <div>
                    <h3 class="font-heading font-semibold mb-4">Legal</h3>
                    <ul class="space-y-2">
                        <li><a href="{{ route('privacy') }}" class="text-gray-300 hover:text-white transition-colors">Privacy Policy</a></li>
                        <li><a href="{{ route('terms') }}" class="text-gray-300 hover:text-white transition-colors">Terms of Service</a></li>
                        <li><a href="{{ route('refund') }}" class="text-gray-300 hover:text-white transition-colors">Refund Policy</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center">
                <p class="text-gray-400">&copy; {{ date('Y') }} Freeligo. All rights reserved.</p>
            </div>
        </div>
    </footer>
</body>
</html>
