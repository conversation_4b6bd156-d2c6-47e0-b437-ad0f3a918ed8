<?php

namespace App\Mail;

use App\Models\Invoice;
use App\Models\InvoicePayment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class PaymentReceivedMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public Invoice $invoice;
    public InvoicePayment $payment;
    public string $recipientType; // 'client' or 'freelancer'

    /**
     * Create a new message instance.
     */
    public function __construct(Invoice $invoice, InvoicePayment $payment, string $recipientType = 'client')
    {
        $this->invoice = $invoice;
        $this->payment = $payment;
        $this->recipientType = $recipientType;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $subject = $this->recipientType === 'client'
            ? "Payment Confirmation - Invoice #{$this->invoice->invoice_number}"
            : "Payment Received - Invoice #{$this->invoice->invoice_number}";

        $to = $this->recipientType === 'client'
            ? [$this->invoice->client->email]
            : [$this->invoice->user->email];

        return new Envelope(
            subject: $subject,
            to: $to,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.payment-received',
            with: [
                'invoice' => $this->invoice,
                'payment' => $this->payment,
                'client' => $this->invoice->client,
                'freelancer' => $this->invoice->user,
                'recipientType' => $this->recipientType,
                'isFullyPaid' => $this->invoice->isFullyPaidByClients(),
                'remainingAmount' => $this->invoice->remaining_amount,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
