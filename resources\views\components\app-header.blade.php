@props(['title' => '', 'subtitle' => ''])

@php
    $user = auth()->user();
@endphp

<header class="bg-white border-b border-secondary-200 shadow-sm">
    <div class="px-6 py-4">
        <div class="flex items-center justify-between">
            <!-- Page Title -->
            <div class="flex items-center">
                <button @click="sidebarOpen = true" class="lg:hidden text-secondary-500 hover:text-secondary-700 p-2 rounded-lg hover:bg-secondary-100 transition-colors duration-200 mr-3">
                    <i class="fas fa-bars text-lg"></i>
                </button>
                <div>
                    @if($title)
                        <h1 class="text-2xl font-bold text-secondary-900">{{ $title }}</h1>
                    @endif
                    @if($subtitle)
                        <p class="text-sm text-secondary-600 mt-1">{{ $subtitle }}</p>
                    @endif
                </div>
            </div>

            <!-- Right Side Actions -->
            <div class="flex items-center space-x-4">
                <!-- Date/Time Info -->
                <div class="hidden sm:block text-right">
                    <p class="text-sm text-secondary-500">{{ now()->format('l, F j, Y') }}</p>
                    <p class="text-xs text-secondary-400">{{ now()->format('g:i A') }}</p>
                </div>

                <!-- New Invoice Button (for non-admin users) -->
                @if(!$user->hasRole('admin'))
                    <a href="{{ route('invoices.create') }}"
                       class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-primary-600 to-primary-700 text-white text-sm font-medium rounded-lg hover:from-primary-700 hover:to-primary-800 transition-all duration-200 shadow-sm hover:shadow-md">
                        <i class="fas fa-plus mr-2 text-xs"></i>
                        New Invoice
                    </a>
                @endif

                <!-- Upgrade Plan Button (for non-admin users) -->
                @if(!$user->hasRole('admin'))
                    <a href="{{ route('subscriptions.plans') }}"
                       class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-accent-600 to-accent-700 text-white text-sm font-medium rounded-lg hover:from-accent-700 hover:to-accent-800 transition-all duration-200 shadow-sm hover:shadow-md">
                        <i class="fas fa-crown mr-2 text-xs"></i>
                        Upgrade Plan
                    </a>
                @endif

                <!-- Profile Dropdown -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" 
                            class="flex items-center space-x-3 p-2 rounded-lg hover:bg-secondary-50 transition-colors duration-200">
                        <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center shadow-sm">
                            <span class="text-white font-medium text-xs">{{ substr($user->name, 0, 2) }}</span>
                        </div>
                        <div class="hidden sm:block text-left">
                            <p class="text-sm font-medium text-secondary-900">{{ $user->name }}</p>
                            <p class="text-xs text-secondary-500">{{ $user->email }}</p>
                        </div>
                        <i class="fas fa-chevron-down text-xs text-secondary-400 transition-transform duration-200" 
                           :class="{ 'rotate-180': open }"></i>
                    </button>

                    <!-- Dropdown Menu -->
                    <div x-show="open" 
                         @click.away="open = false"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 scale-95"
                         x-transition:enter-end="opacity-100 scale-100"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 scale-100"
                         x-transition:leave-end="opacity-0 scale-95"
                         class="absolute right-0 mt-2 w-56 bg-white rounded-xl shadow-lg border border-secondary-200 py-2 z-50">
                        
                        <!-- User Info -->
                        <div class="px-4 py-3 border-b border-secondary-100">
                            <p class="text-sm font-medium text-secondary-900">{{ $user->name }}</p>
                            <p class="text-xs text-secondary-500">{{ $user->email }}</p>
                            @if($user->hasRole('admin'))
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-accent-100 text-accent-800 mt-1">
                                    <i class="fas fa-crown mr-1 text-xs"></i>
                                    Admin
                                </span>
                            @endif
                        </div>

                        <!-- Menu Items -->
                        <div class="py-1">
                            <a href="{{ route('profile.edit') }}" 
                               class="flex items-center px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50 hover:text-secondary-900 transition-colors duration-200">
                                <i class="fas fa-user-cog mr-3 text-secondary-400"></i>
                                Account Settings
                            </a>
                            
                            @if(!$user->hasRole('admin'))
                                <a href="{{ route('subscriptions.plans') }}" 
                                   class="flex items-center px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50 hover:text-secondary-900 transition-colors duration-200">
                                    <i class="fas fa-crown mr-3 text-secondary-400"></i>
                                    Upgrade Plan
                                </a>
                            @endif
                            
                            <div class="border-t border-secondary-100 my-1"></div>
                            
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit" 
                                        onclick="return confirm('Are you sure you want to logout?')"
                                        class="w-full flex items-center px-4 py-2 text-sm text-danger-600 hover:bg-danger-50 hover:text-danger-700 transition-colors duration-200">
                                    <i class="fas fa-sign-out-alt mr-3 text-danger-400"></i>
                                    Logout
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>
