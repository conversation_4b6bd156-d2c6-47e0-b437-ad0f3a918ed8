@extends('client-portal.layout')

@section('content')
<div class="max-w-2xl mx-auto">
    <div class="text-center">
        <!-- Razorpay Logo -->
        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 mb-6">
            <i class="fas fa-credit-card text-blue-600 text-2xl"></i>
        </div>

        <!-- Processing Message -->
        <h2 class="text-3xl font-bold text-gray-900 mb-2">Complete Your Payment</h2>
        <p class="text-lg text-gray-600 mb-8">
            Click the button below to open the secure payment window.
        </p>

        <!-- Payment Summary -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8 text-left">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Payment Summary</h3>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-gray-600">Invoice Number:</span>
                    <span class="font-medium text-gray-900">{{ $invoice->invoice_number }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Payment Amount:</span>
                    <span class="font-medium text-gray-900">{{ $payment->formatted_amount }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Payment Method:</span>
                    <span class="font-medium text-gray-900">Razorpay</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Transaction ID:</span>
                    <span class="font-medium text-gray-900">{{ $payment->payment_id }}</span>
                </div>
            </div>
        </div>

        <!-- Payment Button -->
        <div class="mb-8">
            <button id="razorpay-button" 
                    class="w-full inline-flex justify-center items-center px-6 py-4 border border-transparent text-lg font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <i class="fas fa-credit-card mr-2"></i>
                Pay {{ $payment->formatted_amount }}
            </button>
        </div>

        <!-- Loading State -->
        <div id="loading-state" class="hidden">
            <div class="flex items-center justify-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span class="text-gray-600">Processing payment...</span>
            </div>
        </div>

        <!-- Payment Methods Info -->
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <h4 class="text-sm font-medium text-gray-900 mb-2">Accepted Payment Methods</h4>
            <div class="flex justify-center space-x-4 text-sm text-gray-600">
                <span><i class="fas fa-credit-card mr-1"></i>Cards</span>
                <span><i class="fas fa-university mr-1"></i>Net Banking</span>
                <span><i class="fas fa-mobile-alt mr-1"></i>UPI</span>
                <span><i class="fas fa-wallet mr-1"></i>Wallets</span>
            </div>
        </div>

        <!-- Cancel Button -->
        <div class="mt-6">
            <a href="{{ route('client-portal.invoice.payment', ['invoice' => $invoice->id, 'token' => $invoice->payment_token]) }}" 
               class="text-gray-500 hover:text-gray-700 text-sm">
                <i class="fas fa-arrow-left mr-1"></i>
                Cancel and go back
            </a>
        </div>
    </div>
</div>

@push('scripts')
<!-- Razorpay SDK -->
<script src="https://checkout.razorpay.com/v1/checkout.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const razorpayButton = document.getElementById('razorpay-button');
    const loadingState = document.getElementById('loading-state');
    
    const options = {
        "key": "{{ config('services.razorpay.key_id') }}",
        "amount": {{ $orderData['amount'] }}, // Amount in paise
        "currency": "{{ $orderData['currency'] }}",
        "name": "{{ config('app.name') }}",
        "description": "Payment for Invoice #{{ $invoice->invoice_number }}",
        "order_id": "{{ $orderData['id'] }}",
        "handler": function (response) {
            loadingState.classList.remove('hidden');
            razorpayButton.classList.add('hidden');
            
            // Verify payment on server
            fetch('{{ route("payment.razorpay.webhook") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    razorpay_payment_id: response.razorpay_payment_id,
                    razorpay_order_id: response.razorpay_order_id,
                    razorpay_signature: response.razorpay_signature,
                    invoice_payment_id: '{{ $payment->payment_id }}'
                })
            }).then(function(result) {
                // Redirect to success page
                window.location.href = '{{ route("client-portal.payment.success", ["invoice" => $invoice->id, "token" => $invoice->payment_token]) }}' + 
                    '?payment_id={{ $payment->payment_id }}&razorpay_payment_id=' + response.razorpay_payment_id;
            }).catch(function(error) {
                console.error('Payment verification failed:', error);
                window.location.href = '{{ route("client-portal.payment.failure", ["invoice" => $invoice->id, "token" => $invoice->payment_token]) }}' + 
                    '?payment_id={{ $payment->payment_id }}&reason=Payment verification failed';
            });
        },
        "prefill": {
            "name": "{{ $invoice->client->name }}",
            "email": "{{ $invoice->client->email }}",
            "contact": "{{ $invoice->client->phone ?? '' }}"
        },
        "notes": {
            "invoice_id": "{{ $invoice->id }}",
            "invoice_number": "{{ $invoice->invoice_number }}",
            "payment_id": "{{ $payment->payment_id }}"
        },
        "theme": {
            "color": "#059669"
        },
        "modal": {
            "ondismiss": function() {
                // Payment modal was closed
                window.location.href = '{{ route("client-portal.payment.failure", ["invoice" => $invoice->id, "token" => $invoice->payment_token]) }}' + 
                    '?payment_id={{ $payment->payment_id }}&reason=Payment cancelled by user';
            }
        }
    };

    const rzp = new Razorpay(options);
    
    razorpayButton.addEventListener('click', function(e) {
        e.preventDefault();
        rzp.open();
    });

    // Handle payment failure
    rzp.on('payment.failed', function (response) {
        console.error('Razorpay payment failed:', response.error);
        window.location.href = '{{ route("client-portal.payment.failure", ["invoice" => $invoice->id, "token" => $invoice->payment_token]) }}' + 
            '?payment_id={{ $payment->payment_id }}&reason=' + encodeURIComponent(response.error.description);
    });
});
</script>
@endpush
@endsection
