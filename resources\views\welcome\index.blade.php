<x-app-layout>
    <div class="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50">
        <!-- Hero Section -->
        <div class="relative overflow-hidden">
            <!-- Background Pattern -->
            <div class="absolute inset-0 opacity-5">
                <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-primary-600 to-transparent"></div>
                <div class="absolute -top-40 -left-40 w-80 h-80 bg-primary-600 rounded-full"></div>
                <div class="absolute -bottom-40 -right-40 w-80 h-80 bg-primary-600 rounded-full"></div>
            </div>
            
            <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
                <div class="text-center">
                    <!-- Welcome Animation -->
                    <div class="mb-8 animate-fade-in-up">
                        <div class="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full shadow-glow-lg mb-6">
                            <i class="fas fa-rocket text-4xl text-white"></i>
                        </div>
                        <h1 class="text-5xl lg:text-6xl font-bold text-secondary-900 mb-6 tracking-tight">
                            Welcome to {{ config('app.name') }}! 🎉
                        </h1>
                        <p class="text-xl lg:text-2xl text-secondary-600 max-w-3xl mx-auto leading-relaxed">
                            You're all set to transform how you manage your business. Let's get you started on your journey to success.
                        </p>
                    </div>
                    
                    <!-- Quick Stats -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto mb-16">
                        <div class="text-center">
                            <div class="text-4xl font-bold text-primary-600 mb-2">10,000+</div>
                            <div class="text-secondary-600">Happy Users</div>
                        </div>
                        <div class="text-center">
                            <div class="text-4xl font-bold text-success-600 mb-2">₹50M+</div>
                            <div class="text-secondary-600">Invoices Processed</div>
                        </div>
                        <div class="text-center">
                            <div class="text-4xl font-bold text-accent-600 mb-2">99.9%</div>
                            <div class="text-secondary-600">Uptime</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Getting Started Section -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-secondary-900 mb-4">Let's Get You Started</h2>
                <p class="text-xl text-secondary-600">Follow these simple steps to set up your business</p>
            </div>
            
            <!-- Step Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                <!-- Step 1 -->
                <div class="card hover:shadow-glow transition-all duration-300 group">
                    <div class="card-body text-center">
                        <div class="relative mb-6">
                            <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto group-hover:shadow-glow-lg transition-all duration-300">
                                <i class="fas fa-user-circle text-2xl text-white"></i>
                            </div>
                            <div class="absolute -top-2 -right-2 w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                        </div>
                        <h3 class="text-xl font-bold text-secondary-900 mb-3">Complete Your Profile</h3>
                        <p class="text-secondary-600 mb-6">Add your business information and customize your settings</p>
                        <x-ui.button href="{{ route('profile.edit') }}" variant="primary" size="sm" class="w-full">
                            Setup Profile
                        </x-ui.button>
                    </div>
                </div>
                
                <!-- Step 2 -->
                <div class="card hover:shadow-glow transition-all duration-300 group">
                    <div class="card-body text-center">
                        <div class="relative mb-6">
                            <div class="w-16 h-16 bg-gradient-to-br from-success-500 to-success-600 rounded-2xl flex items-center justify-center mx-auto group-hover:shadow-glow-lg transition-all duration-300">
                                <i class="fas fa-users text-2xl text-white"></i>
                            </div>
                            <div class="absolute -top-2 -right-2 w-8 h-8 bg-success-600 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                        </div>
                        <h3 class="text-xl font-bold text-secondary-900 mb-3">Add Your First Client</h3>
                        <p class="text-secondary-600 mb-6">Start building your client database for better organization</p>
                        <x-ui.button href="{{ route('clients.create') }}" variant="outline" size="sm" class="w-full">
                            Add Client
                        </x-ui.button>
                    </div>
                </div>
                
                <!-- Step 3 -->
                <div class="card hover:shadow-glow transition-all duration-300 group">
                    <div class="card-body text-center">
                        <div class="relative mb-6">
                            <div class="w-16 h-16 bg-gradient-to-br from-accent-500 to-accent-600 rounded-2xl flex items-center justify-center mx-auto group-hover:shadow-glow-lg transition-all duration-300">
                                <i class="fas fa-file-invoice-dollar text-2xl text-white"></i>
                            </div>
                            <div class="absolute -top-2 -right-2 w-8 h-8 bg-accent-600 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                        </div>
                        <h3 class="text-xl font-bold text-secondary-900 mb-3">Create Your First Invoice</h3>
                        <p class="text-secondary-600 mb-6">Send professional invoices and get paid faster</p>
                        <x-ui.button href="{{ route('invoices.create') }}" variant="outline" size="sm" class="w-full">
                            Create Invoice
                        </x-ui.button>
                    </div>
                </div>
            </div>
            
            <!-- Main Action -->
            <div class="text-center">
                <x-ui.button 
                    href="{{ route('dashboard') }}" 
                    variant="primary" 
                    size="lg" 
                    class="px-12 py-4 text-lg font-semibold shadow-card-hover transform hover:scale-105"
                    icon="fas fa-tachometer-alt"
                >
                    Go to Dashboard
                </x-ui.button>
                <p class="text-sm text-secondary-500 mt-4">You can always come back to complete these steps later</p>
            </div>
        </div>
        
        <!-- Features Showcase -->
        <div class="bg-gradient-to-r from-secondary-50 to-neutral-50 py-20">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl font-bold text-secondary-900 mb-4">Everything You Need to Succeed</h2>
                    <p class="text-xl text-secondary-600">Powerful features to grow your business</p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <!-- Feature 1 -->
                    <div class="text-center group">
                        <div class="w-16 h-16 bg-gradient-to-br from-primary-100 to-primary-200 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:shadow-glow transition-all duration-300">
                            <i class="fas fa-file-invoice-dollar text-2xl text-primary-600"></i>
                        </div>
                        <h3 class="font-semibold text-secondary-900 mb-2">Professional Invoicing</h3>
                        <p class="text-secondary-600 text-sm">Create beautiful, branded invoices in seconds</p>
                    </div>
                    
                    <!-- Feature 2 -->
                    <div class="text-center group">
                        <div class="w-16 h-16 bg-gradient-to-br from-success-100 to-success-200 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:shadow-glow transition-all duration-300">
                            <i class="fas fa-chart-line text-2xl text-success-600"></i>
                        </div>
                        <h3 class="font-semibold text-secondary-900 mb-2">Business Analytics</h3>
                        <p class="text-secondary-600 text-sm">Track revenue, expenses, and growth</p>
                    </div>
                    
                    <!-- Feature 3 -->
                    <div class="text-center group">
                        <div class="w-16 h-16 bg-gradient-to-br from-accent-100 to-accent-200 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:shadow-glow transition-all duration-300">
                            <i class="fas fa-users text-2xl text-accent-600"></i>
                        </div>
                        <h3 class="font-semibold text-secondary-900 mb-2">Client Management</h3>
                        <p class="text-secondary-600 text-sm">Organize clients and projects effortlessly</p>
                    </div>
                    
                    <!-- Feature 4 -->
                    <div class="text-center group">
                        <div class="w-16 h-16 bg-gradient-to-br from-warning-100 to-warning-200 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:shadow-glow transition-all duration-300">
                            <i class="fas fa-clock text-2xl text-warning-600"></i>
                        </div>
                        <h3 class="font-semibold text-secondary-900 mb-2">Time Tracking</h3>
                        <p class="text-secondary-600 text-sm">Monitor project time and productivity</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Support Section -->
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div class="card">
                <div class="card-body text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-primary-100 to-primary-200 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-headset text-2xl text-primary-600"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-secondary-900 mb-4">Need Help Getting Started?</h3>
                    <p class="text-secondary-600 mb-8">Our support team is here to help you succeed. Get assistance, tutorials, and tips.</p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <x-ui.button href="#" variant="outline" icon="fas fa-book">
                            View Documentation
                        </x-ui.button>
                        <x-ui.button href="#" variant="primary" icon="fas fa-comments">
                            Contact Support
                        </x-ui.button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Include Onboarding Components -->
    <x-onboarding.checklist />
    <x-onboarding.tour :auto-start="true" />
</x-app-layout>
