<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

        <title><?php echo e(config('app.name', 'Freeligo')); ?></title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700&family=poppins:400,500,600,700&display=swap" rel="stylesheet" />

        <!-- FontAwesome -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

        <!-- Scripts -->
        <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

        <!-- Toast Notifications -->
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
        <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    </head>
    <body class="font-inter antialiased bg-secondary-50">
        <?php if (isset($component)) { $__componentOriginal2880b66d47486b4bfeaf519598a469d6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2880b66d47486b4bfeaf519598a469d6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.sidebar','data' => ['user' => auth()->user(),'title' => $title ?? '','subtitle' => $subtitle ?? '']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['user' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(auth()->user()),'title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($title ?? ''),'subtitle' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($subtitle ?? '')]); ?>
            <!-- Flash Messages -->
            <?php if(session('success')): ?>
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        Toastify({
                            text: "<?php echo e(session('success')); ?>",
                            duration: 4000,
                            gravity: "top",
                            position: "right",
                            backgroundColor: "#10b981",
                            className: "toast-success",
                        }).showToast();
                    });
                </script>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        Toastify({
                            text: "<?php echo e(session('error')); ?>",
                            duration: 5000,
                            gravity: "top",
                            position: "right",
                            backgroundColor: "#ef4444",
                            className: "toast-error",
                        }).showToast();
                    });
                </script>
            <?php endif; ?>

            <?php if(session('warning')): ?>
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        Toastify({
                            text: "<?php echo e(session('warning')); ?>",
                            duration: 4000,
                            gravity: "top",
                            position: "right",
                            backgroundColor: "#f59e0b",
                            className: "toast-warning",
                        }).showToast();
                    });
                </script>
            <?php endif; ?>

            <?php if(session('info')): ?>
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        Toastify({
                            text: "<?php echo e(session('info')); ?>",
                            duration: 4000,
                            gravity: "top",
                            position: "right",
                            backgroundColor: "#3b82f6",
                            className: "toast-info",
                        }).showToast();
                    });
                </script>
            <?php endif; ?>

            <!-- Validation Errors -->
            <?php if($errors->any()): ?>
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            Toastify({
                                text: "<?php echo e($error); ?>",
                                duration: 5000,
                                gravity: "top",
                                position: "right",
                                backgroundColor: "#ef4444",
                                className: "toast-error",
                            }).showToast();
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    });
                </script>
            <?php endif; ?>

            <!-- Page Content -->
            <div class="p-6 transition-all duration-300 ease-in-out">
                <div class="max-w-7xl mx-auto">
                    <?php echo e($slot); ?>

                </div>
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2880b66d47486b4bfeaf519598a469d6)): ?>
<?php $attributes = $__attributesOriginal2880b66d47486b4bfeaf519598a469d6; ?>
<?php unset($__attributesOriginal2880b66d47486b4bfeaf519598a469d6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2880b66d47486b4bfeaf519598a469d6)): ?>
<?php $component = $__componentOriginal2880b66d47486b4bfeaf519598a469d6; ?>
<?php unset($__componentOriginal2880b66d47486b4bfeaf519598a469d6); ?>
<?php endif; ?>

        <!-- Custom Styles -->
        <style>
            .toast-success, .toast-error, .toast-warning, .toast-info {
                border-radius: 8px;
                font-family: 'Inter', sans-serif;
                font-weight: 500;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            }

            .animate-fadeIn {
                animation: fadeIn 0.3s ease-in-out;
            }

            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
            }
        </style>
    </body>
</html>
<?php /**PATH C:\laragon\www\freeligo\resources\views/layouts/app.blade.php ENDPATH**/ ?>