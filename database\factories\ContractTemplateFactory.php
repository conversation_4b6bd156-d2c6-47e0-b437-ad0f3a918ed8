<?php

namespace Database\Factories;

use App\Models\ContractTemplate;
use Illuminate\Database\Eloquent\Factories\Factory;

class ContractTemplateFactory extends Factory
{
    protected $model = ContractTemplate::class;

    public function definition(): array
    {
        return [
            'name' => fake()->words(3, true) . ' Contract Template',
            'type' => fake()->randomElement(['service_agreement', 'nda', 'consulting', 'web_development']),
            'content' => $this->generateContractContent(),
            'variables' => json_encode([
                'client_name',
                'business_name',
                'project_name',
                'amount',
                'duration',
                'start_date',
                'end_date',
                'payment_terms',
            ]),
            'is_active' => true,
        ];
    }

    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    public function webDevelopment(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Web Development Contract',
            'type' => 'web_development',
            'content' => $this->generateWebDevelopmentContract(),
        ]);
    }

    public function consulting(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Consulting Services Contract',
            'type' => 'consulting',
            'content' => $this->generateConsultingContract(),
        ]);
    }

    public function design(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Design Services Contract',
            'type' => 'design',
            'content' => $this->generateDesignContract(),
        ]);
    }

    private function generateContractContent(): string
    {
        return "
        SERVICE AGREEMENT

        This Service Agreement (\"Agreement\") is entered into on [start_date] between [client_name] (\"Client\") and [business_name] (\"Service Provider\").

        1. SCOPE OF WORK
        The Service Provider agrees to provide [project_name] services for a total amount of [amount].

        2. DURATION
        This agreement shall remain in effect for [duration] from the start date.

        3. PAYMENT TERMS
        Payment terms: [payment_terms]

        4. DELIVERABLES
        The Service Provider will deliver the agreed-upon services as specified in the project scope.

        5. TERMINATION
        Either party may terminate this agreement with 30 days written notice.

        By signing below, both parties agree to the terms and conditions outlined in this agreement.
        ";
    }

    private function generateWebDevelopmentContract(): string
    {
        return "
        WEB DEVELOPMENT SERVICE AGREEMENT

        This Web Development Service Agreement is entered into between [client_name] and the Developer.

        PROJECT: [project_name]
        AMOUNT: [amount]
        DURATION: [duration]

        SCOPE OF WORK:
        - Website design and development
        - Responsive design implementation
        - Content management system setup
        - Testing and deployment

        PAYMENT TERMS: [payment_terms]
        ";
    }

    private function generateConsultingContract(): string
    {
        return "
        CONSULTING SERVICES AGREEMENT

        Client: [client_name]
        Project: [project_name]
        Fee: [amount]
        Duration: [duration]

        CONSULTING SERVICES:
        - Strategic planning and analysis
        - Implementation guidance
        - Regular progress reviews
        - Final recommendations report

        PAYMENT: [payment_terms]
        ";
    }

    private function generateDesignContract(): string
    {
        return "
        DESIGN SERVICES AGREEMENT

        Client: [client_name]
        Project: [project_name]
        Total Fee: [amount]
        Timeline: [duration]

        DESIGN SERVICES:
        - Concept development
        - Design mockups and prototypes
        - Revisions (up to 3 rounds)
        - Final design files delivery

        PAYMENT SCHEDULE: [payment_terms]
        ";
    }
}
