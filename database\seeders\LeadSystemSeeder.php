<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\LeadSource;
use App\Models\LeadStage;
use App\Models\User;

class LeadSystemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all users to create default sources and stages for each
        $users = User::all();

        foreach ($users as $user) {
            $this->createDefaultLeadSources($user);
            $this->createDefaultLeadStages($user);
        }
    }

    /**
     * Create default lead sources for a user
     */
    private function createDefaultLeadSources(User $user): void
    {
        $defaultSources = [
            [
                'name' => 'Website',
                'description' => 'Leads from website contact forms and inquiries',
                'color' => '#3B82F6',
                'is_system_source' => true,
            ],
            [
                'name' => 'Referral',
                'description' => 'Leads from client referrals and word of mouth',
                'color' => '#10B981',
                'is_system_source' => true,
            ],
            [
                'name' => 'Social Media',
                'description' => 'Leads from social media platforms',
                'color' => '#8B5CF6',
                'is_system_source' => true,
            ],
            [
                'name' => 'Cold Email',
                'description' => 'Leads from cold email outreach campaigns',
                'color' => '#F59E0B',
                'is_system_source' => true,
            ],
            [
                'name' => 'Networking',
                'description' => 'Leads from networking events and professional connections',
                'color' => '#EF4444',
                'is_system_source' => true,
            ],
            [
                'name' => 'Online Marketplace',
                'description' => 'Leads from freelance platforms and marketplaces',
                'color' => '#06B6D4',
                'is_system_source' => true,
            ],
        ];

        foreach ($defaultSources as $sourceData) {
            LeadSource::firstOrCreate(
                [
                    'user_id' => $user->id,
                    'name' => $sourceData['name'],
                ],
                array_merge($sourceData, ['user_id' => $user->id])
            );
        }
    }

    /**
     * Create default lead stages for a user
     */
    private function createDefaultLeadStages(User $user): void
    {
        $defaultStages = [
            [
                'name' => 'New Lead',
                'description' => 'Newly acquired leads that need initial qualification',
                'color' => '#6B7280',
                'sort_order' => 1,
                'conversion_probability' => 10,
                'is_system_stage' => true,
            ],
            [
                'name' => 'Qualified',
                'description' => 'Leads that have been qualified and show genuine interest',
                'color' => '#3B82F6',
                'sort_order' => 2,
                'conversion_probability' => 25,
                'is_system_stage' => true,
            ],
            [
                'name' => 'Proposal Sent',
                'description' => 'Leads who have received a proposal',
                'color' => '#F59E0B',
                'sort_order' => 3,
                'conversion_probability' => 50,
                'is_system_stage' => true,
            ],
            [
                'name' => 'Negotiation',
                'description' => 'Leads in active negotiation phase',
                'color' => '#8B5CF6',
                'sort_order' => 4,
                'conversion_probability' => 75,
                'is_system_stage' => true,
            ],
            [
                'name' => 'Won',
                'description' => 'Successfully converted leads',
                'color' => '#10B981',
                'sort_order' => 5,
                'conversion_probability' => 100,
                'is_won_stage' => true,
                'is_system_stage' => true,
            ],
            [
                'name' => 'Lost',
                'description' => 'Leads that did not convert',
                'color' => '#EF4444',
                'sort_order' => 6,
                'conversion_probability' => 0,
                'is_lost_stage' => true,
                'is_system_stage' => true,
            ],
        ];

        foreach ($defaultStages as $stageData) {
            LeadStage::firstOrCreate(
                [
                    'user_id' => $user->id,
                    'name' => $stageData['name'],
                ],
                array_merge($stageData, ['user_id' => $user->id])
            );
        }
    }
}
