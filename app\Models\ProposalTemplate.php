<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ProposalTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'description',
        'category',
        'content',
        'sections',
        'variables',
        'is_system_template',
        'is_active',
        'usage_count',
        'average_rating',
        'tags',
    ];

    protected function casts(): array
    {
        return [
            'sections' => 'array',
            'variables' => 'array',
            'tags' => 'array',
            'is_system_template' => 'boolean',
            'is_active' => 'boolean',
            'usage_count' => 'integer',
            'average_rating' => 'decimal:2',
        ];
    }

    /**
     * Get the user that owns the template.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get proposals using this template.
     */
    public function proposals(): HasMany
    {
        return $this->hasMany(Proposal::class);
    }

    /**
     * Scope a query to only include active templates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include system templates.
     */
    public function scopeSystem($query)
    {
        return $query->where('is_system_template', true);
    }

    /**
     * Scope a query to only include user templates.
     */
    public function scopeUser($query, $userId = null)
    {
        $userId = $userId ?? auth()->id();
        return $query->where('user_id', $userId)->where('is_system_template', false);
    }

    /**
     * Scope for templates available to user.
     */
    public function scopeAvailableToUser($query, $userId = null)
    {
        $userId = $userId ?? auth()->id();
        return $query->where(function ($q) use ($userId) {
            $q->where('is_system_template', true)
              ->orWhere('user_id', $userId);
        })->where('is_active', true);
    }

    /**
     * Increment usage count.
     */
    public function incrementUsage(): void
    {
        $this->increment('usage_count');
    }

    /**
     * Get template variables for replacement.
     */
    public function getTemplateVariables(): array
    {
        return $this->variables ?? [];
    }

    /**
     * Replace variables in content.
     */
    public function replaceVariables(array $values): string
    {
        $content = $this->content;
        
        foreach ($values as $key => $value) {
            $content = str_replace("{{$key}}", $value, $content);
        }
        
        return $content;
    }
}
