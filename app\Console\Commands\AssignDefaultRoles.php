<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;

class AssignDefaultRoles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'roles:assign-default';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Assign default freelancer role to users who don\'t have any role';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Assigning default roles to users...');

        $usersWithoutRoles = User::whereDoesntHave('roles')->get();

        if ($usersWithoutRoles->isEmpty()) {
            $this->info('All users already have roles assigned.');
            return;
        }

        $count = 0;
        foreach ($usersWithoutRoles as $user) {
            $user->assignRole('freelancer');
            $count++;
            $this->line("Assigned 'freelancer' role to: {$user->email}");
        }

        $this->info("Successfully assigned default roles to {$count} users.");
    }
}
