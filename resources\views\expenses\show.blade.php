<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Expense Details') }} - {{ $expense->expense_number }}
            </h2>
            <div class="flex space-x-2">
                @if($expense->canBeEdited())
                    <a href="{{ route('expenses.edit', $expense) }}" 
                       class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                        <i class="fas fa-edit mr-2"></i>
                        Edit
                    </a>
                @endif
                <a href="{{ route('expenses.index') }}" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Expenses
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <!-- Main Expense Details -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Left Column -->
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-500">Expense Number</label>
                                <p class="text-lg font-semibold text-gray-900">{{ $expense->expense_number }}</p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-500">Category</label>
                                <div class="flex items-center mt-1">
                                    <i class="fas {{ $expense->category->icon ?? 'fa-tag' }} text-{{ $expense->category->color ?? 'gray' }}-500 mr-2"></i>
                                    <span class="text-gray-900">{{ $expense->category->name }}</span>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-500">Description</label>
                                <p class="text-gray-900">{{ $expense->description }}</p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-500">Expense Date</label>
                                <p class="text-gray-900">{{ $expense->expense_date->format('F d, Y') }}</p>
                            </div>

                            @if($expense->client)
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Client</label>
                                    <p class="text-gray-900">{{ $expense->client->display_name }}</p>
                                </div>
                            @endif

                            @if($expense->project)
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Project</label>
                                    <p class="text-gray-900">{{ $expense->project->name }}</p>
                                </div>
                            @endif
                        </div>

                        <!-- Right Column -->
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-500">Amount</label>
                                <p class="text-2xl font-bold text-gray-900">${{ number_format($expense->amount, 2) }}</p>
                                @if($expense->tax_amount > 0)
                                    <p class="text-sm text-gray-600">
                                        Tax: ${{ number_format($expense->tax_amount, 2) }} ({{ $expense->tax_percentage }}%)
                                    </p>
                                    <p class="text-lg font-semibold text-gray-900">
                                        Total: ${{ number_format($expense->total_amount, 2) }}
                                    </p>
                                @endif
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-500">Status</label>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium 
                                    @if($expense->status === 'approved') bg-green-100 text-green-800
                                    @elseif($expense->status === 'rejected') bg-red-100 text-red-800
                                    @elseif($expense->status === 'submitted') bg-yellow-100 text-yellow-800
                                    @else bg-gray-100 text-gray-800 @endif">
                                    {{ ucfirst($expense->status) }}
                                </span>
                            </div>

                            @if($expense->payment_method)
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Payment Method</label>
                                    <p class="text-gray-900">{{ ucwords(str_replace('_', ' ', $expense->payment_method)) }}</p>
                                </div>
                            @endif

                            @if($expense->vendor_name)
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Vendor/Merchant</label>
                                    <p class="text-gray-900">{{ $expense->vendor_name }}</p>
                                </div>
                            @endif

                            @if($expense->reference_number)
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Reference Number</label>
                                    <p class="text-gray-900">{{ $expense->reference_number }}</p>
                                </div>
                            @endif

                            <div>
                                <label class="block text-sm font-medium text-gray-500">Billable</label>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                    {{ $expense->is_billable ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                    {{ $expense->is_billable ? 'Yes' : 'No' }}
                                </span>
                                @if($expense->is_billable && $expense->is_billed)
                                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        Billed
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>

                    @if($expense->notes)
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <label class="block text-sm font-medium text-gray-500 mb-2">Notes</label>
                            <p class="text-gray-900 whitespace-pre-wrap">{{ $expense->notes }}</p>
                        </div>
                    @endif

                    @if($expense->receipt_path)
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <label class="block text-sm font-medium text-gray-500 mb-2">Receipt/Document</label>
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-file-alt text-gray-400 text-2xl"></i>
                                <div>
                                    <a href="{{ Storage::url($expense->receipt_path) }}" 
                                       target="_blank"
                                       class="text-blue-600 hover:text-blue-800 font-medium">
                                        View Receipt
                                    </a>
                                    <p class="text-sm text-gray-500">{{ basename($expense->receipt_path) }}</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    @if($expense->approved_at && $expense->approvedBy)
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <label class="block text-sm font-medium text-gray-500 mb-2">Approval Details</label>
                            <p class="text-gray-900">
                                Approved by {{ $expense->approvedBy->name }} on {{ $expense->approved_at->format('F d, Y \a\t g:i A') }}
                            </p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="flex flex-wrap gap-3">
                        @if($expense->status === 'draft')
                            <form action="{{ route('expenses.submit', $expense) }}" method="POST" class="inline">
                                @csrf
                                @method('PATCH')
                                <button type="submit" 
                                        class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors duration-200"
                                        onclick="return confirm('Are you sure you want to submit this expense for approval?')">
                                    <i class="fas fa-paper-plane mr-2"></i>
                                    Submit for Approval
                                </button>
                            </form>
                        @endif

                        @can('approve', $expense)
                            @if($expense->canBeApproved())
                                <form action="{{ route('expenses.approve', $expense) }}" method="POST" class="inline">
                                    @csrf
                                    @method('PATCH')
                                    <button type="submit" 
                                            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-200"
                                            onclick="return confirm('Are you sure you want to approve this expense?')">
                                        <i class="fas fa-check mr-2"></i>
                                        Approve
                                    </button>
                                </form>

                                <button type="button" 
                                        onclick="openRejectModal()"
                                        class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                                    <i class="fas fa-times mr-2"></i>
                                    Reject
                                </button>
                            @endif
                        @endcan

                        @if($expense->canBeDeleted())
                            <form action="{{ route('expenses.destroy', $expense) }}" method="POST" class="inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" 
                                        class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-200"
                                        onclick="return confirm('Are you sure you want to delete this expense? This action cannot be undone.')">
                                    <i class="fas fa-trash mr-2"></i>
                                    Delete
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reject Modal -->
    <div id="rejectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Reject Expense</h3>
                <form action="{{ route('expenses.reject', $expense) }}" method="POST">
                    @csrf
                    @method('PATCH')
                    <div class="mb-4">
                        <label for="reason" class="block text-sm font-medium text-gray-700 mb-2">
                            Reason for rejection (optional)
                        </label>
                        <textarea name="reason" 
                                  id="reason"
                                  rows="3"
                                  placeholder="Provide a reason for rejecting this expense..."
                                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"></textarea>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button" 
                                onclick="closeRejectModal()"
                                class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            Cancel
                        </button>
                        <button type="submit" 
                                class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            Reject Expense
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function openRejectModal() {
            document.getElementById('rejectModal').classList.remove('hidden');
        }

        function closeRejectModal() {
            document.getElementById('rejectModal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('rejectModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeRejectModal();
            }
        });
    </script>
</x-app-layout>
