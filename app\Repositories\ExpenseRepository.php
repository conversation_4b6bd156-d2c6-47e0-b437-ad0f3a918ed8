<?php

namespace App\Repositories;

use App\Models\Expense;
use App\Traits\HasDateRanges;
use App\Traits\HasFiltering;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class ExpenseRepository extends BaseRepository
{
    use HasDateRanges, HasFiltering;

    protected function getModel(): Model
    {
        return new Expense();
    }

    /**
     * Get expenses for a specific user with filters
     */
    public function getForUser(int $userId, Request $request = null): LengthAwarePaginator
    {
        $query = $this->model->newQuery()
            ->with(['category:id,name,icon,color', 'client:id,name,company_name', 'project:id,name'])
            ->where('user_id', $userId);

        if ($request) {
            $query = $this->applyFilters($query, $request, [
                'search_fields' => ['description', 'expense_number', 'vendor_name', 'reference_number'],
                'status_field' => 'status',
                'date_field' => 'expense_date',
                'sort_by' => 'expense_date',
                'sort_direction' => 'desc',
            ]);

            // Additional filters
            if ($request->filled('category_id')) {
                $query->where('category_id', $request->category_id);
            }

            if ($request->filled('client_id')) {
                $query->where('client_id', $request->client_id);
            }

            if ($request->filled('project_id')) {
                $query->where('project_id', $request->project_id);
            }

            if ($request->filled('is_billable')) {
                $query->where('is_billable', $request->boolean('is_billable'));
            }

            if ($request->filled('payment_method')) {
                $query->where('payment_method', $request->payment_method);
            }

            return $this->applyPagination($query, $request);
        }

        return $query->latest('expense_date')->paginate(15);
    }

    /**
     * Get expenses for dashboard
     */
    public function getForDashboard(int $userId, int $limit = 5): \Illuminate\Database\Eloquent\Collection
    {
        return $this->model->newQuery()
            ->forUser($userId)
            ->forDashboard()
            ->latest('expense_date')
            ->limit($limit)
            ->get();
    }

    /**
     * Get monthly expense totals for a user
     */
    public function getMonthlyTotals(int $userId, int $year = null): array
    {
        $year = $year ?? now()->year;
        
        return $this->model->newQuery()
            ->forUser($userId)
            ->whereYear('expense_date', $year)
            ->selectRaw('MONTH(expense_date) as month, SUM(amount + tax_amount) as total')
            ->groupBy('month')
            ->orderBy('month')
            ->pluck('total', 'month')
            ->toArray();
    }

    /**
     * Get expense totals by category for a user
     */
    public function getTotalsByCategory(int $userId, string $startDate = null, string $endDate = null): array
    {
        $query = $this->model->newQuery()
            ->forUser($userId)
            ->join('expense_categories', 'expenses.category_id', '=', 'expense_categories.id')
            ->selectRaw('expense_categories.name, expense_categories.color, SUM(expenses.amount + expenses.tax_amount) as total')
            ->groupBy('expense_categories.id', 'expense_categories.name', 'expense_categories.color');

        if ($startDate && $endDate) {
            $query->whereBetween('expense_date', [$startDate, $endDate]);
        }

        return $query->orderByDesc('total')->get()->toArray();
    }

    /**
     * Get billable expenses for a client
     */
    public function getBillableForClient(int $clientId, bool $unbilledOnly = true): \Illuminate\Database\Eloquent\Collection
    {
        $query = $this->model->newQuery()
            ->where('client_id', $clientId)
            ->where('is_billable', true)
            ->with(['category:id,name,icon']);

        if ($unbilledOnly) {
            $query->where('is_billed', false);
        }

        return $query->orderBy('expense_date')->get();
    }

    /**
     * Get expense statistics for a user
     */
    public function getStatistics(int $userId, string $period = 'month'): array
    {
        $startDate = match($period) {
            'week' => now()->startOfWeek(),
            'month' => now()->startOfMonth(),
            'quarter' => now()->startOfQuarter(),
            'year' => now()->startOfYear(),
            default => now()->startOfMonth(),
        };

        $query = $this->model->newQuery()->forUser($userId)->where('expense_date', '>=', $startDate);

        return [
            'total_amount' => $query->sum('amount'),
            'total_tax' => $query->sum('tax_amount'),
            'total_expenses' => $query->count(),
            'billable_amount' => $query->where('is_billable', true)->sum('amount'),
            'categories_count' => $query->distinct('category_id')->count(),
        ];
    }

    /**
     * Get total expenses for a specific period
     */
    public function getTotalForPeriod(int $userId, $startDate, $endDate): float
    {
        return $this->model->newQuery()
            ->forUser($userId)
            ->whereBetween('expense_date', [$startDate, $endDate])
            ->sum(\DB::raw('amount + tax_amount'));
    }

    /**
     * Get tax deductible expenses total
     */
    public function getTaxDeductibleTotal(int $userId, $startDate): float
    {
        return $this->model->newQuery()
            ->forUser($userId)
            ->join('expense_categories', 'expenses.category_id', '=', 'expense_categories.id')
            ->where('expense_categories.is_tax_deductible', true)
            ->where('expense_date', '>=', $startDate)
            ->sum(\DB::raw('expenses.amount + expenses.tax_amount'));
    }

    /**
     * Get billable expenses total
     */
    public function getBillableTotal(int $userId, $startDate): float
    {
        return $this->model->newQuery()
            ->forUser($userId)
            ->where('is_billable', true)
            ->where('expense_date', '>=', $startDate)
            ->sum(\DB::raw('amount + tax_amount'));
    }

    /**
     * Get top expense categories
     */
    public function getTopCategories(int $userId, int $limit = 5): array
    {
        return $this->model->newQuery()
            ->forUser($userId)
            ->join('expense_categories', 'expenses.category_id', '=', 'expense_categories.id')
            ->selectRaw('expense_categories.name, expense_categories.color, expense_categories.icon, SUM(expenses.amount + expenses.tax_amount) as total, COUNT(*) as count')
            ->groupBy('expense_categories.id', 'expense_categories.name', 'expense_categories.color', 'expense_categories.icon')
            ->orderByDesc('total')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    /**
     * Get filtered expenses with relationships
     */
    public function getFilteredExpenses(int $userId, array $filters): \Illuminate\Database\Eloquent\Collection
    {
        $query = $this->model->newQuery()
            ->forUser($userId)
            ->with(['category', 'client']);

        if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
            $query->whereBetween('expense_date', [$filters['start_date'], $filters['end_date']]);
        }

        if (!empty($filters['category_id'])) {
            $query->where('category_id', $filters['category_id']);
        }

        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['is_billable'])) {
            $query->where('is_billable', $filters['is_billable']);
        }

        if (!empty($filters['client_id'])) {
            $query->where('client_id', $filters['client_id']);
        }

        if (isset($filters['is_tax_deductible'])) {
            $query->whereHas('category', function($q) use ($filters) {
                $q->where('is_tax_deductible', $filters['is_tax_deductible']);
            });
        }

        return $query->orderBy('expense_date', 'desc')->get();
    }

    /**
     * Get expenses for a specific period
     */
    public function getExpensesForPeriod(int $userId, $startDate, $endDate): \Illuminate\Database\Eloquent\Collection
    {
        return $this->model->newQuery()
            ->forUser($userId)
            ->with(['category', 'client'])
            ->whereBetween('expense_date', [$startDate, $endDate])
            ->orderBy('expense_date', 'desc')
            ->get();
    }

    /**
     * Get expense statistics for user
     */
    public function getStatsForUser(int $userId): array
    {
        $query = $this->model->newQuery()->forUser($userId);

        // Calculate total amount using raw SQL since total_amount is an accessor
        $totalAmount = $query->clone()->selectRaw('SUM(amount + tax_amount) as total')->value('total') ?? 0;

        return [
            'total_expenses' => $query->clone()->count(),
            'total_expense_amount' => $totalAmount,
            'approved_expenses' => $query->clone()->where('status', 'approved')->count(),
            'pending_expenses' => $query->clone()->where('status', 'submitted')->count(),
            'draft_expenses' => $query->clone()->where('status', 'draft')->count(),
        ];
    }

    /**
     * Get recent expenses for a user
     */
    public function getRecent(int $userId, int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return $this->model->newQuery()
            ->forUser($userId)
            ->forListing()
            ->latest('expense_date')
            ->limit($limit)
            ->get();
    }

    /**
     * Get recent expenses for user (alias for dashboard compatibility)
     */
    public function getRecentForUser(int $userId, int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return $this->model->newQuery()
            ->where('user_id', $userId)
            ->with(['category:id,name,icon,color', 'client:id,name,company_name'])
            ->latest('expense_date')
            ->limit($limit)
            ->get();
    }

    /**
     * Search expenses for user
     */
    public function searchForUser(int $userId, string $term): \Illuminate\Database\Eloquent\Collection
    {
        return $this->model->newQuery()
            ->forUser($userId)
            ->where(function ($query) use ($term) {
                $query->where('description', 'like', "%{$term}%")
                      ->orWhere('expense_number', 'like', "%{$term}%")
                      ->orWhere('vendor_name', 'like', "%{$term}%")
                      ->orWhere('reference_number', 'like', "%{$term}%");
            })
            ->with(['category:id,name,icon', 'client:id,name'])
            ->latest('expense_date')
            ->limit(20)
            ->get();
    }
}
