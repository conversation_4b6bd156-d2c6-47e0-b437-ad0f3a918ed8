<?php

namespace Database\Factories;

use App\Models\Contract;
use App\Models\User;
use App\Models\Client;
use App\Models\ContractTemplate;
use Illuminate\Database\Eloquent\Factories\Factory;

class ContractFactory extends Factory
{
    protected $model = Contract::class;

    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'client_id' => Client::factory(),
            'contract_template_id' => ContractTemplate::factory(),
            'title' => fake()->sentence(4),
            'content' => fake()->paragraphs(5, true),
            'variables' => json_encode([
                'client_name' => fake()->name(),
                'business_name' => fake()->company(),
                'project_name' => fake()->words(3, true),
                'amount' => fake()->numberBetween(10000, 100000),
                'duration' => fake()->numberBetween(1, 12) . ' months',
            ]),
            'status' => fake()->randomElement(['draft', 'sent', 'signed', 'expired']),
            'pdf_path' => null,
            'sent_date' => null,
            'signed_date' => null,
        ];
    }

    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
            'sent_date' => null,
            'signed_date' => null,
        ]);
    }

    public function sent(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'sent',
            'sent_date' => fake()->dateTimeBetween('-30 days', 'now'),
            'signed_date' => null,
        ]);
    }

    public function signed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'signed',
            'sent_date' => fake()->dateTimeBetween('-60 days', '-30 days'),
            'signed_date' => fake()->dateTimeBetween('-30 days', 'now'),
        ]);
    }

    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'expired',
            'sent_date' => fake()->dateTimeBetween('-90 days', '-60 days'),
            'signed_date' => null,
        ]);
    }

    public function forUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
        ]);
    }

    public function forClient(Client $client): static
    {
        return $this->state(fn (array $attributes) => [
            'client_id' => $client->id,
            'user_id' => $client->user_id,
        ]);
    }

    public function withTemplate(ContractTemplate $template): static
    {
        return $this->state(fn (array $attributes) => [
            'contract_template_id' => $template->id,
        ]);
    }
}
