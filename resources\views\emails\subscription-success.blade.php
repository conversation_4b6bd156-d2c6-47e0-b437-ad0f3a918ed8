@extends('emails.layouts.app')

@section('content')
    <!-- Success Message -->
    <div style="text-align: center; margin-bottom: 32px;">
        <div style="font-size: 64px; margin-bottom: 16px;">🎉</div>
        <h2 style="color: #166534; margin-bottom: 8px;">Payment Successful!</h2>
        <p class="lead" style="color: #15803d;">
            Congratulations, {{ $user->name }}! Your payment has been processed successfully and you now have access to all {{ $plan->name ?? 'Pro' }} features.
        </p>
    </div>
    
    <!-- Payment Receipt -->
    <div class="success-box">
        <h3 style="color: #166534; margin: 0 0 16px 0;">📄 Payment Receipt</h3>
        <table style="width: 100%; border-collapse: collapse;">
            <tr style="border-bottom: 1px solid #bbf7d0;">
                <td style="padding: 12px 0; color: #15803d; font-weight: 600;">Plan:</td>
                <td style="padding: 12px 0; color: #166534; font-weight: 700; text-align: right;">{{ $plan->name ?? 'Pro Plan' }}</td>
            </tr>
            <tr style="border-bottom: 1px solid #bbf7d0;">
                <td style="padding: 12px 0; color: #15803d; font-weight: 600;">Billing Cycle:</td>
                <td style="padding: 12px 0; color: #166534; font-weight: 700; text-align: right;">{{ ucfirst($plan->billing_cycle ?? 'Monthly') }}</td>
            </tr>
            <tr style="border-bottom: 1px solid #bbf7d0;">
                <td style="padding: 12px 0; color: #15803d; font-weight: 600;">Amount Paid:</td>
                <td style="padding: 12px 0; color: #166534; font-weight: 700; text-align: right; font-size: 18px;">₹{{ number_format($plan->price ?? 199, 2) }}</td>
            </tr>
            <tr style="border-bottom: 1px solid #bbf7d0;">
                <td style="padding: 12px 0; color: #15803d; font-weight: 600;">Payment Date:</td>
                <td style="padding: 12px 0; color: #166534; font-weight: 700; text-align: right;">{{ now()->format('M d, Y') }}</td>
            </tr>
            <tr style="border-bottom: 1px solid #bbf7d0;">
                <td style="padding: 12px 0; color: #15803d; font-weight: 600;">Transaction ID:</td>
                <td style="padding: 12px 0; color: #166534; font-weight: 700; text-align: right; font-family: monospace;">{{ $paymentDetails['transaction_id'] ?? 'TXN' . strtoupper(uniqid()) }}</td>
            </tr>
            <tr>
                <td style="padding: 12px 0; color: #15803d; font-weight: 600;">Next Billing Date:</td>
                <td style="padding: 12px 0; color: #166534; font-weight: 700; text-align: right;">{{ $subscription->next_billing_date ?? now()->addMonth()->format('M d, Y') }}</td>
            </tr>
        </table>
        
        <div style="margin-top: 20px; padding: 16px; background-color: rgba(255, 255, 255, 0.7); border-radius: 8px;">
            <p style="color: #15803d; font-size: 14px; margin: 0; text-align: center;">
                <strong>📧 Receipt sent to:</strong> {{ $user->email }}
                <br>
                <em>Keep this email for your records</em>
            </p>
        </div>
    </div>
    
    <!-- Plan Features Unlocked -->
    <h3>🚀 Features Now Available</h3>
    <p>Your {{ $plan->name ?? 'Pro' }} plan includes all these powerful features:</p>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 16px; margin: 24px 0;">
        <div style="background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); padding: 20px; border-radius: 12px; border: 1px solid #bbf7d0;">
            <div style="color: #166534; font-size: 24px; margin-bottom: 8px;">∞</div>
            <h4 style="margin: 0 0 8px 0; color: #166534; font-size: 16px;">Unlimited Invoices</h4>
            <p style="margin: 0; color: #15803d; font-size: 14px;">Create and send unlimited professional invoices</p>
        </div>
        
        <div style="background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%); padding: 20px; border-radius: 12px; border: 1px solid #bae6fd;">
            <div style="color: #1e40af; font-size: 24px; margin-bottom: 8px;">📊</div>
            <h4 style="margin: 0 0 8px 0; color: #1e40af; font-size: 16px;">Advanced Reports</h4>
            <p style="margin: 0; color: #2563eb; font-size: 14px;">Detailed analytics and business insights</p>
        </div>
        
        <div style="background: linear-gradient(135deg, #fdf4ff 0%, #fae8ff 100%); padding: 20px; border-radius: 12px; border: 1px solid #f5d0fe;">
            <div style="color: #a21caf; font-size: 24px; margin-bottom: 8px;">⚡</div>
            <h4 style="margin: 0 0 8px 0; color: #a21caf; font-size: 16px;">Priority Support</h4>
            <p style="margin: 0; color: #c026d3; font-size: 14px;">Get help faster with priority assistance</p>
        </div>
        
        <div style="background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%); padding: 20px; border-radius: 12px; border: 1px solid #fde68a;">
            <div style="color: #92400e; font-size: 24px; margin-bottom: 8px;">🎨</div>
            <h4 style="margin: 0 0 8px 0; color: #92400e; font-size: 16px;">Custom Branding</h4>
            <p style="margin: 0; color: #b45309; font-size: 14px;">Add your logo and brand colors</p>
        </div>
        
        <div style="background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%); padding: 20px; border-radius: 12px; border: 1px solid #99f6e4;">
            <div style="color: #0f766e; font-size: 24px; margin-bottom: 8px;">🔄</div>
            <h4 style="margin: 0 0 8px 0; color: #0f766e; font-size: 16px;">Automated Reminders</h4>
            <p style="margin: 0; color: #0d9488; font-size: 14px;">Smart payment follow-ups</p>
        </div>
        
        <div style="background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%); padding: 20px; border-radius: 12px; border: 1px solid #fecaca;">
            <div style="color: #dc2626; font-size: 24px; margin-bottom: 8px;">🔐</div>
            <h4 style="margin: 0 0 8px 0; color: #dc2626; font-size: 16px;">Enhanced Security</h4>
            <p style="margin: 0; color: #ef4444; font-size: 14px;">Advanced data protection</p>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <h3>🎯 What's Next?</h3>
    <p>Here are some things you can do right now to make the most of your new plan:</p>
    
    <div style="display: flex; flex-wrap: wrap; gap: 12px; margin: 24px 0;">
        <a href="{{ route('dashboard') }}" class="btn btn-primary">
            📊 Go to Dashboard
        </a>
        <a href="{{ route('invoices.create') }}" class="btn btn-outline">
            📄 Create Invoice
        </a>
        <a href="{{ route('profile.edit') }}" class="btn btn-outline">
            🎨 Setup Branding
        </a>
    </div>
    
    <!-- Billing Information -->
    <div class="info-box">
        <h3>💳 Billing Information</h3>
        <div style="display: flex; flex-wrap: wrap; gap: 20px;">
            <div style="flex: 1; min-width: 200px;">
                <h4 style="color: #1e293b; margin: 0 0 8px 0; font-size: 16px;">Automatic Renewal</h4>
                <p style="color: #64748b; margin: 0; font-size: 14px;">
                    Your subscription will automatically renew on <strong>{{ $subscription->next_billing_date ?? now()->addMonth()->format('M d, Y') }}</strong> 
                    for ₹{{ number_format($plan->price ?? 199, 2) }}.
                </p>
            </div>
            <div style="flex: 1; min-width: 200px;">
                <h4 style="color: #1e293b; margin: 0 0 8px 0; font-size: 16px;">Manage Subscription</h4>
                <p style="color: #64748b; margin: 0; font-size: 14px;">
                    You can update your billing information, change plans, or cancel anytime from your account settings.
                </p>
            </div>
        </div>
        
        <div style="margin-top: 16px; text-align: center;">
            <a href="{{ route('profile.edit') }}" style="color: #10b981; text-decoration: none; font-weight: 600;">
                Manage Billing Settings →
            </a>
        </div>
    </div>
    
    <!-- Support Section -->
    <div style="background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); border: 1px solid #e2e8f0; border-radius: 12px; padding: 24px; margin: 24px 0; text-align: center;">
        <div style="font-size: 48px; margin-bottom: 16px;">🎧</div>
        <h3 style="color: #1e293b; margin: 0 0 12px 0;">Need Help Getting Started?</h3>
        <p style="color: #64748b; margin: 0 0 20px 0;">
            Our support team is here to help you make the most of your new plan. Get personalized assistance and tips for growing your business.
        </p>
        <div style="display: flex; flex-wrap: wrap; gap: 12px; justify-content: center;">
            <a href="#" class="btn btn-secondary">
                📚 View Tutorials
            </a>
            <a href="#" class="btn btn-primary">
                💬 Contact Support
            </a>
        </div>
    </div>
    
    <!-- Thank You Message -->
    <div style="text-align: center; margin: 40px 0;">
        <h3 style="color: #1e293b; margin-bottom: 16px;">Thank You for Upgrading! 🙏</h3>
        <p style="color: #64748b; font-size: 18px; margin-bottom: 20px;">
            We're excited to be part of your business journey and can't wait to see what you'll achieve with {{ config('app.name') }}.
        </p>
        <p style="color: #64748b; font-size: 16px;">
            Here's to your continued success!
        </p>
    </div>
    
    <!-- Footer Note -->
    <div style="border-top: 1px solid #e2e8f0; padding-top: 20px; margin-top: 32px;">
        <p style="color: #64748b; font-size: 14px; text-align: center; margin: 0;">
            Questions about your subscription? Reply to this email or visit our 
            <a href="#" style="color: #10b981; text-decoration: none;">Help Center</a> for instant answers.
        </p>
    </div>
@endsection
