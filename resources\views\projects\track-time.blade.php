<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Time Tracking</h1>
                <p class="text-gray-600 mt-1">{{ $project->name }} - Track Time</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('projects.show', $project) }}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Project
                </a>
                @if(!$activeTimer)
                    <button onclick="openStartTimerModal()"
                            class="bg-green-600 hover:bg-green-700 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-200">
                        <i class="fas fa-play mr-2"></i>Start Timer
                    </button>
                @endif
                <a href="{{ route('time-tracking.create', ['project_id' => $project->id]) }}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-200">
                    <i class="fas fa-plus mr-2"></i>Manual Entry
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Project Info -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">{{ $project->name }}</h3>
                            @if($project->client)
                                <p class="text-gray-600">Client: {{ $project->client->name }}</p>
                            @endif
                        </div>
                        <div class="flex items-center space-x-4">
                            @if($project->hourly_rate)
                                <span class="text-sm text-gray-600">Rate: ${{ number_format($project->hourly_rate, 2) }}/hr</span>
                            @endif
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                @if($project->status === 'active') bg-green-100 text-green-800
                                @elseif($project->status === 'completed') bg-blue-100 text-blue-800
                                @elseif($project->status === 'on_hold') bg-yellow-100 text-yellow-800
                                @else bg-gray-100 text-gray-800 @endif">
                                {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Timer -->
            @if($activeTimer && $activeTimer->project_id == $project->id)
                <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse mr-3"></div>
                            <div>
                                <h3 class="text-lg font-semibold text-green-900">Timer Running</h3>
                                <p class="text-green-700">{{ $activeTimer->description ?? 'No description' }}</p>
                                @if($activeTimer->task)
                                    <p class="text-sm text-green-600">Task: {{ $activeTimer->task->title }}</p>
                                @endif
                            </div>
                        </div>
                        <div class="flex items-center space-x-4">
                            <div class="text-right">
                                <div class="text-2xl font-bold text-green-900" id="timer-display">
                                    {{ gmdate('H:i:s', $activeTimer->start_time->diffInSeconds(now())) }}
                                </div>
                                <div class="text-sm text-green-700">Started: {{ $activeTimer->start_time->format('g:i A') }}</div>
                            </div>
                            <button onclick="stopTimer({{ $activeTimer->id }})"
                                    class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg">
                                <i class="fas fa-stop mr-2"></i>Stop Timer
                            </button>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Time Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-clock text-blue-600"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Hours</p>
                            <p class="text-2xl font-bold text-gray-900">{{ number_format($project->timeEntries->sum('duration_minutes') / 60, 1) }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-dollar-sign text-green-600"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Billable Hours</p>
                            <p class="text-2xl font-bold text-gray-900">{{ number_format($project->timeEntries->where('is_billable', true)->sum('duration_minutes') / 60, 1) }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-calendar-week text-purple-600"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">This Week</p>
                            <p class="text-2xl font-bold text-gray-900">{{ number_format($project->timeEntries->where('start_time', '>=', now()->startOfWeek())->sum('duration_minutes') / 60, 1) }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-money-bill-wave text-yellow-600"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Value</p>
                            <p class="text-2xl font-bold text-gray-900">
                                ${{ number_format($project->timeEntries->where('is_billable', true)->sum(function($entry) { return ($entry->duration_minutes / 60) * ($entry->hourly_rate ?? $entry->project->hourly_rate ?? 0); }), 2) }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Time Entries -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">Time Entries</h3>
                        <div class="flex space-x-3">
                            <input type="date" id="dateFilter" class="border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <select id="taskFilter" class="border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">All Tasks</option>
                                @foreach($tasks as $task)
                                    <option value="{{ $task->id }}">{{ $task->title }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    @if($timeEntries->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Task</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Billable</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($timeEntries as $entry)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $entry->start_time->format('M d, Y') }}
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="text-sm text-gray-900">{{ $entry->description ?? 'No description' }}</div>
                                                <div class="text-xs text-gray-500">
                                                    {{ $entry->start_time->format('g:i A') }} - 
                                                    @if($entry->end_time)
                                                        {{ $entry->end_time->format('g:i A') }}
                                                    @else
                                                        <span class="text-green-600">Running</span>
                                                    @endif
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                @if($entry->task)
                                                    {{ $entry->task->title }}
                                                @else
                                                    <span class="text-gray-400">No task</span>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                @if($entry->duration_minutes)
                                                    {{ gmdate('H:i', $entry->duration_minutes * 60) }}
                                                @else
                                                    <span class="text-green-600">Running</span>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                ${{ number_format($entry->hourly_rate ?? $project->hourly_rate ?? 0, 2) }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                @if($entry->is_billable)
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        Billable
                                                    </span>
                                                @else
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                        Non-billable
                                                    </span>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <div class="flex space-x-2">
                                                    <a href="{{ route('time-tracking.edit', $entry) }}" 
                                                       class="text-indigo-600 hover:text-indigo-900">Edit</a>
                                                    @if($entry->is_running)
                                                        <button onclick="stopTimer({{ $entry->id }})" 
                                                                class="text-red-600 hover:text-red-900">Stop</button>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="mt-6">
                            {{ $timeEntries->links() }}
                        </div>
                    @else
                        <div class="text-center py-12">
                            <i class="fas fa-clock text-gray-400 text-4xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No time entries yet</h3>
                            <p class="text-gray-600 mb-4">Start tracking time for this project.</p>
                            <button onclick="openStartTimerModal()" 
                                    class="bg-green-600 hover:bg-green-700 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-200">
                                <i class="fas fa-play mr-2"></i>Start Timer
                            </button>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Start Timer Modal -->
    <div id="startTimerModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Start Timer</h3>
                <form id="startTimerForm">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <input type="text" id="timerDescription" class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="What are you working on?">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Task (Optional)</label>
                        <select id="timerTask" class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="">Select a task</option>
                            @foreach($tasks as $task)
                                <option value="{{ $task->id }}">{{ $task->title }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-4">
                        <label class="flex items-center">
                            <input type="checkbox" id="timerBillable" checked class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-700">Billable</span>
                        </label>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeStartTimerModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded">
                            Cancel
                        </button>
                        <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded">
                            Start Timer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        // Define functions globally to ensure they're accessible
        window.openStartTimerModal = function() {
            document.getElementById('startTimerModal').classList.remove('hidden');
        };

        window.closeStartTimerModal = function() {
            document.getElementById('startTimerModal').classList.add('hidden');
        };

        // Wait for DOM to be ready
        document.addEventListener('DOMContentLoaded', function() {
            const startTimerForm = document.getElementById('startTimerForm');
            if (startTimerForm) {
                startTimerForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = {
                        project_id: {{ $project->id }},
                        task_id: document.getElementById('timerTask').value || null,
                        description: document.getElementById('timerDescription').value,
                        is_billable: document.getElementById('timerBillable').checked
                    };

                    fetch('/time-tracking/start', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify(formData)
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            location.reload();
                        } else {
                            alert('Error starting timer: ' + (data.message || 'Unknown error'));
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Error starting timer');
                    });
                });
            }
        });

        window.stopTimer = function(timerId) {
            if (confirm('Are you sure you want to stop this timer?')) {
                fetch('/time-tracking/stop', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ time_entry_id: timerId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error stopping timer: ' + (data.message || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error stopping timer');
                });
            }
        };

        // Update timer display if active
        @if($activeTimer && $activeTimer->project_id == $project->id)
        setInterval(function() {
            const startTime = new Date('{{ $activeTimer->start_time->toISOString() }}');
            const now = new Date();
            const diff = Math.floor((now - startTime) / 1000);
            
            const hours = Math.floor(diff / 3600);
            const minutes = Math.floor((diff % 3600) / 60);
            const seconds = diff % 60;
            
            document.getElementById('timer-display').textContent = 
                String(hours).padStart(2, '0') + ':' + 
                String(minutes).padStart(2, '0') + ':' + 
                String(seconds).padStart(2, '0');
        }, 1000);
        @endif
    </script>
    @endpush
</x-app-layout>
