<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Edit Expense Category') }} - {{ $expenseCategory->name }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('admin.expense-categories.show', $expenseCategory) }}" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                    <i class="fas fa-eye mr-2"></i>
                    View
                </a>
                <a href="{{ route('admin.expense-categories.index') }}" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Categories
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-3xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('admin.expense-categories.update', $expenseCategory) }}" class="space-y-6">
                        @csrf
                        @method('PUT')

                        <!-- Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                Category Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" 
                                   id="name" 
                                   name="name" 
                                   value="{{ old('name', $expenseCategory->name) }}"
                                   required
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('name') border-red-300 @enderror"
                                   placeholder="e.g., Office Supplies">
                            @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                                Description
                            </label>
                            <textarea id="description" 
                                      name="description" 
                                      rows="3"
                                      class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('description') border-red-300 @enderror"
                                      placeholder="Brief description of this expense category">{{ old('description', $expenseCategory->description) }}</textarea>
                            @error('description')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Icon and Color Row -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Icon -->
                            <div>
                                <label for="icon" class="block text-sm font-medium text-gray-700 mb-2">
                                    Icon <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <input type="text" 
                                           id="icon" 
                                           name="icon" 
                                           value="{{ old('icon', $expenseCategory->icon) }}"
                                           required
                                           class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('icon') border-red-300 @enderror pl-10"
                                           placeholder="fa-folder">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas {{ $expenseCategory->icon }} text-gray-400" id="icon-preview"></i>
                                    </div>
                                </div>
                                <p class="mt-1 text-sm text-gray-500">
                                    FontAwesome icon class (e.g., fa-folder, fa-paperclip, fa-plane)
                                </p>
                                @error('icon')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Color -->
                            <div>
                                <label for="color" class="block text-sm font-medium text-gray-700 mb-2">
                                    Color <span class="text-red-500">*</span>
                                </label>
                                <select id="color" 
                                        name="color" 
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('color') border-red-300 @enderror">
                                    <option value="">Select Color</option>
                                    <option value="blue" {{ old('color', $expenseCategory->color) === 'blue' ? 'selected' : '' }}>Blue</option>
                                    <option value="green" {{ old('color', $expenseCategory->color) === 'green' ? 'selected' : '' }}>Green</option>
                                    <option value="purple" {{ old('color', $expenseCategory->color) === 'purple' ? 'selected' : '' }}>Purple</option>
                                    <option value="red" {{ old('color', $expenseCategory->color) === 'red' ? 'selected' : '' }}>Red</option>
                                    <option value="yellow" {{ old('color', $expenseCategory->color) === 'yellow' ? 'selected' : '' }}>Yellow</option>
                                    <option value="indigo" {{ old('color', $expenseCategory->color) === 'indigo' ? 'selected' : '' }}>Indigo</option>
                                    <option value="pink" {{ old('color', $expenseCategory->color) === 'pink' ? 'selected' : '' }}>Pink</option>
                                    <option value="gray" {{ old('color', $expenseCategory->color) === 'gray' ? 'selected' : '' }}>Gray</option>
                                </select>
                                @error('color')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Settings Row -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <!-- Tax Deductible -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Tax Deductible
                                </label>
                                <div class="flex items-center">
                                    <input type="hidden" name="is_tax_deductible" value="0">
                                    <input type="checkbox" 
                                           id="is_tax_deductible" 
                                           name="is_tax_deductible" 
                                           value="1"
                                           {{ old('is_tax_deductible', $expenseCategory->is_tax_deductible) ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <label for="is_tax_deductible" class="ml-2 text-sm text-gray-600">
                                        Expenses in this category are tax deductible
                                    </label>
                                </div>
                            </div>

                            <!-- Active Status -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Status
                                </label>
                                <div class="flex items-center">
                                    <input type="hidden" name="is_active" value="0">
                                    <input type="checkbox" 
                                           id="is_active" 
                                           name="is_active" 
                                           value="1"
                                           {{ old('is_active', $expenseCategory->is_active) ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <label for="is_active" class="ml-2 text-sm text-gray-600">
                                        Category is active
                                    </label>
                                </div>
                            </div>

                            <!-- Sort Order -->
                            <div>
                                <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">
                                    Sort Order
                                </label>
                                <input type="number" 
                                       id="sort_order" 
                                       name="sort_order" 
                                       value="{{ old('sort_order', $expenseCategory->sort_order) }}"
                                       min="0"
                                       max="999"
                                       class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('sort_order') border-red-300 @enderror"
                                       placeholder="Auto-assigned if empty">
                                <p class="mt-1 text-sm text-gray-500">
                                    Lower numbers appear first
                                </p>
                                @error('sort_order')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Current Usage Warning -->
                        @if($expenseCategory->expenses_count > 0)
                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-yellow-800">
                                            Category In Use
                                        </h3>
                                        <div class="mt-2 text-sm text-yellow-700">
                                            <p>
                                                This category is currently used by {{ $expenseCategory->expenses_count }} expense(s). 
                                                Changes to this category will affect all existing expenses using this category.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <!-- Preview -->
                        <div class="border-t pt-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Preview
                            </label>
                            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                <div class="p-2 bg-{{ $expenseCategory->color }}-100 rounded-lg mr-3" id="preview-container">
                                    <i class="fas {{ $expenseCategory->icon }} text-{{ $expenseCategory->color }}-600" id="preview-icon"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900" id="preview-name">{{ $expenseCategory->name }}</div>
                                    <div class="text-sm text-gray-500" id="preview-description">{{ $expenseCategory->description ?: 'Category description will appear here' }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex items-center justify-end space-x-4 pt-6 border-t">
                            <a href="{{ route('admin.expense-categories.show', $expenseCategory) }}" 
                               class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg transition-colors duration-200">
                                Cancel
                            </a>
                            <button type="submit" 
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors duration-200 flex items-center">
                                <i class="fas fa-save mr-2"></i>
                                Update Category
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        // Live preview functionality
        function updatePreview() {
            const name = document.getElementById('name').value || '{{ $expenseCategory->name }}';
            const description = document.getElementById('description').value || 'Category description will appear here';
            const icon = document.getElementById('icon').value || '{{ $expenseCategory->icon }}';
            const color = document.getElementById('color').value || '{{ $expenseCategory->color }}';

            // Update preview
            document.getElementById('preview-name').textContent = name;
            document.getElementById('preview-description').textContent = description;
            document.getElementById('preview-icon').className = `fas ${icon} text-${color}-600`;
            document.getElementById('preview-container').className = `p-2 bg-${color}-100 rounded-lg mr-3`;
            
            // Update icon preview in input
            document.getElementById('icon-preview').className = `fas ${icon} text-gray-400`;
        }

        // Add event listeners
        document.getElementById('name').addEventListener('input', updatePreview);
        document.getElementById('description').addEventListener('input', updatePreview);
        document.getElementById('icon').addEventListener('input', updatePreview);
        document.getElementById('color').addEventListener('change', updatePreview);

        // Initial preview update
        updatePreview();
    </script>
    @endpush
</x-app-layout>
