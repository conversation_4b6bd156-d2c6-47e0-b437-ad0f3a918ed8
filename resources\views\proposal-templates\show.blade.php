<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    {{ $template->name }}
                </h2>
                <p class="text-sm text-gray-600">
                    @if($template->is_system_template)
                        System Template
                    @else
                        Custom Template
                    @endif
                    @if($template->category)
                        • {{ $categories[$template->category] ?? ucfirst($template->category) }}
                    @endif
                </p>
            </div>
            <div class="flex space-x-2">
                @if(!$template->is_system_template && $template->user_id === auth()->id())
                    <a href="{{ route('proposal-templates.edit', $template) }}" 
                       class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        <i class="fas fa-edit mr-2"></i>Edit
                    </a>
                @endif
                
                <a href="{{ route('proposals.create', ['template_id' => $template->id]) }}" 
                   class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    <i class="fas fa-plus mr-2"></i>Use Template
                </a>
                
                <a href="{{ route('proposal-templates.index') }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    <i class="fas fa-arrow-left mr-2"></i>Back
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <!-- Template Details -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Template Information</h3>
                            <dl class="space-y-3">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                                    <dd class="mt-1">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                            {{ $template->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ $template->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Type</dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        @if($template->is_system_template)
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                                System Template
                                            </span>
                                        @else
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                                Custom Template
                                            </span>
                                        @endif
                                    </dd>
                                </div>
                                @if($template->category)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Category</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $categories[$template->category] ?? ucfirst($template->category) }}</dd>
                                    </div>
                                @endif
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Usage Count</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $template->usage_count }} times</dd>
                                </div>
                            </dl>
                        </div>

                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Created By</h3>
                            @if($template->user)
                                <dl class="space-y-3">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Name</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $template->user->name }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Email</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $template->user->email }}</dd>
                                    </div>
                                </dl>
                            @else
                                <p class="text-gray-500">System Template</p>
                            @endif
                        </div>

                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Timeline</h3>
                            <dl class="space-y-3">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Created</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $template->created_at->format('M d, Y g:i A') }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $template->updated_at->format('M d, Y g:i A') }}</dd>
                                </div>
                                @if($template->last_used_at)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Last Used</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $template->last_used_at->format('M d, Y g:i A') }}</dd>
                                    </div>
                                @endif
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            @if($template->description)
                <!-- Description -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Description</h3>
                        <p class="text-gray-700">{{ $template->description }}</p>
                    </div>
                </div>
            @endif

            @if($template->tags && count($template->tags) > 0)
                <!-- Tags -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Tags</h3>
                        <div class="flex flex-wrap gap-2">
                            @foreach($template->tags as $tag)
                                <span class="inline-flex px-3 py-1 text-sm rounded-full bg-blue-100 text-blue-800">
                                    {{ $tag }}
                                </span>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif

            <!-- Template Content -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Template Content</h3>
                        <div class="text-sm text-gray-500">
                            <i class="fas fa-info-circle mr-1"></i>
                            Variables will be replaced when creating proposals
                        </div>
                    </div>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <pre class="whitespace-pre-wrap text-sm text-gray-700 font-mono">{{ $template->content }}</pre>
                    </div>
                    
                    <!-- Available Variables -->
                    <div class="mt-4 p-4 bg-blue-50 rounded-lg">
                        <h4 class="text-sm font-medium text-blue-900 mb-2">Available Variables:</h4>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-blue-700">
                            <code>{client_name}</code>
                            <code>{project_name}</code>
                            <code>{amount}</code>
                            <code>{date}</code>
                            <code>{company_name}</code>
                            <code>{user_name}</code>
                            <code>{user_email}</code>
                            <code>{proposal_number}</code>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
