<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\UserSubscription;
use App\Models\Payment;
use App\Models\Plan;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SubscriptionManagementController extends Controller
{
    /**
     * Display subscription management dashboard.
     */
    public function index(Request $request)
    {
        $status = $request->get('status', 'all');
        $plan = $request->get('plan', 'all');
        $search = $request->get('search');

        $query = UserSubscription::with(['user', 'plan', 'payments']);

        // Filter by status
        if ($status !== 'all') {
            $query->where('status', $status);
        }

        // Filter by plan
        if ($plan !== 'all') {
            $query->where('plan_id', $plan);
        }

        // Search by user name or email
        if ($search) {
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $subscriptions = $query->latest()->paginate(20);

        // Get statistics
        $stats = $this->getSubscriptionStats();
        
        // Get plans for filter
        $plans = Plan::limit(20)->get();

        return view('admin.subscriptions.index', compact(
            'subscriptions', 
            'stats', 
            'plans', 
            'status', 
            'plan', 
            'search'
        ));
    }

    /**
     * Show subscription details.
     */
    public function show(UserSubscription $subscription)
    {
        $subscription->load(['user', 'plan', 'payments']);
        
        return view('admin.subscriptions.show', compact('subscription'));
    }

    /**
     * Cancel a subscription.
     */
    public function cancel(UserSubscription $subscription)
    {
        if ($subscription->status === 'cancelled') {
            return back()->with('error', 'Subscription is already cancelled.');
        }

        $subscription->cancel();

        return back()->with('success', 'Subscription cancelled successfully.');
    }

    /**
     * Reactivate a subscription.
     */
    public function reactivate(UserSubscription $subscription)
    {
        if ($subscription->status === 'active') {
            return back()->with('error', 'Subscription is already active.');
        }

        $subscription->update([
            'status' => 'active',
            'cancelled_at' => null,
        ]);

        return back()->with('success', 'Subscription reactivated successfully.');
    }

    /**
     * Change subscription plan.
     */
    public function changePlan(Request $request, UserSubscription $subscription)
    {
        $request->validate([
            'plan_id' => 'required|exists:plans,id',
        ]);

        $newPlan = Plan::findOrFail($request->plan_id);

        if ($subscription->plan_id === $newPlan->id) {
            return back()->with('error', 'User is already on this plan.');
        }

        DB::transaction(function () use ($subscription, $newPlan) {
            // Update subscription
            $subscription->update([
                'plan_id' => $newPlan->id,
            ]);

            // Update user's current plan
            $subscription->user->update([
                'current_plan_id' => $newPlan->id,
            ]);

            // Reset usage if needed
            $subscription->resetUsage();
        });

        return back()->with('success', 'Plan changed successfully.');
    }

    /**
     * Show payments for a subscription.
     */
    public function payments(UserSubscription $subscription)
    {
        $payments = $subscription->payments()->latest()->paginate(15);
        
        return view('admin.subscriptions.payments', compact('subscription', 'payments'));
    }

    /**
     * Get subscription statistics.
     */
    private function getSubscriptionStats(): array
    {
        $totalSubscriptions = UserSubscription::count();
        $activeSubscriptions = UserSubscription::where('status', 'active')->count();
        $cancelledSubscriptions = UserSubscription::where('status', 'cancelled')->count();
        $pendingSubscriptions = UserSubscription::where('status', 'pending')->count();

        $monthlyRevenue = Payment::where('status', 'completed')
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('amount');

        $totalRevenue = Payment::where('status', 'completed')->sum('amount');

        // Plan distribution
        $planDistribution = UserSubscription::where('status', 'active')
            ->with('plan')
            ->get()
            ->groupBy('plan.name')
            ->map(function ($subscriptions) {
                return $subscriptions->count();
            });

        return [
            'total_subscriptions' => $totalSubscriptions,
            'active_subscriptions' => $activeSubscriptions,
            'cancelled_subscriptions' => $cancelledSubscriptions,
            'pending_subscriptions' => $pendingSubscriptions,
            'monthly_revenue' => $monthlyRevenue,
            'total_revenue' => $totalRevenue,
            'plan_distribution' => $planDistribution,
        ];
    }

    /**
     * Export subscriptions data.
     */
    public function export(Request $request)
    {
        $subscriptions = UserSubscription::with(['user', 'plan'])
            ->get()
            ->map(function ($subscription) {
                return [
                    'User Name' => $subscription->user->name,
                    'Email' => $subscription->user->email,
                    'Plan' => $subscription->plan->name,
                    'Status' => $subscription->status,
                    'Amount Paid' => $subscription->amount_paid,
                    'Currency' => $subscription->currency,
                    'Started At' => $subscription->starts_at?->format('Y-m-d H:i:s'),
                    'Ends At' => $subscription->ends_at?->format('Y-m-d H:i:s'),
                    'Cancelled At' => $subscription->cancelled_at?->format('Y-m-d H:i:s'),
                ];
            });

        $filename = 'subscriptions_' . now()->format('Y_m_d_H_i_s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($subscriptions) {
            $file = fopen('php://output', 'w');
            
            // Add headers
            if ($subscriptions->isNotEmpty()) {
                fputcsv($file, array_keys($subscriptions->first()));
            }
            
            // Add data
            foreach ($subscriptions as $subscription) {
                fputcsv($file, $subscription);
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
