<?php

namespace App\Services;

use App\Repositories\ExpenseRepository;
use App\Repositories\ExpenseCategoryRepository;
use Illuminate\Support\Facades\Response;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;

class ExpenseReportService
{
    protected ExpenseRepository $expenseRepository;
    protected ExpenseCategoryRepository $categoryRepository;

    public function __construct(
        ExpenseRepository $expenseRepository,
        ExpenseCategoryRepository $categoryRepository
    ) {
        $this->expenseRepository = $expenseRepository;
        $this->categoryRepository = $categoryRepository;
    }

    /**
     * Generate comprehensive expense report
     */
    public function generateReport(int $userId, array $filters): array
    {
        $expenses = $this->expenseRepository->getFilteredExpenses($userId, $filters);
        
        // Calculate totals
        $totalAmount = $expenses->sum('amount');
        $totalTax = $expenses->sum('tax_amount');
        $totalExpenses = $expenses->sum(function($expense) {
            return $expense->amount + $expense->tax_amount;
        });
        
        // Group by category
        $categoryBreakdown = $expenses->groupBy('category.name')->map(function($categoryExpenses) {
            return [
                'count' => $categoryExpenses->count(),
                'total_amount' => $categoryExpenses->sum('amount'),
                'total_tax' => $categoryExpenses->sum('tax_amount'),
                'total' => $categoryExpenses->sum(function($expense) {
                    return $expense->amount + $expense->tax_amount;
                }),
                'category' => $categoryExpenses->first()->category,
            ];
        });
        
        // Group by status
        $statusBreakdown = $expenses->groupBy('status')->map(function($statusExpenses) {
            return [
                'count' => $statusExpenses->count(),
                'total' => $statusExpenses->sum(function($expense) {
                    return $expense->amount + $expense->tax_amount;
                }),
            ];
        });
        
        // Tax deductible summary
        $taxDeductibleExpenses = $expenses->filter(function($expense) {
            return $expense->category->is_tax_deductible;
        });
        
        $taxDeductibleTotal = $taxDeductibleExpenses->sum(function($expense) {
            return $expense->amount + $expense->tax_amount;
        });
        
        // Billable summary
        $billableExpenses = $expenses->where('is_billable', true);
        $billableTotal = $billableExpenses->sum(function($expense) {
            return $expense->amount + $expense->tax_amount;
        });
        
        return [
            'expenses' => $expenses,
            'summary' => [
                'total_amount' => $totalAmount,
                'total_tax' => $totalTax,
                'total_expenses' => $totalExpenses,
                'expense_count' => $expenses->count(),
                'tax_deductible_total' => $taxDeductibleTotal,
                'tax_deductible_percentage' => $totalExpenses > 0 ? ($taxDeductibleTotal / $totalExpenses) * 100 : 0,
                'billable_total' => $billableTotal,
                'billable_percentage' => $totalExpenses > 0 ? ($billableTotal / $totalExpenses) * 100 : 0,
            ],
            'category_breakdown' => $categoryBreakdown,
            'status_breakdown' => $statusBreakdown,
            'filters' => $filters,
        ];
    }

    /**
     * Export expense report
     */
    public function exportReport(int $userId, array $filters, string $format = 'pdf')
    {
        $reportData = $this->generateReport($userId, $filters);
        
        if ($format === 'csv') {
            return $this->exportToCsv($reportData);
        }
        
        return $this->exportToPdf($reportData, 'expense-report');
    }

    /**
     * Get tax summary for a specific year
     */
    public function getTaxSummary(int $userId, int $year): array
    {
        $startDate = Carbon::create($year, 1, 1)->startOfDay();
        $endDate = Carbon::create($year, 12, 31)->endOfDay();
        
        $expenses = $this->expenseRepository->getExpensesForPeriod($userId, $startDate, $endDate);
        
        // Group by month and tax deductible status
        $monthlyData = [];
        for ($month = 1; $month <= 12; $month++) {
            $monthExpenses = $expenses->filter(function($expense) use ($month) {
                return $expense->expense_date->month === $month;
            });
            
            $taxDeductible = $monthExpenses->filter(function($expense) {
                return $expense->category->is_tax_deductible;
            })->sum(function($expense) {
                return $expense->amount + $expense->tax_amount;
            });
            
            $nonTaxDeductible = $monthExpenses->filter(function($expense) {
                return !$expense->category->is_tax_deductible;
            })->sum(function($expense) {
                return $expense->amount + $expense->tax_amount;
            });
            
            $monthlyData[Carbon::create($year, $month, 1)->format('M')] = [
                'tax_deductible' => $taxDeductible,
                'non_tax_deductible' => $nonTaxDeductible,
                'total' => $taxDeductible + $nonTaxDeductible,
            ];
        }
        
        // Category-wise tax deductible breakdown
        $categoryTaxBreakdown = $expenses->groupBy('category.name')->map(function($categoryExpenses) {
            $category = $categoryExpenses->first()->category;
            $total = $categoryExpenses->sum(function($expense) {
                return $expense->amount + $expense->tax_amount;
            });
            
            return [
                'category' => $category,
                'total' => $total,
                'is_tax_deductible' => $category->is_tax_deductible,
                'count' => $categoryExpenses->count(),
            ];
        });
        
        return [
            'year' => $year,
            'monthly_data' => $monthlyData,
            'category_breakdown' => $categoryTaxBreakdown,
            'total_tax_deductible' => $expenses->filter(function($expense) {
                return $expense->category->is_tax_deductible;
            })->sum(function($expense) {
                return $expense->amount + $expense->tax_amount;
            }),
            'total_expenses' => $expenses->sum(function($expense) {
                return $expense->amount + $expense->tax_amount;
            }),
        ];
    }

    /**
     * Export tax summary
     */
    public function exportTaxSummary(int $userId, int $year, string $format = 'pdf')
    {
        $taxData = $this->getTaxSummary($userId, $year);
        
        if ($format === 'csv') {
            return $this->exportTaxSummaryToCsv($taxData);
        }
        
        return $this->exportToPdf($taxData, 'tax-summary');
    }

    /**
     * Get monthly comparison data
     */
    public function getMonthlyComparison(int $userId, int $year): array
    {
        $currentYearData = $this->getMonthlyExpenseData($userId, $year);
        $previousYearData = $this->getMonthlyExpenseData($userId, $year - 1);
        
        $comparison = [];
        for ($month = 1; $month <= 12; $month++) {
            $monthName = Carbon::create($year, $month, 1)->format('M');
            $current = $currentYearData[$month] ?? 0;
            $previous = $previousYearData[$month] ?? 0;
            
            $comparison[$monthName] = [
                'current' => $current,
                'previous' => $previous,
                'difference' => $current - $previous,
                'percentage_change' => $previous > 0 ? (($current - $previous) / $previous) * 100 : 0,
            ];
        }
        
        return [
            'year' => $year,
            'comparison' => $comparison,
            'total_current' => array_sum($currentYearData),
            'total_previous' => array_sum($previousYearData),
        ];
    }

    /**
     * Get category analysis
     */
    public function getCategoryAnalysis(int $userId, string $period): array
    {
        $startDate = $this->getPeriodStartDate($period);
        $endDate = now();
        
        $expenses = $this->expenseRepository->getExpensesForPeriod($userId, $startDate, $endDate);
        
        $categoryAnalysis = $expenses->groupBy('category.name')->map(function($categoryExpenses) {
            $category = $categoryExpenses->first()->category;
            $total = $categoryExpenses->sum(function($expense) {
                return $expense->amount + $expense->tax_amount;
            });
            
            return [
                'category' => $category,
                'total' => $total,
                'count' => $categoryExpenses->count(),
                'average' => $categoryExpenses->count() > 0 ? $total / $categoryExpenses->count() : 0,
                'percentage' => 0, // Will be calculated after getting total
            ];
        });
        
        $grandTotal = $categoryAnalysis->sum('total');
        
        // Calculate percentages
        $categoryAnalysis = $categoryAnalysis->map(function($item) use ($grandTotal) {
            $item['percentage'] = $grandTotal > 0 ? ($item['total'] / $grandTotal) * 100 : 0;
            return $item;
        })->sortByDesc('total');
        
        return [
            'period' => $period,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'categories' => $categoryAnalysis,
            'total' => $grandTotal,
        ];
    }

    /**
     * Export to CSV
     */
    private function exportToCsv(array $data): \Symfony\Component\HttpFoundation\StreamedResponse
    {
        $filename = 'expense-report-' . now()->format('Y-m-d') . '.csv';
        
        return Response::streamDownload(function() use ($data) {
            $handle = fopen('php://output', 'w');
            
            // Headers
            fputcsv($handle, [
                'Date', 'Description', 'Category', 'Amount', 'Tax', 'Total', 
                'Status', 'Billable', 'Tax Deductible', 'Client'
            ]);
            
            // Data rows
            foreach ($data['expenses'] as $expense) {
                fputcsv($handle, [
                    $expense->expense_date->format('Y-m-d'),
                    $expense->description,
                    $expense->category->name,
                    $expense->amount,
                    $expense->tax_amount,
                    $expense->amount + $expense->tax_amount,
                    $expense->status,
                    $expense->is_billable ? 'Yes' : 'No',
                    $expense->category->is_tax_deductible ? 'Yes' : 'No',
                    $expense->client ? $expense->client->name : 'N/A',
                ]);
            }
            
            fclose($handle);
        }, $filename, [
            'Content-Type' => 'text/csv',
        ]);
    }

    /**
     * Export to PDF
     */
    private function exportToPdf(array $data, string $template)
    {
        $pdf = Pdf::loadView("expenses.reports.pdf.{$template}", $data);
        $filename = "{$template}-" . now()->format('Y-m-d') . '.pdf';
        
        return $pdf->download($filename);
    }

    /**
     * Get monthly expense data for a specific year
     */
    private function getMonthlyExpenseData(int $userId, int $year): array
    {
        return $this->expenseRepository->getMonthlyTotals($userId, $year);
    }

    /**
     * Get period start date
     */
    private function getPeriodStartDate(string $period): Carbon
    {
        return match($period) {
            'week' => now()->startOfWeek(),
            'month' => now()->startOfMonth(),
            'quarter' => now()->startOfQuarter(),
            'year' => now()->startOfYear(),
            default => now()->startOfMonth(),
        };
    }

    /**
     * Export tax summary to CSV
     */
    private function exportTaxSummaryToCsv(array $data): \Symfony\Component\HttpFoundation\StreamedResponse
    {
        $filename = 'tax-summary-' . $data['year'] . '.csv';
        
        return Response::streamDownload(function() use ($data) {
            $handle = fopen('php://output', 'w');
            
            // Headers
            fputcsv($handle, ['Month', 'Tax Deductible', 'Non-Tax Deductible', 'Total']);
            
            // Monthly data
            foreach ($data['monthly_data'] as $month => $monthData) {
                fputcsv($handle, [
                    $month,
                    $monthData['tax_deductible'],
                    $monthData['non_tax_deductible'],
                    $monthData['total'],
                ]);
            }
            
            fclose($handle);
        }, $filename, [
            'Content-Type' => 'text/csv',
        ]);
    }
}
