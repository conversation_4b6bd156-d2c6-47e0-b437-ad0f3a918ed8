<?php

namespace App\Http\Controllers;

use App\Models\TdsRecord;
use App\Services\PlanChecker;
use App\Services\TdsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\TdsReportExport;

class TdsController extends Controller
{
    protected TdsService $tdsService;

    public function __construct(TdsService $tdsService)
    {
        $this->tdsService = $tdsService;
    }
    /**
     * Display TDS records.
     */
    public function index(Request $request)
    {
        // Check if user can access TDS reports
        if (!PlanChecker::canAccessTdsReports()) {
            return PlanChecker::redirectToUpgrade('tds_reports');
        }

        $tdsRecords = $this->tdsService->getTdsRecordsForUser(Auth::id(), $request);
        $clients = Auth::user()->clients()->get();

        $financialYear = $request->get('financial_year', TdsRecord::getCurrentFinancialYear());
        $summary = $this->tdsService->getTdsSummary(Auth::id(), $financialYear);

        // Get available financial years
        $financialYears = Auth::user()->tdsRecords()
                                   ->select('financial_year')
                                   ->distinct()
                                   ->orderBy('financial_year', 'desc')
                                   ->pluck('financial_year');

        // Calculate certificate statistics
        $certificateStats = $this->calculateCertificateStats(Auth::id(), $financialYear);

        return view('tds.index', compact(
            'tdsRecords',
            'clients',
            'financialYear',
            'financialYears'
        ) + $summary + $certificateStats);
    }

    /**
     * Show TDS record details.
     */
    public function show(TdsRecord $tdsRecord)
    {
        // Check if user can access TDS reports
        if (!PlanChecker::canAccessTdsReports()) {
            return PlanChecker::redirectToUpgrade('tds_reports');
        }

        $this->authorize('view', $tdsRecord);
        $tdsRecord->load(['client', 'invoice', 'user']);

        return view('tds.show', compact('tdsRecord'));
    }

    /**
     * Update TDS certificate number.
     */
    public function updateCertificate(Request $request, TdsRecord $tdsRecord)
    {
        $this->authorize('update', $tdsRecord);

        $validated = $request->validate([
            'tds_certificate_number' => 'required|string|max:255',
        ]);

        $this->tdsService->updateTdsRecord($tdsRecord, $validated);

        return redirect()->back()
                        ->with('success', 'TDS certificate number updated successfully.');
    }

    /**
     * Export TDS report.
     */
    public function export(Request $request)
    {
        // Check if user can access TDS reports
        if (!PlanChecker::canAccessTdsReports()) {
            return PlanChecker::redirectToUpgrade('tds_reports');
        }

        $financialYear = $request->get('financial_year', TdsRecord::getCurrentFinancialYear());
        $clientId = $request->get('client_id');

        return Excel::download(
            new TdsReportExport($financialYear, $clientId),
            "TDS_Report_{$financialYear}.xlsx"
        );
    }

    /**
     * Get TDS summary by client.
     */
    public function summary(Request $request)
    {
        // Check if user can access TDS reports
        if (!PlanChecker::canAccessTdsReports()) {
            return PlanChecker::redirectToUpgrade('tds_reports');
        }

        $financialYear = $request->get('financial_year', TdsRecord::getCurrentFinancialYear());
        $summary = $this->tdsService->getTdsSummary(Auth::id(), $financialYear);

        // Get clients and financial years for filter dropdowns
        $clients = Auth::user()->clients()->get();
        $financialYears = Auth::user()->tdsRecords()
                                   ->select('financial_year')
                                   ->distinct()
                                   ->orderBy('financial_year', 'desc')
                                   ->pluck('financial_year');

        // Calculate certificate statistics for overall summary
        $certificateStats = $this->calculateCertificateStats(Auth::id(), $financialYear);

        // Create overall summary for the view
        $overallSummary = [
            'total_tds' => $summary['total_tds_amount'] ?? 0,
            'total_invoices' => $summary['total_records'] ?? 0,
            'with_certificates' => $certificateStats['withCertificates'],
            'pending_certificates' => $certificateStats['pendingCertificates'],
        ];

        // Get year-wise summary for all financial years
        $yearwiseSummary = collect();
        foreach ($financialYears as $year) {
            $yearSummary = $this->tdsService->getTdsSummary(Auth::id(), $year);
            $yearCertificateStats = $this->calculateCertificateStats(Auth::id(), $year);

            $yearwiseSummary->push((object) [
                'financial_year' => $year,
                'total_tds' => $yearSummary['total_tds_amount'] ?? 0,
                'invoice_count' => $yearSummary['total_records'] ?? 0,
                'with_certificates' => $yearCertificateStats['withCertificates'],
                'pending_certificates' => $yearCertificateStats['pendingCertificates'],
            ]);
        }

        // Get client-wise summary from the main summary data
        $clientwiseSummary = collect($summary['client_wise_summary'] ?? [])->map(function ($clientData) {
            // Get actual TDS records for this client to calculate certificate stats
            $clientTdsRecords = Auth::user()->tdsRecords()
                ->where('client_id', $clientData['client']->id)
                ->forFinancialYear($clientData['financial_year'])
                ->get();

            return (object) [
                'client_id' => $clientData['client']->id ?? 0,
                'client_name' => $clientData['client']->name ?? 'Unknown',
                'company_name' => $clientData['client']->company_name ?? null,
                'total_tds' => $clientData['total_tds_amount'] ?? 0,
                'invoice_count' => $clientData['records_count'] ?? 0,
                'avg_tds_percentage' => $clientTdsRecords->avg('tds_percentage') ?? 0,
                'with_certificates' => $clientTdsRecords->whereNotNull('tds_certificate_number')->count(),
                'pending_certificates' => $clientTdsRecords->whereNull('tds_certificate_number')->count(),
            ];
        });

        // Get TDS percentage distribution
        $tdsPercentageDistribution = Auth::user()->tdsRecords()
            ->when($financialYear, fn($q) => $q->where('financial_year', $financialYear))
            ->selectRaw('tds_percentage, COUNT(*) as count, SUM(tds_amount) as total_amount')
            ->groupBy('tds_percentage')
            ->orderBy('tds_percentage')
            ->get();

        // Get monthly trend - simplified approach to avoid SQL syntax issues
        $monthlyTrend = Auth::user()->tdsRecords()
            ->when($financialYear, fn($q) => $q->where('financial_year', $financialYear))
            ->get()
            ->groupBy(function($record) {
                return $record->deduction_date->format('Y-m');
            })
            ->map(function($records, $yearMonth) {
                return (object) [
                    'year_month' => $yearMonth,
                    'total_tds' => $records->sum('tds_amount'),
                    'invoice_count' => $records->count(),
                    'avg_tds' => $records->avg('tds_amount'),
                ];
            })
            ->sortBy('year_month')
            ->values();

        return view('tds.summary', compact(
            'summary',
            'financialYear',
            'clients',
            'financialYears',
            'overallSummary',
            'yearwiseSummary',
            'clientwiseSummary',
            'tdsPercentageDistribution',
            'monthlyTrend'
        ));
    }

    /**
     * Calculate certificate statistics for the given user and financial year
     */
    private function calculateCertificateStats(int $userId, string $financialYear): array
    {
        $query = TdsRecord::where('user_id', $userId)
                         ->forFinancialYear($financialYear);

        $withCertificates = (clone $query)->whereNotNull('tds_certificate_number')->count();
        $pendingCertificates = (clone $query)->whereNull('tds_certificate_number')->count();

        return [
            'withCertificates' => $withCertificates,
            'pendingCertificates' => $pendingCertificates,
        ];
    }
}
