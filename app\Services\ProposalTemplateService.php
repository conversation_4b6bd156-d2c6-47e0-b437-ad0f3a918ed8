<?php

namespace App\Services;

use App\Models\ProposalTemplate;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Auth;

class ProposalTemplateService
{
    /**
     * Get templates available to user.
     */
    public function getTemplatesForUser(int $userId, Request $request = null): LengthAwarePaginator
    {
        $query = ProposalTemplate::availableToUser($userId)
            ->orderBy('is_system_template', 'desc')
            ->orderBy('usage_count', 'desc')
            ->orderBy('name');

        if ($request) {
            // Search filter
            if ($request->filled('search')) {
                $search = $request->get('search');
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%")
                      ->orWhere('category', 'like', "%{$search}%");
                });
            }

            // Category filter
            if ($request->filled('category')) {
                $query->where('category', $request->get('category'));
            }

            // Type filter
            if ($request->filled('type')) {
                if ($request->get('type') === 'system') {
                    $query->where('is_system_template', true);
                } elseif ($request->get('type') === 'user') {
                    $query->where('user_id', $userId)->where('is_system_template', false);
                }
            }
        }

        return $query->paginate(12);
    }

    /**
     * Create a new template.
     */
    public function createTemplate(array $data): ProposalTemplate
    {
        $data['user_id'] = Auth::id();
        $data['is_system_template'] = false;
        
        return ProposalTemplate::create($data);
    }

    /**
     * Update template.
     */
    public function updateTemplate(ProposalTemplate $template, array $data): ProposalTemplate
    {
        // Only allow updating user's own templates (not system templates)
        if ($template->is_system_template || $template->user_id !== Auth::id()) {
            throw new \Exception('You can only edit your own templates.');
        }

        $template->update($data);
        return $template->fresh();
    }

    /**
     * Delete template.
     */
    public function deleteTemplate(ProposalTemplate $template): array
    {
        try {
            // Only allow deleting user's own templates (not system templates)
            if ($template->is_system_template || $template->user_id !== Auth::id()) {
                return [
                    'success' => false,
                    'message' => 'You can only delete your own templates.'
                ];
            }

            // Check if template is being used
            if ($template->proposals()->exists()) {
                return [
                    'success' => false,
                    'message' => 'Cannot delete template that is being used by proposals.'
                ];
            }

            $template->delete();
            
            return [
                'success' => true,
                'message' => 'Template deleted successfully.'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error deleting template: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get template categories.
     */
    public function getCategories(): array
    {
        return [
            'web_development' => 'Web Development',
            'mobile_development' => 'Mobile Development',
            'design' => 'Design & UI/UX',
            'content_writing' => 'Content Writing',
            'marketing' => 'Digital Marketing',
            'consulting' => 'Consulting',
            'maintenance' => 'Maintenance & Support',
            'other' => 'Other Services'
        ];
    }

    /**
     * Get system templates.
     */
    public function getSystemTemplates(): array
    {
        return ProposalTemplate::system()->active()->get()->toArray();
    }

    /**
     * Create default system templates.
     */
    public function createDefaultTemplates(): void
    {
        $templates = [
            [
                'name' => 'Web Development Project',
                'description' => 'Standard template for web development projects',
                'category' => 'web_development',
                'content' => $this->getWebDevelopmentTemplate(),
                'sections' => [
                    'introduction',
                    'project_overview',
                    'scope_of_work',
                    'timeline',
                    'pricing',
                    'terms_and_conditions'
                ],
                'variables' => [
                    'client_name',
                    'project_name',
                    'project_description',
                    'total_cost',
                    'timeline_weeks',
                    'payment_terms'
                ],
                'is_system_template' => true,
                'tags' => ['web', 'development', 'website', 'responsive']
            ],
            [
                'name' => 'Mobile App Development',
                'description' => 'Template for mobile application development proposals',
                'category' => 'mobile_development',
                'content' => $this->getMobileAppTemplate(),
                'sections' => [
                    'introduction',
                    'app_overview',
                    'features',
                    'platforms',
                    'development_process',
                    'pricing',
                    'timeline'
                ],
                'variables' => [
                    'client_name',
                    'app_name',
                    'app_description',
                    'platforms',
                    'total_cost',
                    'development_weeks'
                ],
                'is_system_template' => true,
                'tags' => ['mobile', 'app', 'ios', 'android']
            ],
            [
                'name' => 'UI/UX Design Project',
                'description' => 'Template for design and user experience projects',
                'category' => 'design',
                'content' => $this->getDesignTemplate(),
                'sections' => [
                    'introduction',
                    'design_approach',
                    'deliverables',
                    'process',
                    'timeline',
                    'investment'
                ],
                'variables' => [
                    'client_name',
                    'project_name',
                    'design_scope',
                    'total_cost',
                    'timeline_weeks'
                ],
                'is_system_template' => true,
                'tags' => ['design', 'ui', 'ux', 'wireframes', 'prototypes']
            ]
        ];

        foreach ($templates as $template) {
            ProposalTemplate::updateOrCreate(
                ['name' => $template['name'], 'is_system_template' => true],
                $template
            );
        }
    }

    /**
     * Get web development template content.
     */
    private function getWebDevelopmentTemplate(): string
    {
        return "# Web Development Proposal

## Introduction
Dear {client_name},

Thank you for considering our services for your {project_name} project. We're excited about the opportunity to work with you and bring your vision to life.

## Project Overview
{project_description}

## Scope of Work
- Custom website design and development
- Responsive design for all devices
- Content management system integration
- SEO optimization
- Performance optimization
- Cross-browser compatibility testing

## Timeline
The project will be completed in approximately {timeline_weeks} weeks from the start date.

## Investment
Total project cost: {total_cost}

## Payment Terms
{payment_terms}

## Next Steps
Upon acceptance of this proposal, we'll schedule a kickoff meeting to discuss project details and begin development.

We look forward to working with you!";
    }

    /**
     * Get mobile app template content.
     */
    private function getMobileAppTemplate(): string
    {
        return "# Mobile App Development Proposal

## Introduction
Dear {client_name},

We're thrilled to present our proposal for developing {app_name}. Our team specializes in creating high-quality mobile applications that deliver exceptional user experiences.

## App Overview
{app_description}

## Target Platforms
{platforms}

## Development Process
1. Requirements Analysis & Planning
2. UI/UX Design
3. Development & Testing
4. Quality Assurance
5. App Store Deployment
6. Post-launch Support

## Timeline
Development will take approximately {development_weeks} weeks.

## Investment
Total development cost: {total_cost}

## What's Included
- Native app development
- UI/UX design
- Testing and quality assurance
- App store submission
- 30 days post-launch support

Let's build something amazing together!";
    }

    /**
     * Get design template content.
     */
    private function getDesignTemplate(): string
    {
        return "# UI/UX Design Proposal

## Introduction
Hello {client_name},

We're excited to help you create an outstanding user experience for {project_name}. Our design approach focuses on user-centered design principles to deliver intuitive and engaging interfaces.

## Design Approach
{design_scope}

## Deliverables
- User research and personas
- Information architecture
- Wireframes and prototypes
- Visual design mockups
- Design system and style guide
- Developer handoff documentation

## Design Process
1. Discovery & Research
2. Information Architecture
3. Wireframing & Prototyping
4. Visual Design
5. Testing & Iteration
6. Final Delivery

## Timeline
The design process will take {timeline_weeks} weeks to complete.

## Investment
Total design cost: {total_cost}

Ready to create something beautiful and functional together!";
    }
}
