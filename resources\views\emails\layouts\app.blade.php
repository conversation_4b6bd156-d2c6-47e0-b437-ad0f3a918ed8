<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>{{ $subject ?? config('app.name') }}</title>
    
    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #334155;
            background-color: #f8fafc;
            margin: 0;
            padding: 0;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        
        table {
            border-collapse: collapse;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        
        img {
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
            -ms-interpolation-mode: bicubic;
        }
        
        /* Container */
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .email-wrapper {
            padding: 40px 20px;
            background-color: #f8fafc;
        }
        
        /* Header */
        .email-header {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            padding: 32px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .email-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="60" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="70" cy="30" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            opacity: 0.3;
        }
        
        .email-header .logo {
            position: relative;
            z-index: 2;
        }
        
        .email-header h1 {
            color: #ffffff;
            font-size: 28px;
            font-weight: 700;
            margin: 16px 0 8px 0;
            position: relative;
            z-index: 2;
        }
        
        .email-header p {
            color: #d1fae5;
            font-size: 16px;
            margin: 0;
            position: relative;
            z-index: 2;
        }
        
        /* Content */
        .email-content {
            padding: 40px;
        }
        
        .email-content h2 {
            color: #1e293b;
            font-size: 24px;
            font-weight: 600;
            margin: 0 0 16px 0;
            line-height: 1.3;
        }
        
        .email-content h3 {
            color: #334155;
            font-size: 18px;
            font-weight: 600;
            margin: 24px 0 12px 0;
        }
        
        .email-content p {
            color: #64748b;
            font-size: 16px;
            margin: 0 0 16px 0;
            line-height: 1.6;
        }
        
        .email-content .lead {
            font-size: 18px;
            color: #475569;
            margin-bottom: 24px;
        }
        
        /* Buttons */
        .btn {
            display: inline-block;
            padding: 16px 32px;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            border-radius: 12px;
            text-align: center;
            transition: all 0.2s ease;
            margin: 8px 4px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: #ffffff;
            box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3);
        }
        
        .btn-secondary {
            background-color: #f1f5f9;
            color: #475569;
            border: 2px solid #e2e8f0;
        }
        
        .btn-outline {
            background-color: transparent;
            color: #10b981;
            border: 2px solid #10b981;
        }
        
        /* Cards and Boxes */
        .info-box {
            background-color: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 12px;
            padding: 24px;
            margin: 24px 0;
        }
        
        .success-box {
            background-color: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 12px;
            padding: 24px;
            margin: 24px 0;
        }
        
        .warning-box {
            background-color: #fffbeb;
            border: 1px solid #fde68a;
            border-radius: 12px;
            padding: 24px;
            margin: 24px 0;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 24px;
            margin: 24px 0;
        }
        
        /* Lists */
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 24px 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            position: relative;
            padding-left: 32px;
        }
        
        .feature-list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            top: 8px;
            color: #10b981;
            font-weight: bold;
            font-size: 16px;
        }
        
        /* Footer */
        .email-footer {
            background-color: #f8fafc;
            padding: 32px 40px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }
        
        .email-footer p {
            color: #64748b;
            font-size: 14px;
            margin: 8px 0;
        }
        
        .email-footer .social-links {
            margin: 16px 0;
        }
        
        .email-footer .social-links a {
            display: inline-block;
            margin: 0 8px;
            color: #64748b;
            text-decoration: none;
        }
        
        .email-footer .unsubscribe {
            margin-top: 24px;
            padding-top: 24px;
            border-top: 1px solid #e2e8f0;
        }
        
        .email-footer .unsubscribe a {
            color: #64748b;
            text-decoration: underline;
        }
        
        /* Responsive */
        @media only screen and (max-width: 600px) {
            .email-wrapper {
                padding: 20px 10px;
            }
            
            .email-container {
                border-radius: 0;
            }
            
            .email-header {
                padding: 24px 20px;
            }
            
            .email-header h1 {
                font-size: 24px;
            }
            
            .email-content {
                padding: 24px 20px;
            }
            
            .email-footer {
                padding: 24px 20px;
            }
            
            .btn {
                display: block;
                width: 100%;
                margin: 8px 0;
            }
        }
        
        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            .email-container {
                background-color: #1e293b;
            }
            
            .email-content {
                color: #e2e8f0;
            }
            
            .email-content h2,
            .email-content h3 {
                color: #f1f5f9;
            }
            
            .email-content p {
                color: #cbd5e1;
            }
        }
    </style>
</head>
<body>
    <div class="email-wrapper">
        <div class="email-container">
            <!-- Header -->
            <div class="email-header">
                <div class="logo">
                    <img src="{{ asset('images/logo-white.png') }}" alt="{{ config('app.name') }}" style="height: 40px; width: auto;">
                </div>
                @isset($header_title)
                    <h1>{{ $header_title }}</h1>
                @endisset
                @isset($header_subtitle)
                    <p>{{ $header_subtitle }}</p>
                @endisset
            </div>
            
            <!-- Content -->
            <div class="email-content">
                @yield('content')
            </div>
            
            <!-- Footer -->
            <div class="email-footer">
                <p><strong>{{ config('app.name') }}</strong></p>
                <p>Professional Business Management Platform</p>
                
                <div class="social-links">
                    <a href="#" style="margin: 0 8px;">Twitter</a>
                    <a href="#" style="margin: 0 8px;">LinkedIn</a>
                    <a href="#" style="margin: 0 8px;">Facebook</a>
                </div>
                
                <p style="margin-top: 16px;">
                    <a href="{{ route('dashboard') }}" style="color: #10b981; text-decoration: none;">Visit Dashboard</a> |
                    <a href="#" style="color: #64748b; text-decoration: none;">Help Center</a> |
                    <a href="#" style="color: #64748b; text-decoration: none;">Contact Support</a>
                </p>
                
                <div class="unsubscribe">
                    <p>
                        You're receiving this email because you have an account with {{ config('app.name') }}.
                        <br>
                        <a href="#">Manage email preferences</a> | <a href="#">Unsubscribe</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
