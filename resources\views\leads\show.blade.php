<x-app-layout>
    <div class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-6">
                <div class="flex items-center">
                    <a href="{{ route('leads.index') }}" class="text-gray-500 hover:text-gray-700 mr-4">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <div class="flex-1">
                        <h2 class="text-2xl font-bold leading-7 text-gray-900">
                            {{ $lead->name }}
                        </h2>
                        <p class="mt-1 text-sm text-gray-500">
                            Lead Details and Activity Timeline
                        </p>
                    </div>
                    <div class="flex space-x-3">
                        @if($lead->status === 'active')
                            <form action="{{ route('leads.convert', $lead) }}" method="POST" class="inline">
                                @csrf
                                <button type="submit" 
                                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
                                        onclick="return confirm('Convert this lead to a client?')">
                                    <i class="fas fa-user-check mr-2"></i>
                                    Convert to Client
                                </button>
                            </form>
                        @endif
                        <a href="{{ route('leads.edit', $lead) }}" 
                           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-edit mr-2"></i>
                            Edit
                        </a>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Lead Information -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Basic Info -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">Lead Information</h3>
                        </div>
                        <div class="px-6 py-4">
                            <dl class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Name</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $lead->name }}</dd>
                                </div>
                                @if($lead->email)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Email</dt>
                                        <dd class="mt-1 text-sm text-gray-900">
                                            <a href="mailto:{{ $lead->email }}" class="text-blue-600 hover:text-blue-800">
                                                {{ $lead->email }}
                                            </a>
                                        </dd>
                                    </div>
                                @endif
                                @if($lead->phone)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Phone</dt>
                                        <dd class="mt-1 text-sm text-gray-900">
                                            <a href="tel:{{ $lead->phone }}" class="text-blue-600 hover:text-blue-800">
                                                {{ $lead->phone }}
                                            </a>
                                        </dd>
                                    </div>
                                @endif
                                @if($lead->company_name)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Company</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $lead->company_name }}</dd>
                                    </div>
                                @endif
                                @if($lead->title)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Job Title</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $lead->title }}</dd>
                                    </div>
                                @endif
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Source</dt>
                                    <dd class="mt-1">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                              style="background-color: {{ $lead->leadSource->color }}20; color: {{ $lead->leadSource->color }}">
                                            {{ $lead->leadSource->name }}
                                        </span>
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Stage</dt>
                                    <dd class="mt-1">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                              style="background-color: {{ $lead->leadStage->color }}20; color: {{ $lead->leadStage->color }}">
                                            {{ $lead->leadStage->name }}
                                        </span>
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Priority</dt>
                                    <dd class="mt-1">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                              style="background-color: {{ $lead->priority_color }}20; color: {{ $lead->priority_color }}">
                                            {{ ucfirst($lead->priority) }}
                                        </span>
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Lead Score</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $lead->lead_score }}/100</dd>
                                </div>
                                @if($lead->estimated_value)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Estimated Value</dt>
                                        <dd class="mt-1 text-sm text-gray-900">${{ number_format($lead->estimated_value, 2) }}</dd>
                                    </div>
                                @endif
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                                    <dd class="mt-1">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                                     {{ $lead->status === 'active' ? 'bg-green-100 text-green-800' : 
                                                        ($lead->status === 'converted' ? 'bg-blue-100 text-blue-800' : 'bg-red-100 text-red-800') }}">
                                            {{ ucfirst($lead->status) }}
                                        </span>
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Created</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $lead->created_at->format('M j, Y g:i A') }}</dd>
                                </div>
                            </dl>

                            @if($lead->notes)
                                <div class="mt-6">
                                    <dt class="text-sm font-medium text-gray-500">Notes</dt>
                                    <dd class="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{{ $lead->notes }}</dd>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Activities Timeline -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">Activity Timeline</h3>
                        </div>
                        <div class="px-6 py-4">
                            @if($lead->activities->count() > 0)
                                <div class="flow-root">
                                    <ul class="-mb-8">
                                        @foreach($lead->activities->sortByDesc('created_at') as $activity)
                                            <li>
                                                <div class="relative pb-8">
                                                    @if(!$loop->last)
                                                        <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"></span>
                                                    @endif
                                                    <div class="relative flex space-x-3">
                                                        <div>
                                                            <span class="h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white"
                                                                  style="background-color: {{ $activity->type_color }}">
                                                                <i class="{{ $activity->type_icon }} text-white text-xs"></i>
                                                            </span>
                                                        </div>
                                                        <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                                            <div>
                                                                <p class="text-sm text-gray-900">{{ $activity->title }}</p>
                                                                @if($activity->description)
                                                                    <p class="text-sm text-gray-500">{{ $activity->description }}</p>
                                                                @endif
                                                                @if($activity->outcome)
                                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mt-1"
                                                                          style="background-color: {{ $activity->outcome_color }}20; color: {{ $activity->outcome_color }}">
                                                                        {{ ucfirst($activity->outcome) }}
                                                                    </span>
                                                                @endif
                                                            </div>
                                                            <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                                                <time>{{ $activity->created_at->format('M j, g:i A') }}</time>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            @else
                                <div class="text-center py-6 text-gray-500">
                                    <i class="fas fa-history text-2xl mb-2"></i>
                                    <p>No activities recorded yet</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Quick Actions -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
                        </div>
                        <div class="px-6 py-4 space-y-3">
                            @if($lead->status === 'active')
                                <!-- Move to Stage -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Move to Stage</label>
                                    <select onchange="moveToStage(this.value)" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        <option value="">Select Stage</option>
                                        @foreach($stages as $stage)
                                            @if($stage->id !== $lead->lead_stage_id)
                                                <option value="{{ $stage->id }}">{{ $stage->name }}</option>
                                            @endif
                                        @endforeach
                                    </select>
                                </div>

                                <!-- Mark as Lost -->
                                <form action="{{ route('leads.mark-as-lost', $lead) }}" method="POST">
                                    @csrf
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Mark as Lost</label>
                                        <input type="text" name="lost_reason" placeholder="Reason (optional)" 
                                               class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 mb-2">
                                        <button type="submit" 
                                                class="w-full bg-red-600 text-white px-3 py-2 rounded-md hover:bg-red-700 text-sm"
                                                onclick="return confirm('Mark this lead as lost?')">
                                            Mark as Lost
                                        </button>
                                    </div>
                                </form>
                            @endif

                            @if($lead->convertedClient)
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                                    <p class="text-sm text-blue-800 font-medium">Converted to Client</p>
                                    <a href="{{ route('clients.show', $lead->convertedClient) }}" 
                                       class="text-sm text-blue-600 hover:text-blue-800">
                                        View Client Profile →
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Lead Stats -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">Lead Stats</h3>
                        </div>
                        <div class="px-6 py-4 space-y-4">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500">Contact Attempts</span>
                                <span class="text-sm font-medium text-gray-900">{{ $lead->contact_attempts }}</span>
                            </div>
                            @if($lead->last_contacted_at)
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Last Contact</span>
                                    <span class="text-sm font-medium text-gray-900">{{ $lead->last_contacted_at->diffForHumans() }}</span>
                                </div>
                            @endif
                            @if($lead->next_follow_up_at)
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Next Follow-up</span>
                                    <span class="text-sm font-medium {{ $lead->isOverdueForFollowUp() ? 'text-red-600' : 'text-gray-900' }}">
                                        {{ $lead->next_follow_up_at->format('M j, Y') }}
                                    </span>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function moveToStage(stageId) {
            if (!stageId) return;
            
            fetch(`/leads/{{ $lead->id }}/move-to-stage`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    stage_id: stageId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                } else {
                    alert('Error moving lead: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error moving lead. Please try again.');
            });
        }
    </script>
</x-app-layout>
