<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('projects', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('client_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('status', ['planning', 'active', 'completed', 'on_hold', 'cancelled'])->default('planning');
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->date('start_date')->nullable();
            $table->date('due_date')->nullable();
            $table->date('completed_date')->nullable();
            $table->decimal('budget', 10, 2)->nullable();
            $table->decimal('hourly_rate', 8, 2)->nullable();
            $table->enum('billing_type', ['fixed', 'hourly', 'milestone'])->default('hourly');
            $table->boolean('is_billable')->default(true);
            $table->text('notes')->nullable();
            $table->json('custom_fields')->nullable();
            $table->decimal('progress_percentage', 5, 2)->default(0);
            $table->timestamps();

            $table->index(['user_id', 'status']);
            $table->index(['client_id', 'status']);
            $table->index('due_date');
            $table->index('start_date');
            $table->index(['status', 'due_date']);
            $table->index('priority');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('projects');
    }
};
