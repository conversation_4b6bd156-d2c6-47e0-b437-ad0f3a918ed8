<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Create Expense Category') }}
            </h2>
            <a href="{{ route('admin.expense-categories.index') }}" 
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Categories
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-3xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('admin.expense-categories.store') }}" class="space-y-6">
                        @csrf

                        <!-- Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                Category Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" 
                                   id="name" 
                                   name="name" 
                                   value="{{ old('name') }}"
                                   required
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('name') border-red-300 @enderror"
                                   placeholder="e.g., Office Supplies">
                            @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                                Description
                            </label>
                            <textarea id="description" 
                                      name="description" 
                                      rows="3"
                                      class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('description') border-red-300 @enderror"
                                      placeholder="Brief description of this expense category">{{ old('description') }}</textarea>
                            @error('description')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Icon and Color Row -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Icon -->
                            <div>
                                <label for="icon" class="block text-sm font-medium text-gray-700 mb-2">
                                    Icon <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <input type="text" 
                                           id="icon" 
                                           name="icon" 
                                           value="{{ old('icon', 'fa-folder') }}"
                                           required
                                           class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('icon') border-red-300 @enderror pl-10"
                                           placeholder="fa-folder">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-folder text-gray-400" id="icon-preview"></i>
                                    </div>
                                </div>
                                <p class="mt-1 text-sm text-gray-500">
                                    FontAwesome icon class (e.g., fa-folder, fa-paperclip, fa-plane)
                                </p>
                                @error('icon')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Color -->
                            <div>
                                <label for="color" class="block text-sm font-medium text-gray-700 mb-2">
                                    Color <span class="text-red-500">*</span>
                                </label>
                                <select id="color" 
                                        name="color" 
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('color') border-red-300 @enderror">
                                    <option value="">Select Color</option>
                                    <option value="blue" {{ old('color') === 'blue' ? 'selected' : '' }}>Blue</option>
                                    <option value="green" {{ old('color') === 'green' ? 'selected' : '' }}>Green</option>
                                    <option value="purple" {{ old('color') === 'purple' ? 'selected' : '' }}>Purple</option>
                                    <option value="red" {{ old('color') === 'red' ? 'selected' : '' }}>Red</option>
                                    <option value="yellow" {{ old('color') === 'yellow' ? 'selected' : '' }}>Yellow</option>
                                    <option value="indigo" {{ old('color') === 'indigo' ? 'selected' : '' }}>Indigo</option>
                                    <option value="pink" {{ old('color') === 'pink' ? 'selected' : '' }}>Pink</option>
                                    <option value="gray" {{ old('color') === 'gray' ? 'selected' : '' }}>Gray</option>
                                </select>
                                @error('color')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Settings Row -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <!-- Tax Deductible -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Tax Deductible
                                </label>
                                <div class="flex items-center">
                                    <input type="hidden" name="is_tax_deductible" value="0">
                                    <input type="checkbox" 
                                           id="is_tax_deductible" 
                                           name="is_tax_deductible" 
                                           value="1"
                                           {{ old('is_tax_deductible', '1') ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <label for="is_tax_deductible" class="ml-2 text-sm text-gray-600">
                                        Expenses in this category are tax deductible
                                    </label>
                                </div>
                            </div>

                            <!-- Active Status -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Status
                                </label>
                                <div class="flex items-center">
                                    <input type="hidden" name="is_active" value="0">
                                    <input type="checkbox" 
                                           id="is_active" 
                                           name="is_active" 
                                           value="1"
                                           {{ old('is_active', '1') ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <label for="is_active" class="ml-2 text-sm text-gray-600">
                                        Category is active
                                    </label>
                                </div>
                            </div>

                            <!-- Sort Order -->
                            <div>
                                <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">
                                    Sort Order
                                </label>
                                <input type="number" 
                                       id="sort_order" 
                                       name="sort_order" 
                                       value="{{ old('sort_order') }}"
                                       min="0"
                                       max="999"
                                       class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('sort_order') border-red-300 @enderror"
                                       placeholder="Auto-assigned if empty">
                                <p class="mt-1 text-sm text-gray-500">
                                    Lower numbers appear first
                                </p>
                                @error('sort_order')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Preview -->
                        <div class="border-t pt-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Preview
                            </label>
                            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                <div class="p-2 bg-blue-100 rounded-lg mr-3" id="preview-container">
                                    <i class="fas fa-folder text-blue-600" id="preview-icon"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900" id="preview-name">Category Name</div>
                                    <div class="text-sm text-gray-500" id="preview-description">Category description will appear here</div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex items-center justify-end space-x-4 pt-6 border-t">
                            <a href="{{ route('admin.expense-categories.index') }}" 
                               class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg transition-colors duration-200">
                                Cancel
                            </a>
                            <button type="submit" 
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors duration-200 flex items-center">
                                <i class="fas fa-save mr-2"></i>
                                Create Category
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        // Live preview functionality
        function updatePreview() {
            const name = document.getElementById('name').value || 'Category Name';
            const description = document.getElementById('description').value || 'Category description will appear here';
            const icon = document.getElementById('icon').value || 'fa-folder';
            const color = document.getElementById('color').value || 'blue';

            // Update preview
            document.getElementById('preview-name').textContent = name;
            document.getElementById('preview-description').textContent = description;
            document.getElementById('preview-icon').className = `fas ${icon} text-${color}-600`;
            document.getElementById('preview-container').className = `p-2 bg-${color}-100 rounded-lg mr-3`;
            
            // Update icon preview in input
            document.getElementById('icon-preview').className = `fas ${icon} text-gray-400`;
        }

        // Add event listeners
        document.getElementById('name').addEventListener('input', updatePreview);
        document.getElementById('description').addEventListener('input', updatePreview);
        document.getElementById('icon').addEventListener('input', updatePreview);
        document.getElementById('color').addEventListener('change', updatePreview);

        // Initial preview update
        updatePreview();
    </script>
    @endpush
</x-app-layout>
