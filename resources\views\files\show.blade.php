<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    {{ $file->name }}
                </h2>
                <p class="text-sm text-gray-600 mt-1">File Details</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('files.download', $file) }}" 
                   class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                    <i class="fas fa-download mr-2"></i>
                    Download
                </a>
                <a href="{{ route('files.index') }}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Files
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- File Preview -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Preview</h3>
                        
                        <div class="flex justify-center">
                            @if($file->isImage())
                                <img src="{{ $file->url }}" 
                                     alt="{{ $file->name }}" 
                                     class="max-w-full max-h-96 rounded-lg shadow-sm">
                            @else
                                <div class="text-center py-12">
                                    <i class="{{ $file->icon }} text-8xl mb-4"></i>
                                    <p class="text-gray-600">{{ $file->name }}</p>
                                    <p class="text-sm text-gray-500 mt-2">Preview not available for this file type</p>
                                    <a href="{{ route('files.download', $file) }}" 
                                       class="inline-block mt-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                                        <i class="fas fa-download mr-2"></i>
                                        Download to View
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- File Information -->
                <div class="space-y-6">
                    <!-- Basic Info -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">File Information</h3>
                        
                        <dl class="space-y-3">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Name</dt>
                                <dd class="text-sm text-gray-900 break-all">{{ $file->name }}</dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Size</dt>
                                <dd class="text-sm text-gray-900">{{ $file->human_size }}</dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Type</dt>
                                <dd class="text-sm text-gray-900">{{ $file->mime_type }}</dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Extension</dt>
                                <dd class="text-sm text-gray-900 uppercase">{{ $file->extension }}</dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Uploaded</dt>
                                <dd class="text-sm text-gray-900">{{ $file->created_at->format('M j, Y g:i A') }}</dd>
                            </div>
                            
                            @if($file->last_accessed_at)
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Last Accessed</dt>
                                <dd class="text-sm text-gray-900">{{ $file->last_accessed_at->format('M j, Y g:i A') }}</dd>
                            </div>
                            @endif
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Visibility</dt>
                                <dd class="text-sm">
                                    @if($file->is_public)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-globe w-3 h-3 mr-1"></i>
                                            Public
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            <i class="fas fa-lock w-3 h-3 mr-1"></i>
                                            Private
                                        </span>
                                    @endif
                                </dd>
                            </div>
                        </dl>
                    </div>

                    <!-- Description -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Description</h3>
                            <button onclick="editDescription()" class="text-blue-600 hover:text-blue-800 text-sm">
                                <i class="fas fa-edit mr-1"></i>Edit
                            </button>
                        </div>
                        
                        <div id="descriptionDisplay">
                            @if($file->description)
                                <p class="text-sm text-gray-700">{{ $file->description }}</p>
                            @else
                                <p class="text-sm text-gray-500 italic">No description provided</p>
                            @endif
                        </div>

                        <form id="descriptionForm" action="{{ route('files.update', $file) }}" method="POST" class="hidden">
                            @csrf
                            @method('PATCH')
                            <textarea name="description" 
                                      rows="3" 
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent mb-3"
                                      placeholder="Add a description...">{{ $file->description }}</textarea>
                            <div class="flex justify-end space-x-2">
                                <button type="button" 
                                        onclick="cancelEdit()" 
                                        class="px-3 py-1 text-gray-700 bg-gray-200 rounded text-sm hover:bg-gray-300">
                                    Cancel
                                </button>
                                <button type="submit" 
                                        class="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700">
                                    Save
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Actions -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Actions</h3>
                        
                        <div class="space-y-3">
                            <a href="{{ route('files.download', $file) }}" 
                               class="w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200">
                                <i class="fas fa-download mr-2"></i>
                                Download File
                            </a>
                            
                            @if($file->is_public)
                                <button onclick="copyPublicLink()" 
                                        class="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                                    <i class="fas fa-link mr-2"></i>
                                    Copy Public Link
                                </button>
                            @endif
                            
                            <button onclick="deleteFile()" 
                                    class="w-full flex items-center justify-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200">
                                <i class="fas fa-trash mr-2"></i>
                                Delete File
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        function editDescription() {
            document.getElementById('descriptionDisplay').classList.add('hidden');
            document.getElementById('descriptionForm').classList.remove('hidden');
        }

        function cancelEdit() {
            document.getElementById('descriptionDisplay').classList.remove('hidden');
            document.getElementById('descriptionForm').classList.add('hidden');
        }

        function copyPublicLink() {
            const url = '{{ $file->url }}';
            navigator.clipboard.writeText(url).then(function() {
                showToast('Public link copied to clipboard!', 'success');
            }, function() {
                showToast('Failed to copy link to clipboard.', 'error');
            });
        }

        function deleteFile() {
            if (confirm('Are you sure you want to delete this file? This action cannot be undone.')) {
                fetch('{{ route('files.destroy', $file) }}', {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Accept': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast(data.message, 'success');
                        window.location.href = '{{ route('files.index') }}';
                    } else {
                        showToast(data.message, 'error');
                    }
                })
                .catch(error => {
                    showToast('An error occurred while deleting the file.', 'error');
                });
            }
        }
    </script>
    @endpush
</x-app-layout>
