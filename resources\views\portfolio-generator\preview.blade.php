<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Portfolio Preview') }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('portfolio-generator.download') }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    <i class="fas fa-download mr-2"></i>Download
                </a>
                <a href="{{ route('portfolio-generator.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Generator
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-8">
                    <!-- Portfolio Header -->
                    <div class="text-center mb-8">
                        <h1 class="text-4xl font-bold text-gray-900 mb-2">{{ $portfolio['name'] ?? 'Your Name' }}</h1>
                        <p class="text-xl text-gray-600 mb-4">{{ $portfolio['title'] ?? 'Professional Title' }}</p>
                        <div class="flex justify-center space-x-4 text-gray-600">
                            @if(isset($portfolio['email']))
                                <span><i class="fas fa-envelope mr-1"></i>{{ $portfolio['email'] }}</span>
                            @endif
                            @if(isset($portfolio['phone']))
                                <span><i class="fas fa-phone mr-1"></i>{{ $portfolio['phone'] }}</span>
                            @endif
                            @if(isset($portfolio['website']))
                                <span><i class="fas fa-globe mr-1"></i>{{ $portfolio['website'] }}</span>
                            @endif
                        </div>
                    </div>

                    <!-- About Section -->
                    @if(isset($portfolio['about']))
                        <div class="mb-8">
                            <h2 class="text-2xl font-bold text-gray-900 mb-4">About Me</h2>
                            <p class="text-gray-700 leading-relaxed">{{ $portfolio['about'] }}</p>
                        </div>
                    @endif

                    <!-- Skills Section -->
                    @if(isset($portfolio['skills']) && !empty($portfolio['skills']))
                        <div class="mb-8">
                            <h2 class="text-2xl font-bold text-gray-900 mb-4">Skills</h2>
                            <div class="flex flex-wrap gap-2">
                                @foreach($portfolio['skills'] as $skill)
                                    <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">{{ $skill }}</span>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Projects Section -->
                    @if(isset($portfolio['projects']) && !empty($portfolio['projects']))
                        <div class="mb-8">
                            <h2 class="text-2xl font-bold text-gray-900 mb-4">Projects</h2>
                            <div class="space-y-6">
                                @foreach($portfolio['projects'] as $project)
                                    <div class="border border-gray-200 rounded-lg p-6">
                                        <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ $project['name'] }}</h3>
                                        @if(isset($project['client']))
                                            <p class="text-gray-600 mb-2"><strong>Client:</strong> {{ $project['client'] }}</p>
                                        @endif
                                        @if(isset($project['description']))
                                            <p class="text-gray-700 mb-3">{{ $project['description'] }}</p>
                                        @endif
                                        @if(isset($project['technologies']) && !empty($project['technologies']))
                                            <div class="flex flex-wrap gap-1">
                                                @foreach($project['technologies'] as $tech)
                                                    <span class="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">{{ $tech }}</span>
                                                @endforeach
                                            </div>
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Experience Section -->
                    @if(isset($portfolio['experience']) && !empty($portfolio['experience']))
                        <div class="mb-8">
                            <h2 class="text-2xl font-bold text-gray-900 mb-4">Experience</h2>
                            <div class="space-y-4">
                                @foreach($portfolio['experience'] as $exp)
                                    <div class="border-l-4 border-blue-500 pl-4">
                                        <h3 class="text-lg font-semibold text-gray-900">{{ $exp['position'] }}</h3>
                                        <p class="text-gray-600">{{ $exp['company'] }} • {{ $exp['duration'] }}</p>
                                        @if(isset($exp['description']))
                                            <p class="text-gray-700 mt-2">{{ $exp['description'] }}</p>
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Education Section -->
                    @if(isset($portfolio['education']) && !empty($portfolio['education']))
                        <div class="mb-8">
                            <h2 class="text-2xl font-bold text-gray-900 mb-4">Education</h2>
                            <div class="space-y-4">
                                @foreach($portfolio['education'] as $edu)
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900">{{ $edu['degree'] }}</h3>
                                        <p class="text-gray-600">{{ $edu['institution'] }} • {{ $edu['year'] }}</p>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Testimonials Section -->
                    @if(isset($portfolio['testimonials']) && !empty($portfolio['testimonials']))
                        <div class="mb-8">
                            <h2 class="text-2xl font-bold text-gray-900 mb-4">Testimonials</h2>
                            <div class="space-y-4">
                                @foreach($portfolio['testimonials'] as $testimonial)
                                    <div class="bg-gray-50 p-4 rounded-lg">
                                        <p class="text-gray-700 italic mb-2">"{{ $testimonial['content'] }}"</p>
                                        <p class="text-gray-600 text-sm">— {{ $testimonial['client'] }}</p>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
