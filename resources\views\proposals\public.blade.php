<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ $proposal->title }} - Proposal</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="font-sans antialiased bg-gray-50">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">{{ $proposal->title }}</h1>
                        <p class="text-sm text-gray-600">Proposal #{{ $proposal->proposal_number }}</p>
                    </div>
                    <div class="text-right">
                        @if($proposal->user)
                            <p class="text-sm font-medium text-gray-900">{{ $proposal->user->name }}</p>
                            <p class="text-sm text-gray-600">{{ $proposal->user->email }}</p>
                        @endif
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Status Messages -->
            @if(session('success'))
                <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                    <i class="fas fa-check-circle mr-2"></i>{{ session('success') }}
                </div>
            @endif

            @if(session('info'))
                <div class="mb-6 bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded">
                    <i class="fas fa-info-circle mr-2"></i>{{ session('info') }}
                </div>
            @endif

            <!-- Proposal Details -->
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h2 class="text-lg font-medium text-gray-900">Proposal Details</h2>
                        <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full 
                            @if($proposal->status === 'accepted') bg-green-100 text-green-800
                            @elseif($proposal->status === 'rejected') bg-red-100 text-red-800
                            @elseif($proposal->status === 'viewed') bg-blue-100 text-blue-800
                            @else bg-yellow-100 text-yellow-800 @endif">
                            {{ ucfirst($proposal->status) }}
                        </span>
                    </div>
                </div>
                
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <dl class="space-y-3">
                                @if($proposal->client)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Client</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $proposal->client->name }}</dd>
                                    </div>
                                @endif
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Created Date</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $proposal->created_at->format('M d, Y') }}</dd>
                                </div>
                                @if($proposal->valid_until)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Valid Until</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $proposal->valid_until->format('M d, Y') }}</dd>
                                    </div>
                                @endif
                            </dl>
                        </div>
                        <div>
                            <dl class="space-y-3">
                                @if($proposal->total_amount)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Total Amount</dt>
                                        <dd class="mt-1 text-lg font-semibold text-gray-900">{{ $proposal->formatted_amount }}</dd>
                                    </div>
                                @endif
                                @if($proposal->sent_at)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Sent Date</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $proposal->sent_at->format('M d, Y g:i A') }}</dd>
                                    </div>
                                @endif
                                @if($proposal->accepted_at)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Accepted Date</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $proposal->accepted_at->format('M d, Y g:i A') }}</dd>
                                    </div>
                                @endif
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Proposal Content -->
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Proposal Content</h2>
                </div>
                <div class="px-6 py-4">
                    @if($proposal->description)
                        <div class="mb-6">
                            <h3 class="text-md font-medium text-gray-900 mb-2">Description</h3>
                            <p class="text-gray-700">{{ $proposal->description }}</p>
                        </div>
                    @endif
                    
                    <div>
                        <h3 class="text-md font-medium text-gray-900 mb-4">Details</h3>
                        <div class="prose max-w-none">
                            {!! nl2br(e($proposal->content)) !!}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            @if($proposal->status === 'sent' || $proposal->status === 'viewed')
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-medium text-gray-900">Response Required</h2>
                        <p class="text-sm text-gray-600 mt-1">Please review the proposal and provide your response.</p>
                    </div>
                    <div class="px-6 py-4">
                        <div class="flex space-x-4">
                            <form method="POST" action="{{ route('proposals.public.accept', [$proposal, $proposal->public_token]) }}" class="inline">
                                @csrf
                                <button type="submit" 
                                        onclick="return confirm('Are you sure you want to accept this proposal?')"
                                        class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-6 rounded">
                                    <i class="fas fa-check mr-2"></i>Accept Proposal
                                </button>
                            </form>
                            
                            <form method="POST" action="{{ route('proposals.public.reject', [$proposal, $proposal->public_token]) }}" class="inline">
                                @csrf
                                <button type="submit" 
                                        onclick="return confirm('Are you sure you want to decline this proposal?')"
                                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-6 rounded">
                                    <i class="fas fa-times mr-2"></i>Decline Proposal
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            @elseif($proposal->status === 'accepted')
                <div class="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
                    <i class="fas fa-check-circle text-green-500 text-4xl mb-4"></i>
                    <h3 class="text-lg font-medium text-green-900 mb-2">Proposal Accepted</h3>
                    <p class="text-green-700">Thank you for accepting this proposal. We will be in touch soon to begin the project.</p>
                </div>
            @elseif($proposal->status === 'rejected')
                <div class="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
                    <i class="fas fa-times-circle text-red-500 text-4xl mb-4"></i>
                    <h3 class="text-lg font-medium text-red-900 mb-2">Proposal Declined</h3>
                    <p class="text-red-700">This proposal has been declined. Thank you for your consideration.</p>
                </div>
            @endif
        </main>

        <!-- Footer -->
        <footer class="bg-white border-t border-gray-200 mt-12">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="text-center text-sm text-gray-500">
                    <p>This proposal was generated using <strong>Freeligo</strong> - Professional Freelance Management</p>
                    @if($proposal->user)
                        <p class="mt-1">For questions about this proposal, please contact {{ $proposal->user->email }}</p>
                    @endif
                </div>
            </div>
        </footer>
    </div>
</body>
</html>
