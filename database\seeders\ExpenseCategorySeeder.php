<?php

namespace Database\Seeders;

use App\Models\ExpenseCategory;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ExpenseCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Office Supplies',
                'slug' => 'office-supplies',
                'description' => 'Stationery, paper, pens, and other office materials',
                'icon' => 'fa-paperclip',
                'color' => 'blue',
                'is_tax_deductible' => true,
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Travel & Transportation',
                'slug' => 'travel-transportation',
                'description' => 'Business travel, flights, hotels, car rentals, fuel',
                'icon' => 'fa-plane',
                'color' => 'green',
                'is_tax_deductible' => true,
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Meals & Entertainment',
                'slug' => 'meals-entertainment',
                'description' => 'Business meals, client entertainment, conferences',
                'icon' => 'fa-utensils',
                'color' => 'orange',
                'is_tax_deductible' => true,
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Software & Subscriptions',
                'slug' => 'software-subscriptions',
                'description' => 'Software licenses, SaaS subscriptions, tools',
                'icon' => 'fa-laptop',
                'color' => 'purple',
                'is_tax_deductible' => true,
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'Marketing & Advertising',
                'slug' => 'marketing-advertising',
                'description' => 'Online ads, print materials, promotional items',
                'icon' => 'fa-bullhorn',
                'color' => 'red',
                'is_tax_deductible' => true,
                'is_active' => true,
                'sort_order' => 5,
            ],
            [
                'name' => 'Professional Services',
                'slug' => 'professional-services',
                'description' => 'Legal fees, accounting, consulting, contractors',
                'icon' => 'fa-handshake',
                'color' => 'indigo',
                'is_tax_deductible' => true,
                'is_active' => true,
                'sort_order' => 6,
            ],
            [
                'name' => 'Equipment & Hardware',
                'slug' => 'equipment-hardware',
                'description' => 'Computers, phones, furniture, machinery',
                'icon' => 'fa-desktop',
                'color' => 'gray',
                'is_tax_deductible' => true,
                'is_active' => true,
                'sort_order' => 7,
            ],
            [
                'name' => 'Utilities & Internet',
                'slug' => 'utilities-internet',
                'description' => 'Phone bills, internet, electricity, water',
                'icon' => 'fa-wifi',
                'color' => 'yellow',
                'is_tax_deductible' => true,
                'is_active' => true,
                'sort_order' => 8,
            ],
            [
                'name' => 'Training & Education',
                'slug' => 'training-education',
                'description' => 'Courses, books, certifications, workshops',
                'icon' => 'fa-graduation-cap',
                'color' => 'teal',
                'is_tax_deductible' => true,
                'is_active' => true,
                'sort_order' => 9,
            ],
            [
                'name' => 'Insurance',
                'slug' => 'insurance',
                'description' => 'Business insurance, liability, equipment coverage',
                'icon' => 'fa-shield-alt',
                'color' => 'blue',
                'is_tax_deductible' => true,
                'is_active' => true,
                'sort_order' => 10,
            ],
            [
                'name' => 'Bank & Finance',
                'slug' => 'bank-finance',
                'description' => 'Bank fees, transaction charges, payment processing',
                'icon' => 'fa-university',
                'color' => 'green',
                'is_tax_deductible' => true,
                'is_active' => true,
                'sort_order' => 11,
            ],
            [
                'name' => 'Miscellaneous',
                'slug' => 'miscellaneous',
                'description' => 'Other business-related expenses',
                'icon' => 'fa-ellipsis-h',
                'color' => 'gray',
                'is_tax_deductible' => false,
                'is_active' => true,
                'sort_order' => 12,
            ],
        ];

        foreach ($categories as $category) {
            ExpenseCategory::updateOrCreate(
                ['slug' => $category['slug']],
                $category
            );
        }
    }
}
