<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Create Proposal') }}
            </h2>
            <a href="{{ route('proposals.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                <i class="fas fa-arrow-left mr-2"></i>Back to Proposals
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <form method="POST" action="{{ route('proposals.store') }}" x-data="proposalForm()">
                @csrf
                
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Title -->
                            <div class="md:col-span-2">
                                <label for="title" class="block text-sm font-medium text-gray-700">Proposal Title *</label>
                                <input type="text" name="title" id="title" value="{{ old('title') }}" required
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('title')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Client -->
                            <div>
                                <label for="client_id" class="block text-sm font-medium text-gray-700">Client</label>
                                <select name="client_id" id="client_id" 
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="">Select a client (optional)</option>
                                    @foreach($clients as $client)
                                        <option value="{{ $client->id }}" {{ old('client_id') == $client->id ? 'selected' : '' }}>
                                            {{ $client->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('client_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Template -->
                            <div>
                                <label for="proposal_template_id" class="block text-sm font-medium text-gray-700">Use Template</label>
                                <select name="proposal_template_id" id="proposal_template_id" x-model="selectedTemplate" @change="loadTemplate()"
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="">Start from scratch</option>
                                    @foreach($templates as $template)
                                        <option value="{{ $template->id }}" 
                                                {{ old('proposal_template_id') == $template->id || (isset($selectedTemplate) && $selectedTemplate->id == $template->id) ? 'selected' : '' }}>
                                            {{ $template->name }}
                                            @if($template->is_system_template)
                                                (System)
                                            @endif
                                        </option>
                                    @endforeach
                                </select>
                                @error('proposal_template_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Total Amount -->
                            <div>
                                <label for="total_amount" class="block text-sm font-medium text-gray-700">Total Amount</label>
                                <div class="mt-1 relative rounded-md shadow-sm">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm">$</span>
                                    </div>
                                    <input type="number" name="total_amount" id="total_amount" step="0.01" min="0" 
                                           value="{{ old('total_amount') }}"
                                           class="pl-7 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                </div>
                                @error('total_amount')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Valid Until -->
                            <div>
                                <label for="valid_until" class="block text-sm font-medium text-gray-700">Valid Until</label>
                                <input type="date" name="valid_until" id="valid_until" 
                                       value="{{ old('valid_until', now()->addDays(30)->format('Y-m-d')) }}"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('valid_until')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Description -->
                            <div class="md:col-span-2">
                                <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                                <textarea name="description" id="description" rows="3" 
                                          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('description') }}</textarea>
                                @error('description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Content -->
                            <div class="md:col-span-2">
                                <div class="flex justify-between items-center mb-2">
                                    <label for="content" class="block text-sm font-medium text-gray-700">Proposal Content *</label>
                                    @if(auth()->user()->currentPlan && auth()->user()->currentPlan->slug === 'business')
                                        <div class="flex space-x-2">
                                            <button type="button" @click="generateAIContent()"
                                                    class="text-sm bg-purple-500 hover:bg-purple-700 text-white px-3 py-1 rounded">
                                                <i class="fas fa-magic mr-1"></i>AI Generate
                                            </button>
                                            <button type="button" @click="improveContent()"
                                                    class="text-sm bg-blue-500 hover:bg-blue-700 text-white px-3 py-1 rounded">
                                                <i class="fas fa-wand-magic-sparkles mr-1"></i>AI Improve
                                            </button>
                                        </div>
                                    @endif
                                </div>
                                <div class="mt-1">
                                    <textarea name="content" id="content" rows="15" required x-model="content"
                                              class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('content', $selectedTemplate->content ?? '') }}</textarea>
                                </div>
                                @error('content')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror

                                <!-- AI Loading Indicator -->
                                <div x-show="aiLoading" class="mt-2 flex items-center text-sm text-blue-600">
                                    <i class="fas fa-spinner fa-spin mr-2"></i>
                                    <span x-text="aiLoadingText">Generating content...</span>
                                </div>
                            </div>

                            <!-- Internal Notes -->
                            <div class="md:col-span-2">
                                <label for="internal_notes" class="block text-sm font-medium text-gray-700">Internal Notes</label>
                                <textarea name="internal_notes" id="internal_notes" rows="3" 
                                          placeholder="Private notes for your reference..."
                                          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('internal_notes') }}</textarea>
                                @error('internal_notes')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="mt-6 flex justify-end space-x-3">
                            <a href="{{ route('proposals.index') }}" 
                               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" 
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                <i class="fas fa-save mr-2"></i>Create Proposal
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        function proposalForm() {
            return {
                selectedTemplate: '{{ old('proposal_template_id', $selectedTemplate->id ?? '') }}',
                content: '{{ old('content', $selectedTemplate->content ?? '') }}',
                aiLoading: false,
                aiLoadingText: 'Generating content...',

                loadTemplate() {
                    if (this.selectedTemplate) {
                        fetch(`/proposal-templates/${this.selectedTemplate}/content`)
                            .then(response => response.json())
                            .then(data => {
                                this.content = data.template.content;
                                document.getElementById('content').value = this.content;
                            })
                            .catch(error => {
                                console.error('Error loading template:', error);
                            });
                    } else {
                        this.content = '';
                        document.getElementById('content').value = '';
                    }
                },

                async generateAIContent() {
                    const title = document.getElementById('title').value;
                    const description = document.getElementById('description').value;
                    const clientId = document.getElementById('client_id').value;
                    const totalAmount = document.getElementById('total_amount').value;

                    if (!title.trim()) {
                        alert('Please enter a proposal title first.');
                        return;
                    }

                    this.aiLoading = true;
                    this.aiLoadingText = 'AI is generating your proposal content...';

                    try {
                        const response = await fetch('/ai-assistant/generate-proposal', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            body: JSON.stringify({
                                client_name: clientId ? document.querySelector(`#client_id option[value="${clientId}"]`).textContent : '',
                                project_type: title,
                                project_description: description || title,
                                budget: totalAmount,
                                timeline: '4-6 weeks'
                            })
                        });

                        const data = await response.json();

                        if (data.content) {
                            this.content = data.content;
                            document.getElementById('content').value = this.content;
                        } else {
                            alert(data.error || 'Failed to generate content');
                        }
                    } catch (error) {
                        console.error('Error generating AI content:', error);
                        alert('An error occurred while generating content');
                    } finally {
                        this.aiLoading = false;
                    }
                },

                async improveContent() {
                    const currentContent = this.content || document.getElementById('content').value;

                    if (!currentContent.trim()) {
                        alert('Please enter some content first.');
                        return;
                    }

                    this.aiLoading = true;
                    this.aiLoadingText = 'AI is improving your proposal content...';

                    try {
                        const response = await fetch('/ai-assistant/improve-proposal', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            body: JSON.stringify({
                                content: currentContent,
                                focus_areas: ['clarity', 'persuasion', 'professionalism']
                            })
                        });

                        const data = await response.json();

                        if (data.improved_content) {
                            this.content = data.improved_content;
                            document.getElementById('content').value = this.content;
                        } else {
                            alert(data.error || 'Failed to improve content');
                        }
                    } catch (error) {
                        console.error('Error improving content:', error);
                        alert('An error occurred while improving content');
                    } finally {
                        this.aiLoading = false;
                    }
                }
            }
        }
    </script>
</x-app-layout>
