@props([
    'type' => 'info',
    'title' => null,
    'dismissible' => false,
    'icon' => null,
    'variant' => 'filled'
])

@php
    $defaultIcons = [
        'success' => 'fas fa-check-circle',
        'warning' => 'fas fa-exclamation-triangle',
        'danger' => 'fas fa-exclamation-circle',
        'info' => 'fas fa-info-circle'
    ];
    
    $filledClasses = [
        'success' => 'bg-success-50 border-success-200 text-success-800',
        'warning' => 'bg-warning-50 border-warning-200 text-warning-800',
        'danger' => 'bg-danger-50 border-danger-200 text-danger-800',
        'info' => 'bg-primary-50 border-primary-200 text-primary-800'
    ];
    
    $outlineClasses = [
        'success' => 'bg-white border-success-300 text-success-700',
        'warning' => 'bg-white border-warning-300 text-warning-700',
        'danger' => 'bg-white border-danger-300 text-danger-700',
        'info' => 'bg-white border-primary-300 text-primary-700'
    ];
    
    $iconClasses = [
        'success' => 'text-success-500',
        'warning' => 'text-warning-500',
        'danger' => 'text-danger-500',
        'info' => 'text-primary-500'
    ];
    
    $classes = $variant === 'filled' ? $filledClasses[$type] : $outlineClasses[$type];
    $iconColor = $iconClasses[$type];
    $displayIcon = $icon ?? $defaultIcons[$type];
@endphp

<div x-data="{ show: true }" 
     x-show="show"
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0 transform scale-95"
     x-transition:enter-end="opacity-100 transform scale-100"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100 transform scale-100"
     x-transition:leave-end="opacity-0 transform scale-95"
     class="rounded-2xl border p-4 {{ $classes }} {{ $attributes->get('class') }}">
    
    <div class="flex items-start">
        @if($displayIcon)
            <div class="flex-shrink-0">
                <i class="{{ $displayIcon }} {{ $iconColor }} text-lg"></i>
            </div>
        @endif
        
        <div class="ml-3 flex-1">
            @if($title)
                <h3 class="text-sm font-semibold mb-1">{{ $title }}</h3>
            @endif
            
            <div class="text-sm">
                {{ $slot }}
            </div>
        </div>
        
        @if($dismissible)
            <div class="ml-auto pl-3">
                <button @click="show = false" 
                        class="inline-flex rounded-xl p-1.5 hover:bg-black hover:bg-opacity-10 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent focus:ring-secondary-500 transition-colors duration-200">
                    <span class="sr-only">Dismiss</span>
                    <i class="fas fa-times text-sm"></i>
                </button>
            </div>
        @endif
    </div>
</div>
