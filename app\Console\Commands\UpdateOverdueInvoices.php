<?php

namespace App\Console\Commands;

use App\Models\Invoice;
use App\Services\ClientPortalService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateOverdueInvoices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'invoices:update-overdue';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update invoice status to overdue for invoices past their due date';

    /**
     * Execute the console command.
     */
    public function handle(ClientPortalService $clientPortalService)
    {
        $this->info('Checking for overdue invoices...');

        // Get all sent invoices that are past due date but not marked as overdue
        $invoices = Invoice::where('status', 'sent')
            ->where('due_date', '<', now())
            ->get();

        $updatedCount = 0;

        foreach ($invoices as $invoice) {
            if ($clientPortalService->checkAndUpdateOverdueStatus($invoice)) {
                $updatedCount++;
                $this->line("Invoice {$invoice->invoice_number} marked as overdue");
            }
        }

        $this->info("Updated {$updatedCount} invoices to overdue status");

        Log::info('Overdue invoices check completed', [
            'total_checked' => $invoices->count(),
            'updated_count' => $updatedCount,
        ]);

        return Command::SUCCESS;
    }
}
