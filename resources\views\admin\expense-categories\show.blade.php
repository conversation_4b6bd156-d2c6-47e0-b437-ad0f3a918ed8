<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Expense Category Details') }} - {{ $expenseCategory->name }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('admin.expense-categories.edit', $expenseCategory) }}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                    <i class="fas fa-edit mr-2"></i>
                    Edit
                </a>
                <a href="{{ route('admin.expense-categories.index') }}" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Categories
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <!-- Category Details -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Basic Information -->
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
                            
                            <div class="space-y-4">
                                <!-- Category Display -->
                                <div class="flex items-center p-4 bg-gray-50 rounded-lg">
                                    <div class="p-3 bg-{{ $expenseCategory->color }}-100 rounded-lg mr-4">
                                        <i class="fas {{ $expenseCategory->icon }} text-{{ $expenseCategory->color }}-600 text-xl"></i>
                                    </div>
                                    <div>
                                        <div class="text-lg font-medium text-gray-900">{{ $expenseCategory->name }}</div>
                                        <div class="text-sm text-gray-500">{{ $expenseCategory->slug }}</div>
                                    </div>
                                </div>

                                <!-- Description -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                                    <p class="text-sm text-gray-900">
                                        {{ $expenseCategory->description ?: 'No description provided' }}
                                    </p>
                                </div>

                                <!-- Icon and Color -->
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Icon</label>
                                        <div class="flex items-center">
                                            <i class="fas {{ $expenseCategory->icon }} text-gray-600 mr-2"></i>
                                            <span class="text-sm text-gray-900">{{ $expenseCategory->icon }}</span>
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Color</label>
                                        <div class="flex items-center">
                                            <div class="w-4 h-4 bg-{{ $expenseCategory->color }}-500 rounded mr-2"></div>
                                            <span class="text-sm text-gray-900 capitalize">{{ $expenseCategory->color }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Settings & Statistics -->
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Settings & Statistics</h3>
                            
                            <div class="space-y-4">
                                <!-- Status Badges -->
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                                        @if($expenseCategory->is_active)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                <i class="fas fa-check-circle mr-1"></i>
                                                Active
                                            </span>
                                        @else
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                <i class="fas fa-times-circle mr-1"></i>
                                                Inactive
                                            </span>
                                        @endif
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Tax Deductible</label>
                                        @if($expenseCategory->is_tax_deductible)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                <i class="fas fa-check mr-1"></i>
                                                Yes
                                            </span>
                                        @else
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                <i class="fas fa-times mr-1"></i>
                                                No
                                            </span>
                                        @endif
                                    </div>
                                </div>

                                <!-- Sort Order -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Sort Order</label>
                                    <p class="text-sm text-gray-900">{{ $expenseCategory->sort_order }}</p>
                                </div>

                                <!-- Statistics -->
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <h4 class="text-sm font-medium text-gray-700 mb-3">Usage Statistics</h4>
                                    <div class="grid grid-cols-1 gap-3">
                                        <div class="flex justify-between">
                                            <span class="text-sm text-gray-600">Total Expenses:</span>
                                            <span class="text-sm font-medium text-gray-900">{{ $expenseCategory->expenses_count }}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Timestamps -->
                                <div class="text-xs text-gray-500 space-y-1">
                                    <div>Created: {{ $expenseCategory->created_at->format('M j, Y g:i A') }}</div>
                                    <div>Updated: {{ $expenseCategory->updated_at->format('M j, Y g:i A') }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Expenses -->
            @if($recentExpenses->count() > 0)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-900">Recent Expenses ({{ $recentExpenses->count() }})</h3>
                        </div>
                        
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Expense
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            User
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Amount
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Date
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Status
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($recentExpenses as $expense)
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900">{{ $expense->title }}</div>
                                                    <div class="text-sm text-gray-500">{{ $expense->expense_number }}</div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900">{{ $expense->user->name }}</div>
                                                <div class="text-sm text-gray-500">{{ $expense->user->email }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">
                                                    ₹{{ number_format($expense->amount + $expense->tax_amount, 2) }}
                                                </div>
                                                @if($expense->tax_amount > 0)
                                                    <div class="text-xs text-gray-500">
                                                        (₹{{ number_format($expense->amount, 2) }} + ₹{{ number_format($expense->tax_amount, 2) }} tax)
                                                    </div>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $expense->expense_date->format('M j, Y') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                                    @if($expense->status === 'approved') bg-green-100 text-green-800
                                                    @elseif($expense->status === 'rejected') bg-red-100 text-red-800
                                                    @elseif($expense->status === 'submitted') bg-yellow-100 text-yellow-800
                                                    @else bg-gray-100 text-gray-800 @endif">
                                                    {{ ucfirst($expense->status) }}
                                                </span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Actions -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Actions</h3>
                    
                    <div class="flex flex-wrap gap-4">
                        <a href="{{ route('admin.expense-categories.edit', $expenseCategory) }}" 
                           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                            <i class="fas fa-edit mr-2"></i>
                            Edit Category
                        </a>

                        <form method="POST" 
                              action="{{ route('admin.expense-categories.toggle-status', $expenseCategory) }}" 
                              class="inline">
                            @csrf
                            @method('PATCH')
                            <button type="submit" 
                                    class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                                <i class="fas fa-{{ $expenseCategory->is_active ? 'pause' : 'play' }} mr-2"></i>
                                {{ $expenseCategory->is_active ? 'Deactivate' : 'Activate' }}
                            </button>
                        </form>

                        @if($expenseCategory->expenses_count === 0)
                            <form method="POST" 
                                  action="{{ route('admin.expense-categories.destroy', $expenseCategory) }}" 
                                  class="inline"
                                  onsubmit="return confirm('Are you sure you want to delete this category? This action cannot be undone.')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" 
                                        class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                                    <i class="fas fa-trash mr-2"></i>
                                    Delete Category
                                </button>
                            </form>
                        @else
                            <div class="text-sm text-gray-500 flex items-center">
                                <i class="fas fa-info-circle mr-2"></i>
                                Cannot delete category with existing expenses
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
