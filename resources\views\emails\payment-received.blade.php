<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment {{ $recipientType === 'client' ? 'Confirmation' : 'Received' }} - {{ $invoice->invoice_number }}</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #374151;
            background-color: #f9fafb;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 32px 24px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
        }
        .header p {
            margin: 8px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        .success-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }
        .content {
            padding: 32px 24px;
        }
        .payment-details {
            background-color: #f0fdf4;
            border: 2px solid #10b981;
            border-radius: 8px;
            padding: 24px;
            margin: 24px 0;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            padding-bottom: 12px;
            border-bottom: 1px solid #d1fae5;
        }
        .detail-row:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }
        .detail-label {
            font-weight: 600;
            color: #065f46;
        }
        .detail-value {
            font-weight: 600;
            color: #064e3b;
        }
        .amount {
            font-size: 24px;
            font-weight: 700;
            color: #10b981;
        }
        .status-badge {
            display: inline-block;
            background-color: #10b981;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            margin: 24px 0;
        }
        .footer {
            background-color: #f9fafb;
            padding: 24px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }
        .footer p {
            margin: 0;
            font-size: 14px;
            color: #6b7280;
        }
        .remaining-balance {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 16px;
            margin: 24px 0;
        }
        .remaining-balance h3 {
            margin: 0 0 8px 0;
            color: #92400e;
            font-size: 16px;
        }
        @media (max-width: 600px) {
            .container {
                margin: 0;
                border-radius: 0;
            }
            .content {
                padding: 24px 16px;
            }
            .header {
                padding: 24px 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="success-icon">✅</div>
            <h1>Payment {{ $recipientType === 'client' ? 'Confirmed' : 'Received' }}!</h1>
            <p>{{ $recipientType === 'client' ? 'Your payment has been successfully processed' : 'A payment has been received for your invoice' }}</p>
        </div>

        <!-- Content -->
        <div class="content">
            @if($recipientType === 'client')
                <p>Dear {{ $client->name }},</p>
                <p>Thank you for your payment! We have successfully received your payment for invoice {{ $invoice->invoice_number }}. Here are the details:</p>
            @else
                <p>Hello {{ $freelancer->name }},</p>
                <p>Great news! You have received a payment from {{ $client->name }} for invoice {{ $invoice->invoice_number }}. Here are the payment details:</p>
            @endif

            <!-- Payment Details -->
            <div class="payment-details">
                <div class="detail-row">
                    <span class="detail-label">Invoice Number:</span>
                    <span class="detail-value">{{ $invoice->invoice_number }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Payment Amount:</span>
                    <span class="detail-value amount">{{ $payment->formatted_amount }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Payment Method:</span>
                    <span class="detail-value">{{ ucfirst($payment->gateway) }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Transaction ID:</span>
                    <span class="detail-value">{{ $payment->payment_id }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Payment Date:</span>
                    <span class="detail-value">{{ $payment->paid_at->format('M d, Y g:i A') }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Status:</span>
                    <span class="detail-value">
                        <span class="status-badge">{{ ucfirst($payment->status) }}</span>
                    </span>
                </div>
            </div>

            @if(!$isFullyPaid && $remainingAmount > 0)
                <!-- Remaining Balance -->
                <div class="remaining-balance">
                    <h3>Remaining Balance</h3>
                    <p>
                        <strong>{{ config('services.currency.symbol', '₹') }}{{ number_format($remainingAmount, 2) }}</strong> 
                        is still pending for this invoice.
                        @if($recipientType === 'client')
                            You can make additional payments using the link below.
                        @endif
                    </p>
                </div>
            @else
                <!-- Fully Paid -->
                <div style="background-color: #f0fdf4; border-left: 4px solid #10b981; padding: 16px; margin: 24px 0;">
                    <h3 style="margin: 0 0 8px 0; color: #065f46; font-size: 16px;">✅ Invoice Fully Paid</h3>
                    <p style="margin: 0; color: #065f46;">This invoice has been paid in full. Thank you!</p>
                </div>
            @endif

            <!-- Call to Action -->
            <div style="text-align: center;">
                <a href="{{ $invoice->public_url }}" class="cta-button">
                    📄 View Invoice Details
                </a>
                @if($recipientType === 'client' && !$isFullyPaid)
                    <a href="{{ $invoice->payment_url }}" class="cta-button" style="background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);">
                        💳 Make Additional Payment
                    </a>
                @endif
            </div>

            @if($recipientType === 'client')
                <p>If you have any questions about this payment or your invoice, please don't hesitate to contact us.</p>
                <p>Thank you for your business!</p>
                <p>Best regards,<br>
                <strong>{{ $freelancer->business_name ?: $freelancer->name }}</strong></p>
            @else
                <p>The payment has been automatically processed and your invoice status has been updated accordingly.</p>
                @if(!$isFullyPaid)
                    <p>Note: This is a partial payment. The remaining balance is {{ config('services.currency.symbol', '₹') }}{{ number_format($remainingAmount, 2) }}.</p>
                @endif
                <p>You can view the complete payment details and invoice information using the link above.</p>
            @endif
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>This is an automated email from {{ config('app.name') }}. Please do not reply to this email.</p>
            <p style="margin-top: 8px;">
                <a href="{{ $invoice->public_url }}" style="color: #059669; text-decoration: none;">View Invoice Online</a>
            </p>
        </div>
    </div>
</body>
</html>
