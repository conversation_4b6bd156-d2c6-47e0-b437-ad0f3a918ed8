<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Proposals') }}
            </h2>
            @if($isAtLimit)
                <a href="{{ route('proposals.upgrade') }}" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                    <i class="fas fa-lock mr-2"></i>Upgrade to Create
                </a>
            @else
                <a href="{{ route('proposals.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    <i class="fas fa-plus mr-2"></i>Create Proposal
                </a>
            @endif
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Plan Usage Alert -->
            @php
                $currentPlan = auth()->user()->currentPlan;
                $isFreePlan = !$currentPlan || $currentPlan->slug === 'free';
                $monthlyCount = auth()->user()->proposals()->whereMonth('created_at', now()->month)->count();
                $limit = $isFreePlan ? 2 : 'unlimited';
                $isNearLimit = $isFreePlan && $monthlyCount >= 1;
                $isAtLimitInline = $isFreePlan && $monthlyCount >= 2;
            @endphp

            @if($isFreePlan)
                <div class="mb-6 bg-{{ $isAtLimitInline ? 'red' : ($isNearLimit ? 'yellow' : 'blue') }}-50 border border-{{ $isAtLimitInline ? 'red' : ($isNearLimit ? 'yellow' : 'blue') }}-200 rounded-lg p-4">
                    <div class="flex justify-between items-center">
                        <div>
                            <h3 class="text-sm font-medium text-{{ $isAtLimitInline ? 'red' : ($isNearLimit ? 'yellow' : 'blue') }}-800">
                                @if($isAtLimitInline)
                                    Proposal Limit Reached
                                @elseif($isNearLimit)
                                    Approaching Proposal Limit
                                @else
                                    Free Plan Usage
                                @endif
                            </h3>
                            <p class="text-sm text-{{ $isAtLimitInline ? 'red' : ($isNearLimit ? 'yellow' : 'blue') }}-700">
                                You've used {{ $monthlyCount }} of {{ $limit }} proposals this month
                                @if($isAtLimitInline)
                                    - Upgrade to create more proposals
                                @endif
                            </p>
                        </div>
                        @if($isNearLimit || $isAtLimitInline)
                            <a href="{{ route('proposals.upgrade') }}"
                               class="bg-{{ $isAtLimitInline ? 'red' : 'yellow' }}-600 hover:bg-{{ $isAtLimitInline ? 'red' : 'yellow' }}-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                                Upgrade Plan
                            </a>
                        @endif
                    </div>

                    <!-- Progress Bar -->
                    <div class="mt-3">
                        <div class="bg-{{ $isAtLimitInline ? 'red' : ($isNearLimit ? 'yellow' : 'blue') }}-200 rounded-full h-2">
                            <div class="bg-{{ $isAtLimitInline ? 'red' : ($isNearLimit ? 'yellow' : 'blue') }}-600 h-2 rounded-full"
                                 style="width: {{ ($monthlyCount / 2) * 100 }}%"></div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-file-alt text-blue-500 text-2xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total Proposals</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ $stats['total'] }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-check-circle text-green-500 text-2xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Accepted</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ $stats['accepted'] }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-percentage text-purple-500 text-2xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Acceptance Rate</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ $stats['acceptance_rate'] }}%</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-dollar-sign text-yellow-500 text-2xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total Value</p>
                                <p class="text-2xl font-semibold text-gray-900">${{ number_format($stats['total_value'], 2) }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <form method="GET" action="{{ route('proposals.index') }}" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                            <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                            <input type="text" name="search" id="search" value="{{ request('search') }}" 
                                   placeholder="Search proposals..." 
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        </div>

                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                            <select name="status" id="status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">All Statuses</option>
                                <option value="draft" {{ request('status') === 'draft' ? 'selected' : '' }}>Draft</option>
                                <option value="sent" {{ request('status') === 'sent' ? 'selected' : '' }}>Sent</option>
                                <option value="viewed" {{ request('status') === 'viewed' ? 'selected' : '' }}>Viewed</option>
                                <option value="accepted" {{ request('status') === 'accepted' ? 'selected' : '' }}>Accepted</option>
                                <option value="rejected" {{ request('status') === 'rejected' ? 'selected' : '' }}>Rejected</option>
                                <option value="expired" {{ request('status') === 'expired' ? 'selected' : '' }}>Expired</option>
                            </select>
                        </div>

                        <div>
                            <label for="client_id" class="block text-sm font-medium text-gray-700">Client</label>
                            <select name="client_id" id="client_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">All Clients</option>
                                @foreach($clients as $client)
                                    <option value="{{ $client->id }}" {{ request('client_id') == $client->id ? 'selected' : '' }}>
                                        {{ $client->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div>
                            <label for="date_from" class="block text-sm font-medium text-gray-700">From Date</label>
                            <input type="date" name="date_from" id="date_from" value="{{ request('date_from') }}" 
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        </div>

                        <div class="flex items-end">
                            <button type="submit" class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                <i class="fas fa-search mr-2"></i>Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Proposals Table -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    @if($proposals->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Proposal
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Client
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Amount
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Status
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Valid Until
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Created
                                        </th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($proposals as $proposal)
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900">
                                                        <a href="{{ route('proposals.show', $proposal) }}" class="text-blue-600 hover:text-blue-900">
                                                            {{ $proposal->title }}
                                                        </a>
                                                    </div>
                                                    <div class="text-sm text-gray-500">{{ $proposal->proposal_number }}</div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900">
                                                    {{ $proposal->client ? $proposal->client->name : 'No Client' }}
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900">
                                                    @if($proposal->total_amount)
                                                        {{ $proposal->formatted_amount }}
                                                    @else
                                                        <span class="text-gray-400">Not set</span>
                                                    @endif
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                                    @if($proposal->status === 'draft') bg-gray-100 text-gray-800
                                                    @elseif($proposal->status === 'sent') bg-blue-100 text-blue-800
                                                    @elseif($proposal->status === 'viewed') bg-yellow-100 text-yellow-800
                                                    @elseif($proposal->status === 'accepted') bg-green-100 text-green-800
                                                    @elseif($proposal->status === 'rejected') bg-red-100 text-red-800
                                                    @elseif($proposal->status === 'expired') bg-gray-100 text-gray-800
                                                    @endif">
                                                    {{ ucfirst($proposal->status) }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                @if($proposal->valid_until)
                                                    {{ $proposal->valid_until->format('M d, Y') }}
                                                    @if($proposal->valid_until->isPast())
                                                        <span class="text-red-500 text-xs">(Expired)</span>
                                                    @endif
                                                @else
                                                    <span class="text-gray-400">Not set</span>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $proposal->created_at->format('M d, Y') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <div class="flex justify-end space-x-2">
                                                    <a href="{{ route('proposals.show', $proposal) }}" 
                                                       class="text-blue-600 hover:text-blue-900" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    @if($proposal->canBeEdited())
                                                        <a href="{{ route('proposals.edit', $proposal) }}" 
                                                           class="text-indigo-600 hover:text-indigo-900" title="Edit">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    @endif
                                                    <form method="POST" action="{{ route('proposals.destroy', $proposal) }}" 
                                                          class="inline" onsubmit="return confirm('Are you sure you want to delete this proposal?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="text-red-600 hover:text-red-900" title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="mt-6">
                            {{ $proposals->withQueryString()->links() }}
                        </div>
                    @else
                        <div class="text-center py-12">
                            <i class="fas fa-file-alt text-gray-400 text-6xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No proposals found</h3>
                            <p class="text-gray-500 mb-6">Get started by creating your first proposal.</p>
                            <a href="{{ route('proposals.create') }}" 
                               class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                <i class="fas fa-plus mr-2"></i>Create Proposal
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
