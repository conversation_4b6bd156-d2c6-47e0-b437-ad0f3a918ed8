@props(['user'])

<div x-data="{
    sidebarOpen: false,
    init() {
        if (window.innerWidth >= 1024) {
            this.sidebarOpen = true;
        }
        window.addEventListener('resize', () => {
            if (window.innerWidth >= 1024) {
                this.sidebarOpen = true;
            }
        });
    }
}" class="flex h-screen bg-gray-50 overflow-hidden">

    <!-- Mobile menu button -->
    <div class="lg:hidden fixed top-4 left-4 z-50">
        <button @click="sidebarOpen = !sidebarOpen"
                class="bg-white p-3 rounded-2xl shadow-card hover:shadow-card-hover transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
            <svg class="w-6 h-6 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path x-show="!sidebarOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                <path x-show="sidebarOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    </div>

    <!-- Mobile sidebar overlay -->
    <div x-show="sidebarOpen"
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 z-40 lg:hidden">
        <div class="fixed inset-0 bg-gray-600 bg-opacity-75" @click="sidebarOpen = false"></div>
    </div>

    <!-- Sidebar -->
    <div x-show="sidebarOpen"
         x-transition:enter="transition ease-in-out duration-300 transform"
         x-transition:enter-start="-translate-x-full"
         x-transition:enter-end="translate-x-0"
         x-transition:leave="transition ease-in-out duration-300 transform"
         x-transition:leave-start="translate-x-0"
         x-transition:leave-end="-translate-x-full"
         class="fixed inset-y-0 left-0 z-50 w-72 bg-white shadow-card-lg lg:translate-x-0 lg:static lg:inset-0 lg:block flex flex-col h-screen border-r border-secondary-200 relative">

        <!-- Sidebar Header -->
        <div class="flex items-center justify-between h-16 px-4 border-b border-secondary-200 flex-shrink-0 bg-gradient-to-r from-primary-50 to-primary-100/50">
            <div class="flex items-center">
                <x-logo class="h-8 w-auto" />
                <span class="ml-2 text-lg font-bold text-secondary-900 tracking-tight">{{ config('app.name', 'Freeligo') }}</span>
            </div>
            <button @click="sidebarOpen = false" class="lg:hidden text-secondary-500 hover:text-secondary-700 p-1.5 rounded-lg hover:bg-secondary-100 transition-colors duration-200">
                <i class="fas fa-times text-sm"></i>
            </button>
        </div>

        <!-- User Profile Section -->
        <div class="px-4 py-3 border-b border-secondary-200 bg-gradient-to-r from-secondary-50 to-neutral-50 flex-shrink-0">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-9 h-9 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center shadow-soft">
                        <span class="text-white font-medium text-xs">{{ substr($user->name, 0, 2) }}</span>
                    </div>
                </div>
                <div class="ml-3 flex-1 min-w-0">
                    <p class="text-xs font-semibold text-secondary-900 truncate">{{ $user->name }}</p>
                    <p class="text-xs text-secondary-500 truncate">{{ $user->email }}</p>
                    @if($user->hasRole('admin'))
                        <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-accent-100 text-accent-800 mt-0.5">
                            <i class="fas fa-crown mr-1 text-xs"></i>
                            Admin
                        </span>
                    @endif
                </div>
            </div>
        </div>

        <!-- Navigation Links -->
        <div class="px-4 py-4 overflow-y-auto scrollbar-thin" style="height: calc(100vh - 200px);">
            <nav class="space-y-0.5">
            @if(auth()->user()->hasRole('admin'))
                <!-- Admin Section Header -->
                <div class="px-2 py-1.5 mb-2">
                    <h3 class="text-xs font-semibold text-secondary-500 uppercase tracking-wider">Administration</h3>
                </div>

                <!-- Admin Menu -->
                <a href="{{ route('admin.dashboard') }}"
                   class="nav-link {{ request()->routeIs('admin.dashboard') ? 'nav-link-active' : 'nav-link-inactive' }}">
                    <div class="flex items-center justify-center w-8 h-8 rounded-lg {{ request()->routeIs('admin.dashboard') ? 'bg-primary-100 text-primary-600' : 'bg-secondary-100 text-secondary-500 group-hover:bg-primary-100 group-hover:text-primary-600' }} transition-all duration-200 mr-2.5">
                        <i class="fas fa-tachometer-alt text-xs"></i>
                    </div>
                    <span class="font-medium text-sm">Dashboard</span>
                </a>

                <a href="{{ route('admin.users.index') }}"
                   class="nav-link {{ request()->routeIs('admin.users.*') ? 'nav-link-active' : 'nav-link-inactive' }}">
                    <div class="flex items-center justify-center w-10 h-10 rounded-xl {{ request()->routeIs('admin.users.*') ? 'bg-primary-100 text-primary-600' : 'bg-secondary-100 text-secondary-500 group-hover:bg-primary-100 group-hover:text-primary-600' }} transition-all duration-200 mr-3">
                        <i class="fas fa-users text-sm"></i>
                    </div>
                    <span class="font-medium">User Management</span>
                </a>

                <a href="{{ route('admin.plans.index') }}"
                   class="nav-link {{ request()->routeIs('admin.plans.*') ? 'nav-link-active' : 'nav-link-inactive' }}">
                    <div class="flex items-center justify-center w-10 h-10 rounded-xl {{ request()->routeIs('admin.plans.*') ? 'bg-primary-100 text-primary-600' : 'bg-secondary-100 text-secondary-500 group-hover:bg-primary-100 group-hover:text-primary-600' }} transition-all duration-200 mr-3">
                        <i class="fas fa-credit-card text-sm"></i>
                    </div>
                    <span class="font-medium">Plan Management</span>
                </a>

                <a href="{{ route('admin.subscriptions.index') }}"
                   class="nav-link {{ request()->routeIs('admin.subscriptions.*') ? 'nav-link-active' : 'nav-link-inactive' }}">
                    <div class="flex items-center justify-center w-10 h-10 rounded-xl {{ request()->routeIs('admin.subscriptions.*') ? 'bg-primary-100 text-primary-600' : 'bg-secondary-100 text-secondary-500 group-hover:bg-primary-100 group-hover:text-primary-600' }} transition-all duration-200 mr-3">
                        <i class="fas fa-receipt text-sm"></i>
                    </div>
                    <span class="font-medium">Subscriptions</span>
                </a>

                <a href="{{ route('admin.settings.index') }}"
                   class="nav-link {{ request()->routeIs('admin.settings.*') ? 'nav-link-active' : 'nav-link-inactive' }}">
                    <div class="flex items-center justify-center w-10 h-10 rounded-xl {{ request()->routeIs('admin.settings.*') ? 'bg-primary-100 text-primary-600' : 'bg-secondary-100 text-secondary-500 group-hover:bg-primary-100 group-hover:text-primary-600' }} transition-all duration-200 mr-3">
                        <i class="fas fa-cogs text-sm"></i>
                    </div>
                    <span class="font-medium">Site Settings</span>
                </a>
            @else
                <!-- Main Section Header -->
                <div class="px-3 py-2 mb-3">
                    <h3 class="text-xs font-semibold text-secondary-500 uppercase tracking-wider">Overview</h3>
                </div>

                <!-- Main Menu -->
                <a href="{{ route('dashboard') }}"
                   class="nav-link {{ request()->routeIs('dashboard') ? 'nav-link-active' : 'nav-link-inactive' }}">
                    <div class="flex items-center justify-center w-10 h-10 rounded-xl {{ request()->routeIs('dashboard') ? 'bg-primary-100 text-primary-600' : 'bg-secondary-100 text-secondary-500 group-hover:bg-primary-100 group-hover:text-primary-600' }} transition-all duration-200 mr-3">
                        <i class="fas fa-tachometer-alt text-sm"></i>
                    </div>
                    <span class="font-medium">Dashboard</span>
                </a>

                <!-- Business Section Header -->
                <div class="px-3 py-2 mb-3 mt-6">
                    <h3 class="text-xs font-semibold text-secondary-500 uppercase tracking-wider">Business</h3>
                </div>

                <a href="{{ route('leads.index') }}"
                   class="nav-link {{ request()->routeIs('leads.*') ? 'nav-link-active' : 'nav-link-inactive' }}">
                    <div class="flex items-center justify-center w-10 h-10 rounded-xl {{ request()->routeIs('leads.*') ? 'bg-primary-100 text-primary-600' : 'bg-secondary-100 text-secondary-500 group-hover:bg-primary-100 group-hover:text-primary-600' }} transition-all duration-200 mr-3">
                        <i class="fas fa-user-plus text-sm"></i>
                    </div>
                    <span class="font-medium">Leads</span>
                </a>

                <a href="{{ route('clients.index') }}"
                   class="nav-link {{ request()->routeIs('clients.*') ? 'nav-link-active' : 'nav-link-inactive' }}">
                    <div class="flex items-center justify-center w-10 h-10 rounded-xl {{ request()->routeIs('clients.*') ? 'bg-primary-100 text-primary-600' : 'bg-secondary-100 text-secondary-500 group-hover:bg-primary-100 group-hover:text-primary-600' }} transition-all duration-200 mr-3">
                        <i class="fas fa-users text-sm"></i>
                    </div>
                    <span class="font-medium">Clients</span>
                </a>

                <a href="{{ route('invoices.index') }}"
                   class="nav-link {{ request()->routeIs('invoices.*') ? 'nav-link-active' : 'nav-link-inactive' }}">
                    <div class="flex items-center justify-center w-10 h-10 rounded-xl {{ request()->routeIs('invoices.*') ? 'bg-primary-100 text-primary-600' : 'bg-secondary-100 text-secondary-500 group-hover:bg-primary-100 group-hover:text-primary-600' }} transition-all duration-200 mr-3">
                        <i class="fas fa-file-invoice-dollar text-sm"></i>
                    </div>
                    <span class="font-medium">Invoices</span>
                </a>

                <a href="{{ route('payment-tracking.index') }}"
                   class="nav-link {{ request()->routeIs('payment-tracking.*') ? 'nav-link-active' : 'nav-link-inactive' }}">
                    <div class="flex items-center justify-center w-10 h-10 rounded-xl {{ request()->routeIs('payment-tracking.*') ? 'bg-primary-100 text-primary-600' : 'bg-secondary-100 text-secondary-500 group-hover:bg-primary-100 group-hover:text-primary-600' }} transition-all duration-200 mr-3">
                        <i class="fas fa-credit-card text-sm"></i>
                    </div>
                    <span class="font-medium">Payment Tracking</span>
                </a>

                <a href="{{ route('proposals.index') }}"
                   class="nav-link {{ request()->routeIs('proposals.*') || request()->routeIs('proposal-templates.*') ? 'nav-link-active' : 'nav-link-inactive' }}">
                    <div class="flex items-center justify-center w-10 h-10 rounded-xl {{ request()->routeIs('proposals.*') || request()->routeIs('proposal-templates.*') ? 'bg-primary-100 text-primary-600' : 'bg-secondary-100 text-secondary-500 group-hover:bg-primary-100 group-hover:text-primary-600' }} transition-all duration-200 mr-3">
                        <i class="fas fa-file-alt text-sm"></i>
                    </div>
                    <span class="font-medium">Proposals</span>
                </a>

                <a href="{{ route('contracts.index') }}"
                   class="nav-link {{ request()->routeIs('contracts.*') ? 'nav-link-active' : 'nav-link-inactive' }}">
                    <div class="flex items-center justify-center w-10 h-10 rounded-xl {{ request()->routeIs('contracts.*') ? 'bg-primary-100 text-primary-600' : 'bg-secondary-100 text-secondary-500 group-hover:bg-primary-100 group-hover:text-primary-600' }} transition-all duration-200 mr-3">
                        <i class="fas fa-file-contract text-sm"></i>
                    </div>
                    <span class="font-medium">Contracts</span>
                </a>

                <!-- Projects Section Header -->
                <div class="px-3 py-2 mb-3 mt-6">
                    <h3 class="text-xs font-semibold text-secondary-500 uppercase tracking-wider">Projects & Time</h3>
                </div>

                <a href="{{ route('projects.index') }}"
                   class="nav-link {{ request()->routeIs('projects.*') ? 'nav-link-active' : 'nav-link-inactive' }}">
                    <div class="flex items-center justify-center w-10 h-10 rounded-xl {{ request()->routeIs('projects.*') ? 'bg-primary-100 text-primary-600' : 'bg-secondary-100 text-secondary-500 group-hover:bg-primary-100 group-hover:text-primary-600' }} transition-all duration-200 mr-3">
                        <i class="fas fa-project-diagram text-sm"></i>
                    </div>
                    <span class="font-medium">Projects</span>
                </a>

                <a href="{{ route('time-tracking.index') }}"
                   class="nav-link {{ request()->routeIs('time-tracking.*') ? 'nav-link-active' : 'nav-link-inactive' }}">
                    <div class="flex items-center justify-center w-10 h-10 rounded-xl {{ request()->routeIs('time-tracking.*') ? 'bg-primary-100 text-primary-600' : 'bg-secondary-100 text-secondary-500 group-hover:bg-primary-100 group-hover:text-primary-600' }} transition-all duration-200 mr-3">
                        <i class="fas fa-clock text-sm"></i>
                    </div>
                    <span class="font-medium">Time Tracking</span>
                </a>

                <!-- Finance Section Header -->
                <div class="px-3 py-2 mb-3 mt-6">
                    <h3 class="text-xs font-semibold text-secondary-500 uppercase tracking-wider">Finance & Files</h3>
                </div>

                <a href="{{ route('expenses.index') }}"
                   class="nav-link {{ request()->routeIs('expenses.index') || request()->routeIs('expenses.create') || request()->routeIs('expenses.show') || request()->routeIs('expenses.edit') ? 'nav-link-active' : 'nav-link-inactive' }}">
                    <div class="flex items-center justify-center w-10 h-10 rounded-xl {{ request()->routeIs('expenses.index') || request()->routeIs('expenses.create') || request()->routeIs('expenses.show') || request()->routeIs('expenses.edit') ? 'bg-primary-100 text-primary-600' : 'bg-secondary-100 text-secondary-500 group-hover:bg-primary-100 group-hover:text-primary-600' }} transition-all duration-200 mr-3">
                        <i class="fas fa-receipt text-sm"></i>
                    </div>
                    <span class="font-medium">Expenses</span>
                </a>

                <a href="{{ route('files.index') }}"
                   class="nav-link {{ request()->routeIs('files.*') ? 'nav-link-active' : 'nav-link-inactive' }}">
                    <div class="flex items-center justify-center w-10 h-10 rounded-xl {{ request()->routeIs('files.*') ? 'bg-primary-100 text-primary-600' : 'bg-secondary-100 text-secondary-500 group-hover:bg-primary-100 group-hover:text-primary-600' }} transition-all duration-200 mr-3">
                        <i class="fas fa-folder text-sm"></i>
                    </div>
                    <span class="font-medium">File Manager</span>
                </a>

                <!-- Settings Section Header -->
                <div class="px-3 py-2 mb-3 mt-6">
                    <h3 class="text-xs font-semibold text-secondary-500 uppercase tracking-wider">Account</h3>
                </div>

                <a href="{{ route('profile.edit') }}"
                   class="nav-link {{ request()->routeIs('profile.*') ? 'nav-link-active' : 'nav-link-inactive' }}">
                    <div class="flex items-center justify-center w-10 h-10 rounded-xl {{ request()->routeIs('profile.*') ? 'bg-primary-100 text-primary-600' : 'bg-secondary-100 text-secondary-500 group-hover:bg-primary-100 group-hover:text-primary-600' }} transition-all duration-200 mr-3">
                        <i class="fas fa-user-cog text-sm"></i>
                    </div>
                    <span class="font-medium">Settings</span>
                </a>

                <form method="POST" action="{{ route('logout') }}" id="logout-form">
                    @csrf
                    <button type="submit" onclick="return confirmLogout(event)"
                            class="w-full nav-link nav-link-inactive group text-left">
                        <div class="flex items-center justify-center w-10 h-10 rounded-xl bg-secondary-100 text-secondary-500 group-hover:bg-danger-100 group-hover:text-danger-600 transition-all duration-200 mr-3">
                            <i class="fas fa-sign-out-alt text-sm"></i>
                        </div>
                        <span class="font-medium">Logout</span>
                    </button>
                </form>

            @endif

            <script>
            function confirmLogout(event) {
                event.preventDefault();
                if (confirm('Are you sure you want to logout?')) {
                    const button = event.target.closest('button');
                    const originalText = button.innerHTML;
                    button.innerHTML = '<div class="flex items-center justify-center w-10 h-10 rounded-xl bg-danger-100 text-danger-600 mr-3"><i class="fas fa-spinner fa-spin text-sm"></i></div><span class="font-medium">Logging out...</span>';
                    button.disabled = true;
                    document.getElementById('logout-form').submit();
                }
                return false;
            }
            </script>
            </nav>
        </div>


    </div>

    <!-- Main Content Area -->
    <div class="flex-1 flex flex-col h-screen overflow-hidden">
        <!-- App Header -->
        <x-app-header :title="$title ?? ''" :subtitle="$subtitle ?? ''" />

        <!-- Page Content -->
        <main class="flex-1 overflow-y-auto scrollbar-thin">
            {{ $slot }}
        </main>
    </div>
</div>
