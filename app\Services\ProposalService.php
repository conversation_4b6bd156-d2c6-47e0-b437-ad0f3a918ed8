<?php

namespace App\Services;

use App\Models\Proposal;
use App\Models\ProposalTemplate;
use App\Models\User;
use App\Models\Client;
use App\Models\Project;
use App\Models\Contract;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ProposalService
{
    /**
     * Get proposals for user with filtering and pagination.
     */
    public function getProposalsForUser(int $userId, Request $request = null): LengthAwarePaginator
    {
        $query = Proposal::where('user_id', $userId)
            ->forListing()
            ->orderBy('created_at', 'desc');

        if ($request) {
            // Search filter
            if ($request->filled('search')) {
                $search = $request->get('search');
                $query->where(function ($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                      ->orWhere('proposal_number', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%")
                      ->orWhereHas('client', function ($clientQuery) use ($search) {
                          $clientQuery->where('name', 'like', "%{$search}%")
                                     ->orWhere('company_name', 'like', "%{$search}%");
                      });
                });
            }

            // Status filter
            if ($request->filled('status')) {
                $query->where('status', $request->get('status'));
            }

            // Client filter
            if ($request->filled('client_id')) {
                $query->where('client_id', $request->get('client_id'));
            }

            // Date range filter
            if ($request->filled('date_from')) {
                $query->whereDate('created_at', '>=', $request->get('date_from'));
            }
            if ($request->filled('date_to')) {
                $query->whereDate('created_at', '<=', $request->get('date_to'));
            }
        }

        return $query->paginate(15);
    }

    /**
     * Create a new proposal.
     */
    public function createProposal(array $data): Proposal
    {
        $data['user_id'] = Auth::id();
        
        // Set default valid_until if not provided (30 days from now)
        if (!isset($data['valid_until'])) {
            $data['valid_until'] = now()->addDays(30);
        }

        return DB::transaction(function () use ($data) {
            $proposal = Proposal::create($data);
            
            // If created from template, increment usage count
            if (isset($data['proposal_template_id']) && $data['proposal_template_id']) {
                $template = ProposalTemplate::find($data['proposal_template_id']);
                if ($template) {
                    $template->incrementUsage();
                }
            }
            
            return $proposal;
        });
    }

    /**
     * Update proposal.
     */
    public function updateProposal(Proposal $proposal, array $data): Proposal
    {
        $proposal->update($data);
        return $proposal->fresh();
    }

    /**
     * Delete proposal.
     */
    public function deleteProposal(Proposal $proposal): array
    {
        try {
            // Check if proposal has been converted to projects/contracts
            if ($proposal->projects()->exists() || $proposal->contracts()->exists()) {
                return [
                    'success' => false,
                    'message' => 'Cannot delete proposal that has been converted to projects or contracts.'
                ];
            }

            $proposal->delete();
            
            return [
                'success' => true,
                'message' => 'Proposal deleted successfully.'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error deleting proposal: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send proposal to client.
     */
    public function sendProposal(Proposal $proposal): array
    {
        try {
            if (!$proposal->canBeSent()) {
                return [
                    'success' => false,
                    'message' => 'Proposal cannot be sent in its current state.'
                ];
            }

            $proposal->update([
                'status' => 'sent',
                'sent_date' => now()
            ]);

            // TODO: Implement email sending logic here
            // This would integrate with your email service

            return [
                'success' => true,
                'message' => 'Proposal sent successfully.'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error sending proposal: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Mark proposal as viewed by client.
     */
    public function markAsViewed(Proposal $proposal): void
    {
        if ($proposal->status === 'sent' && !$proposal->viewed_date) {
            $proposal->update([
                'status' => 'viewed',
                'viewed_date' => now()
            ]);
        }
    }

    /**
     * Accept proposal.
     */
    public function acceptProposal(Proposal $proposal, array $data = []): array
    {
        try {
            $proposal->update([
                'status' => 'accepted',
                'responded_date' => now(),
                'client_notes' => $data['client_notes'] ?? null
            ]);

            // If this proposal is linked to a lead, auto-convert the lead to client
            if ($proposal->lead_id && $proposal->lead) {
                $lead = $proposal->lead;
                if ($lead->status === 'active') {
                    $client = $lead->handleProposalAcceptance($proposal);

                    return [
                        'success' => true,
                        'message' => 'Proposal accepted successfully. Lead has been converted to client.',
                        'client_created' => true,
                        'client' => $client
                    ];
                }
            }

            return [
                'success' => true,
                'message' => 'Proposal accepted successfully.'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error accepting proposal: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Reject proposal.
     */
    public function rejectProposal(Proposal $proposal, array $data = []): array
    {
        try {
            $proposal->update([
                'status' => 'rejected',
                'responded_date' => now(),
                'client_notes' => $data['client_notes'] ?? null
            ]);

            return [
                'success' => true,
                'message' => 'Proposal rejected.'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error rejecting proposal: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Convert proposal to project.
     */
    public function convertToProject(Proposal $proposal, array $projectData = []): array
    {
        try {
            if (!$proposal->canBeConverted()) {
                return [
                    'success' => false,
                    'message' => 'Only accepted proposals can be converted to projects.'
                ];
            }

            $project = DB::transaction(function () use ($proposal, $projectData) {
                $defaultData = [
                    'user_id' => $proposal->user_id,
                    'client_id' => $proposal->client_id,
                    'proposal_id' => $proposal->id,
                    'name' => $projectData['name'] ?? $proposal->title,
                    'description' => $projectData['description'] ?? $proposal->description,
                    'budget' => $projectData['budget'] ?? $proposal->total_amount,
                    'status' => 'active',
                    'start_date' => $projectData['start_date'] ?? now(),
                ];

                return Project::create(array_merge($defaultData, $projectData));
            });

            return [
                'success' => true,
                'message' => 'Proposal converted to project successfully.',
                'project' => $project
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error converting proposal to project: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Convert proposal to contract.
     */
    public function convertToContract(Proposal $proposal, array $contractData = []): array
    {
        try {
            if (!$proposal->canBeConverted()) {
                return [
                    'success' => false,
                    'message' => 'Only accepted proposals can be converted to contracts.'
                ];
            }

            $contract = DB::transaction(function () use ($proposal, $contractData) {
                $defaultData = [
                    'user_id' => $proposal->user_id,
                    'client_id' => $proposal->client_id,
                    'proposal_id' => $proposal->id,
                    'title' => $contractData['title'] ?? $proposal->title,
                    'content' => $contractData['content'] ?? $proposal->content,
                    'status' => 'draft',
                ];

                return Contract::create(array_merge($defaultData, $contractData));
            });

            return [
                'success' => true,
                'message' => 'Proposal converted to contract successfully.',
                'contract' => $contract
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error converting proposal to contract: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Create revision of proposal.
     */
    public function createRevision(Proposal $proposal, array $data): Proposal
    {
        $revisionData = array_merge($proposal->toArray(), $data);
        $revisionData['parent_proposal_id'] = $proposal->id;
        $revisionData['version'] = $proposal->version + 1;
        $revisionData['status'] = 'draft';
        $revisionData['sent_date'] = null;
        $revisionData['viewed_date'] = null;
        $revisionData['responded_date'] = null;
        
        // Remove timestamps and id
        unset($revisionData['id'], $revisionData['created_at'], $revisionData['updated_at']);

        return $this->createProposal($revisionData);
    }

    /**
     * Check if user can create proposals.
     */
    public function canCreateProposal(int $userId): bool
    {
        return PlanChecker::canCreateProposal(User::find($userId));
    }

    /**
     * Get proposal statistics for user.
     */
    public function getProposalStats(int $userId): array
    {
        $proposals = Proposal::where('user_id', $userId);
        
        return [
            'total' => $proposals->count(),
            'draft' => $proposals->where('status', 'draft')->count(),
            'sent' => $proposals->where('status', 'sent')->count(),
            'accepted' => $proposals->where('status', 'accepted')->count(),
            'rejected' => $proposals->where('status', 'rejected')->count(),
            'acceptance_rate' => $this->calculateAcceptanceRate($userId),
            'total_value' => $proposals->where('status', 'accepted')->sum('total_amount'),
        ];
    }

    /**
     * Calculate proposal acceptance rate.
     */
    private function calculateAcceptanceRate(int $userId): float
    {
        $sentProposals = Proposal::where('user_id', $userId)
            ->whereIn('status', ['accepted', 'rejected'])
            ->count();
            
        if ($sentProposals === 0) {
            return 0;
        }
        
        $acceptedProposals = Proposal::where('user_id', $userId)
            ->where('status', 'accepted')
            ->count();
            
        return round(($acceptedProposals / $sentProposals) * 100, 1);
    }
}
