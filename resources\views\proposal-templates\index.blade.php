<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Proposal Templates') }}
            </h2>
            <a href="{{ route('proposal-templates.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                <i class="fas fa-plus mr-2"></i>Create Template
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Filters -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <form method="GET" action="{{ route('proposal-templates.index') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                            <input type="text" name="search" id="search" value="{{ request('search') }}" 
                                   placeholder="Search templates..." 
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        </div>

                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700">Category</label>
                            <select name="category" id="category" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">All Categories</option>
                                @foreach($categories as $key => $label)
                                    <option value="{{ $key }}" {{ request('category') === $key ? 'selected' : '' }}>
                                        {{ $label }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div>
                            <label for="type" class="block text-sm font-medium text-gray-700">Type</label>
                            <select name="type" id="type" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">All Types</option>
                                <option value="system" {{ request('type') === 'system' ? 'selected' : '' }}>System Templates</option>
                                <option value="user" {{ request('type') === 'user' ? 'selected' : '' }}>My Templates</option>
                            </select>
                        </div>

                        <div class="flex items-end">
                            <button type="submit" class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                <i class="fas fa-search mr-2"></i>Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Templates Grid -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    @if($templates->count() > 0)
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            @foreach($templates as $template)
                                <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                                    <div class="flex justify-between items-start mb-4">
                                        <div class="flex-1">
                                            <h3 class="text-lg font-medium text-gray-900 mb-2">
                                                <a href="{{ route('proposal-templates.show', $template) }}" 
                                                   class="text-blue-600 hover:text-blue-900">
                                                    {{ $template->name }}
                                                </a>
                                            </h3>
                                            @if($template->description)
                                                <p class="text-sm text-gray-600 mb-3">{{ Str::limit($template->description, 100) }}</p>
                                            @endif
                                        </div>
                                        
                                        @if($template->is_system_template)
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                                System
                                            </span>
                                        @else
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                                Custom
                                            </span>
                                        @endif
                                    </div>

                                    <div class="space-y-2 mb-4">
                                        @if($template->category)
                                            <div class="flex items-center text-sm text-gray-500">
                                                <i class="fas fa-tag mr-2"></i>
                                                {{ $categories[$template->category] ?? ucfirst($template->category) }}
                                            </div>
                                        @endif
                                        
                                        <div class="flex items-center text-sm text-gray-500">
                                            <i class="fas fa-chart-bar mr-2"></i>
                                            Used {{ $template->usage_count }} times
                                        </div>

                                        @if($template->tags && count($template->tags) > 0)
                                            <div class="flex flex-wrap gap-1 mt-2">
                                                @foreach(array_slice($template->tags, 0, 3) as $tag)
                                                    <span class="inline-flex px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-700">
                                                        {{ $tag }}
                                                    </span>
                                                @endforeach
                                                @if(count($template->tags) > 3)
                                                    <span class="inline-flex px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-700">
                                                        +{{ count($template->tags) - 3 }} more
                                                    </span>
                                                @endif
                                            </div>
                                        @endif
                                    </div>

                                    <div class="flex justify-between items-center">
                                        <div class="flex space-x-2">
                                            <a href="{{ route('proposal-templates.show', $template) }}" 
                                               class="text-blue-600 hover:text-blue-900 text-sm" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            
                                            @if(!$template->is_system_template && $template->user_id === auth()->id())
                                                <a href="{{ route('proposal-templates.edit', $template) }}" 
                                                   class="text-indigo-600 hover:text-indigo-900 text-sm" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form method="POST" action="{{ route('proposal-templates.destroy', $template) }}" 
                                                      class="inline" onsubmit="return confirm('Are you sure you want to delete this template?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="text-red-600 hover:text-red-900 text-sm" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                        
                                        <a href="{{ route('proposals.create', ['template_id' => $template->id]) }}" 
                                           class="bg-blue-500 hover:bg-blue-700 text-white text-xs font-bold py-1 px-3 rounded">
                                            Use Template
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <!-- Pagination -->
                        <div class="mt-6">
                            {{ $templates->withQueryString()->links() }}
                        </div>
                    @else
                        <div class="text-center py-12">
                            <i class="fas fa-file-alt text-gray-400 text-6xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
                            <p class="text-gray-500 mb-6">Create your first template to speed up proposal creation.</p>
                            <a href="{{ route('proposal-templates.create') }}" 
                               class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                <i class="fas fa-plus mr-2"></i>Create Template
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
