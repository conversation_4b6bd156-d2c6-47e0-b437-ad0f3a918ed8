@extends('emails.layouts.app')

@section('content')
    <!-- Password Reset Header -->
    <div style="text-align: center; margin-bottom: 32px;">
        <div style="font-size: 64px; margin-bottom: 16px;">🔑</div>
        <h2 style="color: #f59e0b; margin-bottom: 8px;">Reset Your Password</h2>
        <p class="lead" style="color: #d97706;">
            Hi {{ $user->name }}, we received a request to reset your password. No worries - we'll help you get back in!
        </p>
    </div>
    
    <!-- Reset Instructions -->
    <p style="font-size: 18px; color: #374151; margin-bottom: 24px;">
        Someone (hopefully you!) requested a password reset for your {{ config('app.name') }} account. 
        If this was you, click the button below to create a new password.
    </p>
    
    <!-- Security Alert -->
    <div class="warning-box">
        <h3 style="color: #92400e; margin: 0 0 12px 0;">🔒 Security Alert</h3>
        <p style="color: #b45309; margin: 0 0 16px 0;">
            This password reset was requested on {{ now()->format('M d, Y \a\t g:i A') }} from IP address: {{ request()->ip() ?? 'Unknown' }}
        </p>
        <div style="background-color: rgba(255, 255, 255, 0.7); padding: 16px; border-radius: 8px;">
            <p style="color: #b45309; margin: 0; font-size: 14px;">
                <strong>If you didn't request this reset:</strong> Your account may be at risk. Please contact our security team immediately and do not click the reset button.
            </p>
        </div>
    </div>
    
    <!-- Reset Button -->
    <div style="text-align: center; margin: 40px 0;">
        <a href="{{ $resetUrl }}" class="btn btn-primary" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); font-size: 18px; padding: 18px 36px; text-decoration: none;">
            🔑 Reset My Password
        </a>
        <p style="color: #64748b; font-size: 14px; margin-top: 16px;">
            This reset link will expire in 60 minutes for security reasons.
        </p>
    </div>
    
    <!-- Alternative Reset -->
    <div class="highlight-box">
        <h3>📱 Can't Click the Button?</h3>
        <p>If you're having trouble clicking the reset button, copy and paste this link into your browser:</p>
        <div style="background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 16px; margin: 16px 0; word-break: break-all; font-family: monospace; font-size: 14px; color: #475569;">
            {{ $resetUrl }}
        </div>
    </div>
    
    <!-- Password Security Tips -->
    <h3>🛡️ Creating a Strong Password</h3>
    <p>When creating your new password, follow these security best practices:</p>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 16px; margin: 24px 0;">
        <div style="background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); padding: 20px; border-radius: 12px; border: 1px solid #bbf7d0;">
            <div style="color: #166534; font-size: 24px; margin-bottom: 8px;">🔢</div>
            <h4 style="margin: 0 0 8px 0; color: #166534; font-size: 16px;">Use 12+ Characters</h4>
            <p style="margin: 0; color: #15803d; font-size: 14px;">Longer passwords are exponentially harder to crack</p>
        </div>
        
        <div style="background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%); padding: 20px; border-radius: 12px; border: 1px solid #bae6fd;">
            <div style="color: #1e40af; font-size: 24px; margin-bottom: 8px;">🔤</div>
            <h4 style="margin: 0 0 8px 0; color: #1e40af; font-size: 16px;">Mix Character Types</h4>
            <p style="margin: 0; color: #2563eb; font-size: 14px;">Combine uppercase, lowercase, numbers, and symbols</p>
        </div>
        
        <div style="background: linear-gradient(135deg, #fdf4ff 0%, #fae8ff 100%); padding: 20px; border-radius: 12px; border: 1px solid #f5d0fe;">
            <div style="color: #a21caf; font-size: 24px; margin-bottom: 8px;">🚫</div>
            <h4 style="margin: 0 0 8px 0; color: #a21caf; font-size: 16px;">Avoid Common Words</h4>
            <p style="margin: 0; color: #c026d3; font-size: 14px;">Don't use dictionary words or personal information</p>
        </div>
        
        <div style="background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%); padding: 20px; border-radius: 12px; border: 1px solid #fde68a;">
            <div style="color: #92400e; font-size: 24px; margin-bottom: 8px;">🔐</div>
            <h4 style="margin: 0 0 8px 0; color: #92400e; font-size: 16px;">Use Unique Passwords</h4>
            <p style="margin: 0; color: #b45309; font-size: 14px;">Never reuse passwords across different accounts</p>
        </div>
    </div>
    
    <!-- What Happens Next -->
    <div class="info-box">
        <h3>🎯 What Happens Next?</h3>
        <p>After clicking the reset button, you'll be taken to a secure page where you can:</p>
        <ol style="margin: 16px 0; padding-left: 20px; color: #2563eb;">
            <li style="margin-bottom: 8px;"><strong>Create a new password</strong> - Choose a strong, unique password</li>
            <li style="margin-bottom: 8px;"><strong>Confirm your password</strong> - Make sure you typed it correctly</li>
            <li style="margin-bottom: 8px;"><strong>Sign in immediately</strong> - You'll be automatically logged in</li>
            <li><strong>Update your devices</strong> - You may need to sign in again on other devices</li>
        </ol>
    </div>
    
    <!-- Didn't Request This -->
    <div style="background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%); border: 1px solid #f87171; border-radius: 12px; padding: 24px; margin: 24px 0;">
        <h3 style="color: #dc2626; margin: 0 0 12px 0;">🚨 Didn't Request This Reset?</h3>
        <p style="color: #dc2626; margin: 0 0 16px 0;">
            If you didn't request a password reset, your account may be at risk. Here's what you should do:
        </p>
        <ul style="color: #dc2626; margin: 16px 0; padding-left: 20px;">
            <li style="margin-bottom: 8px;"><strong>Don't click the reset button</strong> - Ignore this email completely</li>
            <li style="margin-bottom: 8px;"><strong>Check your account</strong> - Sign in normally and review recent activity</li>
            <li style="margin-bottom: 8px;"><strong>Contact security</strong> - Report this incident to our security team</li>
            <li><strong>Consider changing your password</strong> - Update it through your account settings</li>
        </ul>
        <div style="text-align: center; margin-top: 20px;">
            <a href="mailto:security@{{ config('app.domain', 'freeligo.com') }}" class="btn btn-secondary" style="background-color: #dc2626; color: white;">
                🚨 Report Security Issue
            </a>
        </div>
    </div>
    
    <!-- Account Security -->
    <h3>🔒 Keep Your Account Secure</h3>
    <p>Here are some additional ways to protect your {{ config('app.name') }} account:</p>
    
    <div style="background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px; margin: 20px 0;">
        <div style="display: flex; flex-wrap: wrap; gap: 20px;">
            <div style="flex: 1; min-width: 200px;">
                <h4 style="color: #1e293b; margin: 0 0 8px 0; font-size: 16px;">🔐 Enable Two-Factor Authentication</h4>
                <p style="color: #64748b; margin: 0; font-size: 14px;">Add an extra layer of security to your account</p>
            </div>
            <div style="flex: 1; min-width: 200px;">
                <h4 style="color: #1e293b; margin: 0 0 8px 0; font-size: 16px;">📧 Keep Email Secure</h4>
                <p style="color: #64748b; margin: 0; font-size: 14px;">Use a strong password for your email account too</p>
            </div>
            <div style="flex: 1; min-width: 200px;">
                <h4 style="color: #1e293b; margin: 0 0 8px 0; font-size: 16px;">👀 Monitor Activity</h4>
                <p style="color: #64748b; margin: 0; font-size: 14px;">Regularly check your account for suspicious activity</p>
            </div>
        </div>
    </div>
    
    <!-- Support Section -->
    <div style="text-align: center; background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; padding: 32px; margin: 32px 0;">
        <div style="font-size: 48px; margin-bottom: 16px;">🎧</div>
        <h3 style="color: #1e293b; margin: 0 0 12px 0;">Need Help with Password Reset?</h3>
        <p style="color: #64748b; margin: 0 0 24px 0;">
            If you're having trouble resetting your password or have security concerns, our support team is here to help.
        </p>
        
        <div style="display: flex; flex-wrap: wrap; gap: 12px; justify-content: center;">
            <a href="mailto:support@{{ config('app.domain', 'freeligo.com') }}" class="btn btn-outline">
                📧 Email Support
            </a>
            <a href="#" class="btn btn-secondary">
                💬 Live Chat
            </a>
        </div>
        
        <p style="color: #64748b; font-size: 14px; margin: 20px 0 0 0;">
            <strong>Support Hours:</strong> Monday - Friday, 9:00 AM - 6:00 PM IST
        </p>
    </div>
    
    <!-- Expiration Notice -->
    <div style="text-align: center; margin: 40px 0;">
        <h3 style="color: #f59e0b; margin-bottom: 16px;">⏰ Time Sensitive</h3>
        <p style="color: #64748b; font-size: 16px; margin-bottom: 20px;">
            This password reset link will expire in <strong>60 minutes</strong> for your security.
        </p>
        <p style="color: #64748b; font-size: 14px;">
            If the link expires, you can request a new password reset from the login page.
        </p>
    </div>
    
    <!-- Footer Note -->
    <div style="border-top: 1px solid #e2e8f0; padding-top: 20px; margin-top: 32px;">
        <p style="color: #64748b; font-size: 14px; text-align: center; margin: 0;">
            This password reset was requested for {{ $user->email }} on {{ now()->format('M d, Y \a\t g:i A') }}.
            <br>
            If you have security concerns, please contact our security team at 
            <a href="mailto:security@{{ config('app.domain', 'freeligo.com') }}" style="color: #dc2626; text-decoration: none;">security@{{ config('app.domain', 'freeligo.com') }}</a>.
        </p>
    </div>
@endsection
