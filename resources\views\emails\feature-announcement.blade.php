@extends('emails.layouts.app')

@section('content')
    <!-- Feature Announcement Header -->
    <div style="text-align: center; margin-bottom: 32px;">
        <div style="font-size: 64px; margin-bottom: 16px;">🚀</div>
        <h2 style="color: #10b981; margin-bottom: 8px;">{{ $feature['title'] ?? 'New Feature Available!' }}</h2>
        <p class="lead" style="color: #059669;">
            Hi {{ $user->name }}, we're excited to share something new that will help you grow your business even faster!
        </p>
    </div>
    
    <!-- Feature Introduction -->
    <p style="font-size: 18px; color: #374151; margin-bottom: 24px;">
        {{ $feature['description'] ?? 'We\'ve been working hard to bring you new features that make managing your business easier and more efficient.' }}
    </p>
    
    <!-- Feature Highlight -->
    <div style="background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); border: 2px solid #10b981; border-radius: 16px; padding: 32px; margin: 32px 0; text-align: center;">
        <div style="font-size: 48px; margin-bottom: 16px;">{{ $feature['icon'] ?? '✨' }}</div>
        <h3 style="color: #166534; margin: 0 0 16px 0; font-size: 24px;">{{ $feature['title'] ?? 'Amazing New Feature' }}</h3>
        <p style="color: #15803d; margin: 0 0 24px 0; font-size: 18px;">
            {{ $feature['tagline'] ?? 'Designed to make your business management effortless' }}
        </p>
        
        @if(isset($feature['cta_url']))
            <a href="{{ $feature['cta_url'] }}" class="btn btn-primary" style="font-size: 18px; padding: 16px 32px;">
                {{ $feature['cta_text'] ?? 'Try It Now' }}
            </a>
        @endif
    </div>
    
    <!-- Feature Benefits -->
    <h3>🎯 What This Means for You</h3>
    <p>Here's how this new feature will benefit your business:</p>
    
    @if(isset($feature['benefits']) && is_array($feature['benefits']))
        <ul class="feature-list">
            @foreach($feature['benefits'] as $benefit)
                <li>{{ $benefit }}</li>
            @endforeach
        </ul>
    @else
        <ul class="feature-list">
            <li><strong>Save Time</strong> - Automate repetitive tasks and focus on growing your business</li>
            <li><strong>Increase Efficiency</strong> - Streamlined workflows that get things done faster</li>
            <li><strong>Better Insights</strong> - Make data-driven decisions with improved analytics</li>
            <li><strong>Enhanced Experience</strong> - Enjoy a smoother, more intuitive interface</li>
        </ul>
    @endif
    
    <!-- How It Works -->
    @if(isset($feature['how_it_works']) && is_array($feature['how_it_works']))
        <h3>🔧 How It Works</h3>
        <p>Getting started with this feature is simple:</p>
        
        <div style="margin: 24px 0;">
            @foreach($feature['how_it_works'] as $index => $step)
                <div style="display: flex; align-items: flex-start; margin-bottom: 20px; padding: 16px; background-color: #f8fafc; border-radius: 12px; border-left: 4px solid #10b981;">
                    <div style="background-color: #10b981; color: white; width: 32px; height: 32px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 16px; flex-shrink: 0;">{{ $index + 1 }}</div>
                    <div>
                        <p style="margin: 0; color: #1e293b; font-weight: 600;">{{ $step }}</p>
                    </div>
                </div>
            @endforeach
        </div>
    @endif
    
    <!-- Feature Showcase -->
    @if(isset($feature['showcase_items']) && is_array($feature['showcase_items']))
        <h3>✨ Feature Highlights</h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 16px; margin: 24px 0;">
            @foreach($feature['showcase_items'] as $item)
                <div style="background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); padding: 20px; border-radius: 12px; border: 1px solid #e2e8f0;">
                    <div style="color: #10b981; font-size: 24px; margin-bottom: 8px;">{{ $item['icon'] ?? '⭐' }}</div>
                    <h4 style="margin: 0 0 8px 0; color: #1e293b; font-size: 16px;">{{ $item['title'] ?? 'Feature' }}</h4>
                    <p style="margin: 0; color: #64748b; font-size: 14px;">{{ $item['description'] ?? 'Amazing functionality' }}</p>
                </div>
            @endforeach
        </div>
    @endif
    
    <!-- Call to Action -->
    <div style="text-align: center; margin: 40px 0;">
        @if(isset($feature['cta_url']))
            <a href="{{ $feature['cta_url'] }}" class="btn btn-primary" style="font-size: 18px; padding: 18px 36px;">
                🚀 {{ $feature['cta_text'] ?? 'Explore New Feature' }}
            </a>
        @else
            <a href="{{ route('dashboard') }}" class="btn btn-primary" style="font-size: 18px; padding: 18px 36px;">
                🚀 Go to Dashboard
            </a>
        @endif
        
        <p style="color: #64748b; font-size: 14px; margin-top: 16px;">
            Available now in your {{ config('app.name') }} dashboard
        </p>
    </div>
    
    <!-- User Feedback -->
    @if(isset($feature['show_feedback']) && $feature['show_feedback'])
        <div class="info-box">
            <h3>💬 We'd Love Your Feedback</h3>
            <p>Your opinion matters to us! Try out the new feature and let us know what you think:</p>
            
            <div style="text-align: center; margin: 20px 0;">
                <a href="mailto:feedback@{{ config('app.domain', 'freeligo.com') }}?subject=Feedback: {{ $feature['title'] ?? 'New Feature' }}" class="btn btn-outline">
                    📝 Send Feedback
                </a>
                <a href="#" class="btn btn-secondary">
                    ⭐ Rate Feature
                </a>
            </div>
        </div>
    @endif
    
    <!-- Additional Resources -->
    @if(isset($feature['resources']) && is_array($feature['resources']))
        <h3>📚 Learn More</h3>
        <p>Get the most out of this new feature with these helpful resources:</p>
        
        <div style="display: flex; flex-wrap: wrap; gap: 16px; margin: 20px 0;">
            @foreach($feature['resources'] as $resource)
                <div style="flex: 1; min-width: 200px; background-color: #f8fafc; padding: 20px; border-radius: 12px; text-align: center;">
                    <div style="font-size: 32px; margin-bottom: 12px;">{{ $resource['icon'] ?? '📖' }}</div>
                    <h4 style="margin: 0 0 8px 0; color: #1e293b; font-size: 16px;">{{ $resource['title'] ?? 'Resource' }}</h4>
                    <p style="margin: 0 0 12px 0; color: #64748b; font-size: 14px;">{{ $resource['description'] ?? 'Helpful information' }}</p>
                    @if(isset($resource['url']))
                        <a href="{{ $resource['url'] }}" style="color: #10b981; text-decoration: none; font-weight: 600;">{{ $resource['link_text'] ?? 'Learn More' }} →</a>
                    @endif
                </div>
            @endforeach
        </div>
    @endif
    
    <!-- What's Coming Next -->
    @if(isset($feature['roadmap_preview']) && is_array($feature['roadmap_preview']))
        <div class="highlight-box">
            <h3>🔮 What's Coming Next</h3>
            <p>We're not stopping here! Here's a sneak peek at what we're working on:</p>
            
            <div style="margin: 20px 0;">
                @foreach($feature['roadmap_preview'] as $upcoming)
                    <div style="display: flex; align-items: center; margin-bottom: 12px; padding: 12px; background-color: rgba(16, 185, 129, 0.1); border-radius: 8px;">
                        <div style="color: #10b981; font-size: 20px; margin-right: 12px;">{{ $upcoming['icon'] ?? '🔜' }}</div>
                        <div>
                            <h4 style="margin: 0; color: #1e293b; font-size: 14px;">{{ $upcoming['title'] ?? 'Upcoming Feature' }}</h4>
                            <p style="margin: 0; color: #64748b; font-size: 12px;">{{ $upcoming['description'] ?? 'Coming soon' }}</p>
                        </div>
                    </div>
                @endforeach
            </div>
            
            <p style="color: #64748b; font-size: 14px; margin: 16px 0 0 0; text-align: center;">
                <em>Stay tuned for more exciting updates!</em>
            </p>
        </div>
    @endif
    
    <!-- Support Section -->
    <div style="text-align: center; background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; padding: 32px; margin: 32px 0;">
        <div style="font-size: 48px; margin-bottom: 16px;">🎧</div>
        <h3 style="color: #1e293b; margin: 0 0 12px 0;">Need Help with the New Feature?</h3>
        <p style="color: #64748b; margin: 0 0 24px 0;">
            Our support team is ready to help you get the most out of this new feature. We're here to answer any questions you might have.
        </p>
        
        <div style="display: flex; flex-wrap: wrap; gap: 12px; justify-content: center;">
            <a href="mailto:support@{{ config('app.domain', 'freeligo.com') }}" class="btn btn-outline">
                📧 Email Support
            </a>
            <a href="#" class="btn btn-secondary">
                💬 Live Chat
            </a>
            <a href="#" class="btn btn-secondary">
                📚 Help Center
            </a>
        </div>
    </div>
    
    <!-- Social Sharing -->
    @if(isset($feature['social_sharing']) && $feature['social_sharing'])
        <div style="text-align: center; margin: 32px 0;">
            <h3 style="color: #1e293b; margin-bottom: 16px;">Love the New Feature? Share It! 📢</h3>
            <p style="color: #64748b; margin-bottom: 20px;">Help other business owners discover {{ config('app.name') }}</p>
            
            <div style="display: flex; flex-wrap: wrap; gap: 12px; justify-content: center;">
                <a href="https://twitter.com/intent/tweet?text=Just discovered an amazing new feature in @{{ config('app.name') }}! 🚀" class="btn btn-secondary" style="background-color: #1da1f2; color: white;">
                    🐦 Share on Twitter
                </a>
                <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode(config('app.url')) }}" class="btn btn-secondary" style="background-color: #0077b5; color: white;">
                    💼 Share on LinkedIn
                </a>
            </div>
        </div>
    @endif
    
    <!-- Thank You Message -->
    <div style="text-align: center; margin: 40px 0;">
        <h3 style="color: #1e293b; margin-bottom: 16px;">Thank You for Being Part of Our Journey! 🙏</h3>
        <p style="color: #64748b; font-size: 18px; margin-bottom: 20px;">
            Your feedback and support help us build better features that truly make a difference for your business.
        </p>
        <p style="color: #64748b; font-size: 16px;">
            Here's to your continued success with {{ config('app.name') }}!
        </p>
    </div>
    
    <!-- Footer Note -->
    <div style="border-top: 1px solid #e2e8f0; padding-top: 20px; margin-top: 32px;">
        <p style="color: #64748b; font-size: 14px; text-align: center; margin: 0;">
            This feature announcement was sent to {{ $user->email }} because you're a valued {{ config('app.name') }} user.
            <br>
            <a href="#" style="color: #64748b; text-decoration: none;">Manage email preferences</a> | 
            <a href="#" style="color: #64748b; text-decoration: none;">Unsubscribe from feature updates</a>
        </p>
    </div>
@endsection
