<?php

namespace App\Http\Controllers;

use App\Services\PlanChecker;
use App\Services\AIServiceFactory;
use App\Services\AnalyticsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AiAssistantController extends Controller
{
    protected $aiService;

    public function __construct()
    {
        $this->aiService = AIServiceFactory::create();
    }

    /**
     * Display AI Assistant dashboard.
     */
    public function index()
    {
        // Check if user can use AI assistant
        if (!PlanChecker::canUseAiAssistant()) {
            return PlanChecker::redirectToUpgrade('ai_assistant');
        }

        // Check if AI service is configured
        $provider = AIServiceFactory::getCurrentProvider();
        if (!AIServiceFactory::isProviderConfigured($provider)) {
            return view('ai-assistant.index', [
                'error' => 'AI Assistant is not properly configured. Please contact support.'
            ]);
        }

        return view('ai-assistant.index');
    }

    /**
     * Generate document content using AI.
     */
    public function generate(Request $request)
    {
        // Check if user can use AI assistant
        if (!PlanChecker::canUseAiAssistant()) {
            return response()->json(['error' => 'AI Assistant requires Business plan'], 403);
        }

        $validated = $request->validate([
            'type' => 'required|in:invoice,contract,proposal,followup',
            'prompt' => 'required|string|max:1000',
            'context' => 'nullable|array'
        ]);

        try {
            $context = $validated['context'] ?? [];
            $context['user_prompt'] = $validated['prompt'];
            $context['type'] = $validated['type'];

            // Generate content based on type
            switch ($validated['type']) {
                case 'invoice':
                    $result = $this->aiService->generateInvoiceDescription($context);
                    break;
                case 'contract':
                    $result = $this->aiService->generateContractRecommendations($context);
                    break;
                case 'followup':
                    $result = $this->aiService->generateFollowUpMessage($context);
                    break;
                case 'proposal':
                    $result = $this->aiService->generateProposalContent($context);
                    break;
                default:
                    $result = ['success' => false, 'error' => 'Invalid document type'];
            }

            if ($result['success']) {
                return response()->json([
                    'content' => $result['content'],
                    'suggestions' => $this->generateSuggestions($validated['type']),
                ]);
            } else {
                return response()->json([
                    'error' => $result['error'] ?? 'Failed to generate content'
                ], 500);
            }

        } catch (\Exception $e) {
            \Log::error('AI content generation failed', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'type' => $validated['type'] ?? 'unknown'
            ]);

            return response()->json([
                'error' => 'An error occurred while generating content. Please try again.'
            ], 500);
        }
    }

    /**
     * Analyze document for improvements.
     */
    public function analyze(Request $request)
    {
        // Check if user can use AI assistant
        if (!PlanChecker::canUseAiAssistant()) {
            return response()->json(['error' => 'AI Assistant requires Business plan'], 403);
        }

        $validated = $request->validate([
            'content' => 'required|string',
            'type' => 'required|in:invoice,contract,proposal'
        ]);

        try {
            $prompt = "Analyze this {$validated['type']} document and provide specific improvements:\n\n";
            $prompt .= $validated['content'] . "\n\n";
            $prompt .= "Provide a JSON response with 'score' (0-100), 'improvements' array, and 'strengths' array.";

            $result = $this->aiService->generateCompletion($prompt, [
                'temperature' => 0.3,
                'max_tokens' => 400,
            ]);

            if ($result['success']) {
                // Try to parse JSON response, fallback to structured format
                $analysis = $this->parseAnalysisResponse($result['content']);
                return response()->json($analysis);
            } else {
                return response()->json([
                    'error' => $result['error'] ?? 'Failed to analyze document'
                ], 500);
            }

        } catch (\Exception $e) {
            \Log::error('AI document analysis failed', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'type' => $validated['type'] ?? 'unknown',
                'content_length' => strlen($validated['content'] ?? '')
            ]);

            return response()->json([
                'error' => 'An error occurred while analyzing the document. Please try again.'
            ], 500);
        }
    }

    /**
     * Generate smart invoice description with client context.
     */
    public function generateInvoiceDescription(Request $request)
    {
        if (!PlanChecker::canUseAiAssistant()) {
            return response()->json(['error' => 'AI Assistant requires Business plan'], 403);
        }

        $validated = $request->validate([
            'client_id' => ['required', 'exists:clients,id', function ($attribute, $value, $fail) {
                $client = \App\Models\Client::find($value);
                if ($client && $client->user_id !== auth()->id()) {
                    $fail('The selected client is invalid.');
                }
            }],
            'project_type' => 'nullable|string',
            'amount' => 'nullable|numeric',
        ]);

        try {
            $user = Auth::user();
            $client = $user->clients()->findOrFail($validated['client_id']);

            // Get client history
            $previousInvoices = $client->invoices()
                ->latest()
                ->limit(5)
                ->get(['notes'])
                ->pluck('notes')
                ->filter()
                ->toArray();

            $context = [
                'client_name' => $client->name,
                'project_type' => $validated['project_type'] ?? 'project',
                'amount' => $validated['amount'] ?? 0,
                'previous_work' => $previousInvoices,
            ];

            $result = $this->aiService->generateInvoiceDescription($context);

            if ($result['success']) {
                return response()->json([
                    'description' => $result['content'],
                ]);
            } else {
                return response()->json([
                    'error' => $result['error'] ?? 'Failed to generate description'
                ], 500);
            }

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'An error occurred while generating the description'
            ], 500);
        }
    }

    /**
     * Generate AI-powered business insights.
     */
    public function generateBusinessInsights(Request $request)
    {
        if (!PlanChecker::canUseAiAssistant()) {
            return response()->json(['error' => 'AI Assistant requires Business plan'], 403);
        }

        try {
            $user = Auth::user();
            $analyticsService = new AnalyticsService($user);

            // Gather comprehensive business data
            $revenueComparison = $analyticsService->getRevenueComparison('monthly', 12);
            $clientPerformance = $analyticsService->getClientPerformanceAnalytics();
            $paymentPatterns = $analyticsService->getInvoicePaymentPatterns();
            $businessGrowth = $analyticsService->getBusinessGrowthMetrics();
            $profitability = $analyticsService->getProfitMarginAnalysis();

            // Prepare data for AI analysis
            $businessData = [
                'total_revenue' => $revenueComparison['total_revenue'] ?? 0,
                'client_count' => $clientPerformance['summary']['total_clients'] ?? 0,
                'avg_project_value' => $revenueComparison['avg_invoice_value'] ?? 0,
                'revenue_growth' => $businessGrowth['revenue_growth'] ?? 0,
                'top_clients' => array_slice(
                    array_column($clientPerformance['clients'] ?? [], 'name'),
                    0, 3
                ),
                'payment_reliability' => $paymentPatterns['on_time_payments'] ?? 0,
                'profit_margin' => $profitability['overall_profit_margin'] ?? 0,
                'monthly_trend' => $revenueComparison['growth_trend'] ?? 'stable',
            ];

            $result = $this->aiService->generateBusinessInsights($businessData);

            if ($result['success']) {
                return response()->json([
                    'insights' => $result['content'],
                    'data_summary' => $businessData,
                ]);
            } else {
                return response()->json([
                    'error' => $result['error'] ?? 'Failed to generate insights'
                ], 500);
            }

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'An error occurred while generating business insights'
            ], 500);
        }
    }

    /**
     * Generate suggestions based on document type.
     */
    private function generateSuggestions(string $type): array
    {
        $suggestions = [
            'invoice' => [
                'Include specific deliverables and milestones',
                'Add clear payment terms and due dates',
                'Consider itemizing work for transparency',
            ],
            'contract' => [
                'Define project scope and boundaries clearly',
                'Include intellectual property clauses',
                'Add cancellation and revision policies',
            ],
            'followup' => [
                'Maintain professional but friendly tone',
                'Include specific invoice details',
                'Provide clear next steps for payment',
            ],
            'proposal' => [
                'Highlight unique value proposition',
                'Include timeline and milestones',
                'Add testimonials or portfolio examples',
            ],
        ];

        return $suggestions[$type] ?? [];
    }

    /**
     * Generate proposal content using AI.
     */
    public function generateProposal(Request $request)
    {
        // Check if user can use AI assistant
        if (!PlanChecker::canUseAiAssistant()) {
            return response()->json(['error' => 'AI Assistant requires Business plan'], 403);
        }

        $validated = $request->validate([
            'client_name' => 'nullable|string|max:255',
            'project_type' => 'required|string|max:255',
            'project_description' => 'required|string|max:1000',
            'budget' => 'nullable|numeric|min:0',
            'timeline' => 'nullable|string|max:255',
            'requirements' => 'nullable|array',
            'requirements.*' => 'string|max:255',
        ]);

        try {
            $result = $this->aiService->generateProposalContent($validated);

            if ($result['success']) {
                return response()->json([
                    'content' => $result['content'],
                    'suggestions' => $this->generateSuggestions('proposal'),
                ]);
            } else {
                return response()->json([
                    'error' => $result['error'] ?? 'Failed to generate proposal content'
                ], 500);
            }

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'An error occurred while generating proposal content'
            ], 500);
        }
    }

    /**
     * Improve existing proposal content using AI.
     */
    public function improveProposal(Request $request)
    {
        // Check if user can use AI assistant
        if (!PlanChecker::canUseAiAssistant()) {
            return response()->json(['error' => 'AI Assistant requires Business plan'], 403);
        }

        $validated = $request->validate([
            'content' => 'required|string',
            'focus_areas' => 'nullable|array',
            'focus_areas.*' => 'string|in:clarity,persuasion,professionalism,structure,value_proposition',
        ]);

        try {
            $context = [];
            if (!empty($validated['focus_areas'])) {
                $context['focus_areas'] = $validated['focus_areas'];
            }

            $result = $this->aiService->improveProposalContent($validated['content'], $context);

            if ($result['success']) {
                return response()->json([
                    'improved_content' => $result['content'],
                ]);
            } else {
                return response()->json([
                    'error' => $result['error'] ?? 'Failed to improve proposal content'
                ], 500);
            }

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'An error occurred while improving proposal content'
            ], 500);
        }
    }

    /**
     * Parse AI analysis response into structured format.
     */
    private function parseAnalysisResponse(string $content): array
    {
        // Try to parse as JSON first
        $decoded = json_decode($content, true);
        if ($decoded && isset($decoded['score'])) {
            return $decoded;
        }

        // Fallback to default structure
        return [
            'score' => 75,
            'improvements' => [
                'Consider adding more specific details',
                'Review for clarity and completeness',
                'Ensure all terms are clearly defined',
            ],
            'strengths' => [
                'Professional tone maintained',
                'Clear structure and organization',
                'Appropriate level of detail',
            ],
        ];
    }
}
