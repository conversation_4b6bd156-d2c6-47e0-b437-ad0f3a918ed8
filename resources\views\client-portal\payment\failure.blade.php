@extends('client-portal.layout')

@section('content')
<div class="max-w-2xl mx-auto">
    <div class="text-center">
        <!-- Failure Icon -->
        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-6">
            <i class="fas fa-times text-red-600 text-2xl"></i>
        </div>

        <!-- Failure Message -->
        <h2 class="text-3xl font-bold text-gray-900 mb-2">Payment Failed</h2>
        <p class="text-lg text-gray-600 mb-8">
            We're sorry, but your payment could not be processed at this time.
        </p>

        <!-- Error Details -->
        <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-8 text-left">
            <h3 class="text-lg font-semibold text-red-900 mb-4">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                What happened?
            </h3>
            <p class="text-red-800">
                {{ $failureReason ?? 'The payment could not be completed. This could be due to insufficient funds, network issues, or other technical problems.' }}
            </p>
        </div>

        <!-- Invoice Details -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8 text-left">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Invoice Details</h3>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-gray-600">Invoice Number:</span>
                    <span class="font-medium text-gray-900">{{ $invoice->invoice_number }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Amount Due:</span>
                    <span class="font-medium text-gray-900">
                        {{ config('services.currency.symbol', '₹') }}{{ number_format($invoice->remaining_amount, 2) }}
                    </span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Due Date:</span>
                    <span class="font-medium text-gray-900">{{ $invoice->due_date->format('M d, Y') }}</span>
                </div>
            </div>
        </div>

        <!-- Next Steps -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8 text-left">
            <h3 class="text-lg font-semibold text-blue-900 mb-4">
                <i class="fas fa-lightbulb mr-2"></i>
                What can you do next?
            </h3>
            <ul class="text-blue-800 space-y-2 text-sm">
                <li class="flex items-start">
                    <i class="fas fa-check text-blue-600 mr-2 mt-0.5"></i>
                    Check your account balance and ensure sufficient funds
                </li>
                <li class="flex items-start">
                    <i class="fas fa-check text-blue-600 mr-2 mt-0.5"></i>
                    Verify your payment method details are correct
                </li>
                <li class="flex items-start">
                    <i class="fas fa-check text-blue-600 mr-2 mt-0.5"></i>
                    Try using a different payment method
                </li>
                <li class="flex items-start">
                    <i class="fas fa-check text-blue-600 mr-2 mt-0.5"></i>
                    Contact your bank if the issue persists
                </li>
            </ul>
        </div>

        <!-- Action Buttons -->
        <div class="space-y-4">
            <a href="{{ $invoice->payment_url }}" 
               class="w-full inline-flex justify-center items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500">
                <i class="fas fa-redo mr-2"></i>
                Try Payment Again
            </a>
            
            <a href="{{ route('client-portal.invoice.view', ['invoice' => $invoice->id, 'token' => $invoice->public_token]) }}" 
               class="w-full inline-flex justify-center items-center px-6 py-3 border border-gray-300 shadow-sm text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500">
                <i class="fas fa-file-invoice mr-2"></i>
                Back to Invoice
            </a>
        </div>

        <!-- Support Information -->
        <div class="mt-8 text-sm text-gray-500">
            <p class="mb-2">
                Need help? Contact us for assistance with your payment.
            </p>
            <div class="flex justify-center space-x-4">
                @if($invoice->user->email)
                    <a href="mailto:{{ $invoice->user->email }}" class="text-emerald-600 hover:text-emerald-700">
                        <i class="fas fa-envelope mr-1"></i>
                        Email Support
                    </a>
                @endif
                @if($invoice->user->phone)
                    <a href="tel:{{ $invoice->user->phone }}" class="text-emerald-600 hover:text-emerald-700">
                        <i class="fas fa-phone mr-1"></i>
                        Call Support
                    </a>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
