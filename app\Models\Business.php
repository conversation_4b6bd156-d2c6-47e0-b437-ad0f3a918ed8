<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Business extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'slug',
        'type',
        'email',
        'phone',
        'website',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'tax_number',
        'registration_number',
        'logo_path',
        'description',
        'industry',
        'employee_count',
        'founded_year',
        'is_active',
        'subscription_plan_id',
        'trial_ends_at',
        'settings',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'trial_ends_at' => 'datetime',
            'settings' => 'array',
            'founded_year' => 'integer',
            'employee_count' => 'integer',
        ];
    }

    /**
     * Get the business settings.
     */
    public function businessSettings(): HasOne
    {
        return $this->hasOne(BusinessSettings::class);
    }

    /**
     * Get the subscription plan.
     */
    public function subscriptionPlan(): BelongsTo
    {
        return $this->belongsTo(Plan::class, 'subscription_plan_id');
    }

    /**
     * Get the users belonging to this business.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'business_users')
            ->withPivot(['role', 'permissions', 'is_owner', 'joined_at', 'status'])
            ->withTimestamps();
    }

    /**
     * Get the business owner.
     */
    public function owner()
    {
        return $this->users()->wherePivot('is_owner', true)->first();
    }

    /**
     * Get all clients for this business.
     */
    public function clients(): HasMany
    {
        return $this->hasMany(Client::class);
    }

    /**
     * Get all invoices for this business.
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Get all projects for this business.
     */
    public function projects(): HasMany
    {
        return $this->hasMany(Project::class);
    }

    /**
     * Get all contracts for this business.
     */
    public function contracts(): HasMany
    {
        return $this->hasMany(Contract::class);
    }

    /**
     * Get all proposals for this business.
     */
    public function proposals(): HasMany
    {
        return $this->hasMany(Proposal::class);
    }

    /**
     * Get all expenses for this business.
     */
    public function expenses(): HasMany
    {
        return $this->hasMany(Expense::class);
    }

    /**
     * Get all leads for this business.
     */
    public function leads(): HasMany
    {
        return $this->hasMany(Lead::class);
    }

    /**
     * Get the subscription for this business.
     */
    public function subscription(): HasOne
    {
        return $this->hasOne(BusinessSubscription::class);
    }

    /**
     * Check if business is a freelancer type.
     */
    public function isFreelancer(): bool
    {
        return $this->type === 'freelancer';
    }

    /**
     * Check if business is a startup.
     */
    public function isStartup(): bool
    {
        return $this->type === 'startup';
    }

    /**
     * Check if business is a small business.
     */
    public function isSmallBusiness(): bool
    {
        return $this->type === 'small_business';
    }

    /**
     * Get business display name.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name;
    }

    /**
     * Generate unique slug for business.
     */
    public static function generateSlug(string $name): string
    {
        $slug = \Str::slug($name);
        $count = static::where('slug', 'like', $slug . '%')->count();

        return $count > 0 ? $slug . '-' . ($count + 1) : $slug;
    }

    /**
     * Scope for active businesses.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for freelancer businesses.
     */
    public function scopeFreelancers($query)
    {
        return $query->where('type', 'freelancer');
    }

    /**
     * Scope for startup businesses.
     */
    public function scopeStartups($query)
    {
        return $query->where('type', 'startup');
    }

    /**
     * Scope for small businesses.
     */
    public function scopeSmallBusinesses($query)
    {
        return $query->where('type', 'small_business');
    }
}
