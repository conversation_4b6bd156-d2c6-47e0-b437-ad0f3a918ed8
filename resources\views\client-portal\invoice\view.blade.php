@extends('client-portal.layout')

@section('content')
<div class="space-y-6">
    <!-- Invoice Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex justify-between items-start">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Invoice {{ $invoice->invoice_number }}</h2>
                <div class="mt-2 space-y-1">
                    <p class="text-sm text-gray-600">
                        <span class="font-medium">Issue Date:</span> {{ $invoice->invoice_date->format('M d, Y') }}
                    </p>
                    <p class="text-sm text-gray-600">
                        <span class="font-medium">Due Date:</span> {{ $invoice->due_date->format('M d, Y') }}
                    </p>
                    @if($invoiceStats['is_overdue'])
                        <p class="text-sm text-red-600 font-medium">
                            <i class="fas fa-exclamation-triangle mr-1"></i>
                            Overdue by {{ abs($invoiceStats['days_until_due']) }} days
                        </p>
                    @elseif($invoiceStats['days_until_due'] > 0)
                        <p class="text-sm text-green-600">
                            <i class="fas fa-clock mr-1"></i>
                            Due in {{ $invoiceStats['days_until_due'] }} days
                        </p>
                    @endif
                </div>
            </div>
            <div class="text-right">
                <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                    @if($invoice->status === 'paid') bg-green-100 text-green-800
                    @elseif($invoice->status === 'overdue') bg-red-100 text-red-800
                    @elseif($invoice->status === 'sent') bg-blue-100 text-blue-800
                    @else bg-gray-100 text-gray-800 @endif">
                    @if($invoice->status === 'paid')
                        <i class="fas fa-check-circle mr-1"></i> Paid
                    @elseif($invoice->status === 'overdue')
                        <i class="fas fa-exclamation-triangle mr-1"></i> Overdue
                    @elseif($invoice->status === 'sent')
                        <i class="fas fa-paper-plane mr-1"></i> Sent
                    @else
                        <i class="fas fa-file-invoice mr-1"></i> {{ ucfirst($invoice->status) }}
                    @endif
                </div>
                <div class="mt-2">
                    <a href="{{ route('client-portal.invoice.download', ['invoice' => $invoice->id, 'token' => $invoice->public_token]) }}" 
                       class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500">
                        <i class="fas fa-download mr-2"></i>
                        Download PDF
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Summary -->
    @if($paymentSummary['payment_count'] > 0 || !$paymentSummary['is_fully_paid'])
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Payment Summary</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-gray-50 rounded-lg p-4">
                    <p class="text-sm font-medium text-gray-600">Total Amount</p>
                    <p class="text-2xl font-bold text-gray-900">
                        {{ config('services.currency.symbol', '₹') }}{{ number_format($paymentSummary['total_amount'], 2) }}
                    </p>
                </div>
                @if($paymentSummary['total_paid'] > 0)
                    <div class="bg-green-50 rounded-lg p-4">
                        <p class="text-sm font-medium text-green-600">Amount Paid</p>
                        <p class="text-2xl font-bold text-green-900">
                            {{ config('services.currency.symbol', '₹') }}{{ number_format($paymentSummary['total_paid'], 2) }}
                        </p>
                    </div>
                @endif
                @if($paymentSummary['remaining_amount'] > 0)
                    <div class="bg-blue-50 rounded-lg p-4">
                        <p class="text-sm font-medium text-blue-600">Remaining Amount</p>
                        <p class="text-2xl font-bold text-blue-900">
                            {{ config('services.currency.symbol', '₹') }}{{ number_format($paymentSummary['remaining_amount'], 2) }}
                        </p>
                    </div>
                @endif
            </div>

            @if($paymentSummary['is_fully_paid'])
                <div class="mt-4 bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-600 mr-2"></i>
                        <span class="text-green-800 font-medium">This invoice has been fully paid. Thank you!</span>
                    </div>
                </div>
            @elseif($canMakePayment['can_pay'])
                <div class="mt-6">
                    <a href="{{ $invoice->payment_url }}" 
                       class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500">
                        <i class="fas fa-credit-card mr-2"></i>
                        Make Payment
                    </a>
                </div>
            @else
                <div class="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle text-yellow-600 mr-2"></i>
                        <span class="text-yellow-800">{{ $canMakePayment['reason'] }}</span>
                    </div>
                </div>
            @endif
        </div>
    @endif

    <!-- Invoice Details -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- From -->
            <div>
                <h4 class="text-sm font-semibold text-gray-900 uppercase tracking-wide mb-3">From</h4>
                <div class="text-sm text-gray-600 space-y-1">
                    <p class="font-medium text-gray-900">{{ $invoice->user->business_name ?: $invoice->user->name }}</p>
                    @if($invoice->user->address)
                        <p>{{ $invoice->user->address }}</p>
                    @endif
                    @if($invoice->user->phone)
                        <p>{{ $invoice->user->phone }}</p>
                    @endif
                    <p>{{ $invoice->user->email }}</p>
                    @if($invoice->user->gst_number)
                        <p><span class="font-medium">GST:</span> {{ $invoice->user->gst_number }}</p>
                    @endif
                </div>
            </div>

            <!-- To -->
            <div>
                <h4 class="text-sm font-semibold text-gray-900 uppercase tracking-wide mb-3">To</h4>
                <div class="text-sm text-gray-600 space-y-1">
                    <p class="font-medium text-gray-900">{{ $invoice->client->name }}</p>
                    @if($invoice->client->company_name)
                        <p>{{ $invoice->client->company_name }}</p>
                    @endif
                    @if($invoice->client->address)
                        <p>{{ $invoice->client->address }}</p>
                    @endif
                    @if($invoice->client->phone)
                        <p>{{ $invoice->client->phone }}</p>
                    @endif
                    <p>{{ $invoice->client->email }}</p>
                    @if($invoice->client->gst_number)
                        <p><span class="font-medium">GST:</span> {{ $invoice->client->gst_number }}</p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Invoice Items -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Invoice Items</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($invoice->items as $item)
                        <tr>
                            <td class="px-6 py-4 text-sm text-gray-900">{{ $item->description }}</td>
                            <td class="px-6 py-4 text-sm text-gray-900 text-center">{{ $item->quantity }}</td>
                            <td class="px-6 py-4 text-sm text-gray-900 text-right">
                                {{ config('services.currency.symbol', '₹') }}{{ number_format($item->rate, 2) }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900 text-right font-medium">
                                {{ config('services.currency.symbol', '₹') }}{{ number_format($item->amount, 2) }}
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Invoice Totals -->
        <div class="bg-gray-50 px-6 py-4">
            <div class="flex justify-end">
                <div class="w-64 space-y-2">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Subtotal:</span>
                        <span class="text-gray-900">{{ config('services.currency.symbol', '₹') }}{{ number_format($invoice->subtotal, 2) }}</span>
                    </div>
                    @if($invoice->tax_amount > 0)
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Tax ({{ $invoice->tax_percentage }}%):</span>
                            <span class="text-gray-900">{{ config('services.currency.symbol', '₹') }}{{ number_format($invoice->tax_amount, 2) }}</span>
                        </div>
                    @endif
                    <div class="flex justify-between text-sm font-medium border-t border-gray-200 pt-2">
                        <span class="text-gray-900">Total:</span>
                        <span class="text-gray-900">{{ config('services.currency.symbol', '₹') }}{{ number_format($invoice->total_amount, 2) }}</span>
                    </div>
                    @if($invoice->tds_amount > 0)
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">TDS ({{ $invoice->tds_percentage }}%):</span>
                            <span class="text-red-600">-{{ config('services.currency.symbol', '₹') }}{{ number_format($invoice->tds_amount, 2) }}</span>
                        </div>
                        <div class="flex justify-between text-lg font-bold border-t border-gray-200 pt-2">
                            <span class="text-gray-900">Net Amount:</span>
                            <span class="text-gray-900">{{ config('services.currency.symbol', '₹') }}{{ number_format($invoice->net_amount, 2) }}</span>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    @if($invoice->notes)
        <!-- Notes -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">Notes</h3>
            <p class="text-gray-600">{{ $invoice->notes }}</p>
        </div>
    @endif

    <!-- Payment History -->
    @if($paymentSummary['payment_count'] > 0)
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Payment History</h3>
            <div class="space-y-3">
                @foreach($paymentSummary['payments'] as $payment)
                    <div class="flex justify-between items-center py-3 border-b border-gray-100 last:border-b-0">
                        <div>
                            <p class="text-sm font-medium text-gray-900">{{ $payment['formatted_amount'] }}</p>
                            <p class="text-xs text-gray-500">
                                via {{ ucfirst($payment['gateway']) }} • 
                                {{ \Carbon\Carbon::parse($payment['paid_at'])->format('M d, Y g:i A') }}
                            </p>
                        </div>
                        <div class="text-green-600">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @endif
</div>
@endsection
