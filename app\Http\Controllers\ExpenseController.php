<?php

namespace App\Http\Controllers;

use App\Models\Expense;
use App\Services\ExpenseService;
use App\Services\ClientService;
use App\Services\ProjectService;
use App\Services\PlanChecker;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class ExpenseController extends Controller
{
    protected ExpenseService $expenseService;
    protected ClientService $clientService;
    protected ProjectService $projectService;

    public function __construct(
        ExpenseService $expenseService,
        ClientService $clientService,
        ProjectService $projectService
    ) {
        $this->expenseService = $expenseService;
        $this->clientService = $clientService;
        $this->projectService = $projectService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('viewAny', Expense::class);

        $expenses = $this->expenseService->getExpensesForUser(Auth::id(), $request);
        $categories = $this->expenseService->getActiveCategories();
        $clients = $this->clientService->getClientsForSelect(Auth::id());
        $projects = $this->projectService->getProjectsForUser(Auth::id());

        // Get expense usage for plan limitations
        $expenseUsage = PlanChecker::getMonthlyExpenseUsage(Auth::user());
        $canCreateExpense = $this->expenseService->canCreateExpense(Auth::id());

        return view('expenses.index', compact('expenses', 'categories', 'clients', 'projects', 'expenseUsage', 'canCreateExpense'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create', Expense::class);

        // Check if user can create expenses
        if (!$this->expenseService->canCreateExpense(Auth::id())) {
            return redirect()->route('upgrade.required', ['feature' => 'expenses_limit']);
        }

        $categories = $this->expenseService->getActiveCategories();
        $clients = $this->clientService->getClientsForSelect(Auth::id());
        $projects = $this->projectService->getProjectsForUser(Auth::id());

        return view('expenses.create', compact('categories', 'clients', 'projects'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create', Expense::class);

        // Check if user can create expenses
        if (!$this->expenseService->canCreateExpense(Auth::id())) {
            return redirect()->route('upgrade.required', ['feature' => 'expenses_limit']);
        }

        $validated = $request->validate([
            'category_id' => 'required|exists:expense_categories,id',
            'amount' => 'required|numeric|min:0.01|max:999999.99',
            'description' => 'required|string|max:255',
            'expense_date' => 'required|date|before_or_equal:today',
            'client_id' => 'nullable|exists:clients,id',
            'project_id' => 'nullable|exists:projects,id',
            'tax_percentage' => 'nullable|numeric|min:0|max:100',
            'is_billable' => 'boolean',
            'payment_method' => 'nullable|string|max:50',
            'vendor_name' => 'nullable|string|max:255',
            'reference_number' => 'nullable|string|max:100',
            'notes' => 'nullable|string|max:1000',
            'receipt' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:5120', // 5MB max
        ]);

        $this->expenseService->createExpense($validated);

        return redirect()->route('expenses.index')
                        ->with('success', 'Expense created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Expense $expense)
    {
        $this->authorize('view', $expense);
        $expense->load(['category', 'client', 'project', 'approvedBy']);

        return view('expenses.show', compact('expense'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Expense $expense)
    {
        $this->authorize('update', $expense);

        if (!$expense->canBeEdited()) {
            return redirect()->route('expenses.show', $expense)
                ->with('error', 'This expense cannot be edited in its current status.');
        }

        $categories = $this->expenseService->getActiveCategories();
        $clients = $this->clientService->getClientsForSelect(Auth::id());
        $projects = $this->projectService->getProjectsForUser(Auth::id());

        return view('expenses.edit', compact('expense', 'categories', 'clients', 'projects'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Expense $expense)
    {
        $this->authorize('update', $expense);

        if (!$expense->canBeEdited()) {
            return redirect()->route('expenses.show', $expense)
                ->with('error', 'This expense cannot be edited in its current status.');
        }

        $validated = $request->validate([
            'category_id' => 'required|exists:expense_categories,id',
            'amount' => 'required|numeric|min:0.01|max:999999.99',
            'description' => 'required|string|max:255',
            'expense_date' => 'required|date|before_or_equal:today',
            'client_id' => 'nullable|exists:clients,id',
            'project_id' => 'nullable|exists:projects,id',
            'tax_percentage' => 'nullable|numeric|min:0|max:100',
            'is_billable' => 'boolean',
            'payment_method' => 'nullable|string|max:50',
            'vendor_name' => 'nullable|string|max:255',
            'reference_number' => 'nullable|string|max:100',
            'notes' => 'nullable|string|max:1000',
            'receipt' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:5120', // 5MB max
        ]);

        $this->expenseService->updateExpense($expense, $validated);

        return redirect()->route('expenses.show', $expense)
                        ->with('success', 'Expense updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Expense $expense)
    {
        $this->authorize('delete', $expense);

        if (!$expense->canBeDeleted()) {
            return redirect()->route('expenses.show', $expense)
                ->with('error', 'This expense cannot be deleted in its current status.');
        }

        $this->expenseService->deleteExpense($expense);

        return redirect()->route('expenses.index')
                        ->with('success', 'Expense deleted successfully.');
    }

    /**
     * Submit expense for approval
     */
    public function submit(Expense $expense)
    {
        $this->authorize('submit', $expense);

        if ($expense->status !== 'draft') {
            return redirect()->route('expenses.show', $expense)
                ->with('error', 'Only draft expenses can be submitted for approval.');
        }

        $this->expenseService->submitForApproval($expense);

        return redirect()->route('expenses.show', $expense)
                        ->with('success', 'Expense submitted for approval.');
    }

    /**
     * Approve an expense
     */
    public function approve(Expense $expense)
    {
        $this->authorize('approve', $expense);

        if (!$expense->canBeApproved()) {
            return redirect()->route('expenses.show', $expense)
                ->with('error', 'This expense cannot be approved.');
        }

        $this->expenseService->approveExpense($expense);

        return redirect()->route('expenses.show', $expense)
                        ->with('success', 'Expense approved successfully.');
    }

    /**
     * Reject an expense
     */
    public function reject(Request $request, Expense $expense)
    {
        $this->authorize('reject', $expense);

        if (!$expense->canBeApproved()) {
            return redirect()->route('expenses.show', $expense)
                ->with('error', 'This expense cannot be rejected.');
        }

        $validated = $request->validate([
            'reason' => 'nullable|string|max:500'
        ]);

        $this->expenseService->rejectExpense($expense, $validated['reason'] ?? null);

        return redirect()->route('expenses.show', $expense)
                        ->with('success', 'Expense rejected.');
    }

    /**
     * Export expenses
     */
    public function export(Request $request)
    {
        $this->authorize('export', Expense::class);

        $filters = $request->only(['start_date', 'end_date', 'category_id', 'status']);
        $data = $this->expenseService->getExportData(Auth::id(), $filters);

        $filename = 'expenses_' . now()->format('Y_m_d_H_i_s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');

            if (!empty($data)) {
                fputcsv($file, array_keys($data[0]));
                foreach ($data as $row) {
                    fputcsv($file, $row);
                }
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
