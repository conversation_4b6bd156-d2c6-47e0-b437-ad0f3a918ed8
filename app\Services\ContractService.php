<?php

namespace App\Services;

use App\Models\Contract;
use App\Models\ContractTemplate;
use App\Models\Client;
use App\Repositories\ContractRepository;
use Illuminate\Support\Facades\Auth;
use Exception;

class ContractService
{
    protected ContractRepository $contractRepository;

    public function __construct(ContractRepository $contractRepository)
    {
        $this->contractRepository = $contractRepository;
    }

    /**
     * Create a new contract
     */
    public function createContract(array $data): Contract
    {
        $template = ContractTemplate::findOrFail($data['contract_template_id']);
        $client = Client::where('id', $data['client_id'])
            ->where('user_id', Auth::id())
            ->firstOrFail();
        
        // Get template content
        $content = $template->content;
        
        // Prepare default variables
        $defaultVariables = [
            'client_name' => $client->name,
            'client_company' => $client->company_name ?? $client->name,
            'client_email' => $client->email,
            'client_phone' => $client->phone,
            'client_address' => $client->address,
            'business_name' => Auth::user()->name,
            'business_company' => Auth::user()->business_name ?? Auth::user()->name,
            'business_email' => Auth::user()->email,
            'business_phone' => Auth::user()->phone,
            'business_address' => Auth::user()->address,
            'contract_date' => now()->format('Y-m-d'),
            'contract_title' => $data['title'],
            // Keep old freelancer variables for backward compatibility
            'freelancer_name' => Auth::user()->name,
            'freelancer_business' => Auth::user()->business_name ?? Auth::user()->name,
            'freelancer_email' => Auth::user()->email,
            'freelancer_phone' => Auth::user()->phone,
            'freelancer_address' => Auth::user()->address,
        ];

        // Merge with provided variables
        $variables = array_merge($defaultVariables, $data['variables'] ?? []);

        // Check for required variables
        $requiredVariables = $this->extractRequiredVariables($content);
        $missingVariables = array_diff($requiredVariables, array_keys($variables));
        
        if (!empty($missingVariables)) {
            throw new Exception('Missing required variables: ' . implode(', ', $missingVariables));
        }

        // Replace variables in content
        $processedContent = $this->replaceVariables($content, $variables);

        return Contract::create([
            'user_id' => Auth::id(),
            'business_id' => $data['business_id'] ?? null,
            'client_id' => $data['client_id'],
            'contract_template_id' => $data['contract_template_id'],
            'title' => $data['title'],
            'content' => $processedContent,
            'variables' => $variables,
            'status' => 'draft',
        ]);
    }

    /**
     * Update an existing contract
     */
    public function updateContract(Contract $contract, array $data): Contract
    {
        $template = ContractTemplate::findOrFail($data['contract_template_id']);
        $client = Client::where('id', $data['client_id'])
            ->where('user_id', Auth::id())
            ->firstOrFail();
        
        // Get template content
        $content = $template->content;
        
        // Prepare default variables
        $defaultVariables = [
            'client_name' => $client->name,
            'client_company' => $client->company_name ?? $client->name,
            'client_email' => $client->email,
            'client_phone' => $client->phone,
            'client_address' => $client->address,
            'business_name' => Auth::user()->name,
            'business_company' => Auth::user()->business_name ?? Auth::user()->name,
            'business_email' => Auth::user()->email,
            'business_phone' => Auth::user()->phone,
            'business_address' => Auth::user()->address,
            'contract_date' => $contract->created_at->format('Y-m-d'),
            'contract_title' => $data['title'],
            // Keep old freelancer variables for backward compatibility
            'freelancer_name' => Auth::user()->name,
            'freelancer_business' => Auth::user()->business_name ?? Auth::user()->name,
            'freelancer_email' => Auth::user()->email,
            'freelancer_phone' => Auth::user()->phone,
            'freelancer_address' => Auth::user()->address,
        ];

        // Merge with provided variables
        $variables = array_merge($defaultVariables, $data['variables'] ?? []);

        // Replace variables in content
        $processedContent = $this->replaceVariables($content, $variables);

        $contract->update([
            'client_id' => $data['client_id'],
            'contract_template_id' => $data['contract_template_id'],
            'title' => $data['title'],
            'content' => $processedContent,
            'variables' => $variables,
        ]);

        return $contract;
    }

    /**
     * Send contract to client
     */
    public function sendContract(Contract $contract): Contract
    {
        $contract->update([
            'status' => 'sent',
            'sent_date' => now(),
        ]);

        // Here you could add email sending logic
        // Mail::to($contract->client->email)->send(new ContractSent($contract));

        return $contract;
    }

    /**
     * Mark contract as signed
     */
    public function markAsSigned(Contract $contract, array $data = []): Contract
    {
        $updateData = [
            'status' => 'signed',
            'signed_date' => now(),
        ];

        if (!empty($data['expiry_date'])) {
            $updateData['expiry_date'] = $data['expiry_date'];
        }

        if (!empty($data['signed_by'])) {
            $updateData['signed_by'] = $data['signed_by'];
        }

        $contract->update($updateData);

        return $contract;
    }

    /**
     * Mark contract as expired
     */
    public function markAsExpired(Contract $contract): Contract
    {
        $contract->update(['status' => 'expired']);
        return $contract;
    }

    /**
     * Cancel contract
     */
    public function cancelContract(Contract $contract): Contract
    {
        $contract->update([
            'status' => 'cancelled',
            'cancelled_date' => now(),
        ]);

        return $contract;
    }

    /**
     * Duplicate a contract
     */
    public function duplicateContract(Contract $contract): Contract
    {
        $newContract = $contract->replicate();
        $newContract->status = 'draft';
        $newContract->sent_date = null;
        $newContract->signed_date = null;
        $newContract->cancelled_date = null;
        $newContract->expiry_date = null;
        $newContract->signed_by = null;
        $newContract->save();

        return $newContract;
    }

    /**
     * Get contract statistics for user
     */
    public function getContractStats(int $userId): array
    {
        return $this->contractRepository->getStatsForUser($userId);
    }

    /**
     * Get contracts for user with filters
     */
    public function getContractsForUser(int $userId, $request = null)
    {
        return $this->contractRepository->getForUser($userId, $request);
    }

    /**
     * Check if user can create contracts
     */
    public function canCreateContract(int $userId): bool
    {
        $user = \App\Models\User::find($userId);
        return PlanChecker::canCreateContract($user);
    }

    /**
     * Delete a contract
     */
    public function deleteContract(Contract $contract): array
    {
        if ($contract->status === 'signed') {
            return [
                'success' => false,
                'message' => 'Cannot delete signed contracts.'
            ];
        }

        $this->contractRepository->delete($contract);

        return [
            'success' => true,
            'message' => 'Contract deleted successfully.'
        ];
    }

    /**
     * Get current month usage for user
     */
    public function getCurrentMonthUsage(int $userId): array
    {
        $count = $this->contractRepository->getCurrentMonthCountForUser($userId);
        $limit = 5; // Free plan limit
        
        return [
            'used' => $count,
            'limit' => $limit,
            'remaining' => max(0, $limit - $count),
            'percentage' => $limit > 0 ? ($count / $limit) * 100 : 0,
        ];
    }

    /**
     * Get contract analytics
     */
    public function getContractAnalytics(int $userId): array
    {
        return $this->contractRepository->getAnalyticsData($userId);
    }

    /**
     * Get contracts expiring soon
     */
    public function getExpiringSoon(int $userId, int $days = 30): array
    {
        $contracts = $this->contractRepository->getExpiringSoon($userId, $days);
        
        return $contracts->map(function ($contract) {
            return [
                'contract' => $contract,
                'days_until_expiry' => now()->diffInDays($contract->expiry_date),
                'is_urgent' => now()->diffInDays($contract->expiry_date) <= 7,
            ];
        })->toArray();
    }

    /**
     * Extract required variables from template content
     */
    private function extractRequiredVariables(string $content): array
    {
        preg_match_all('/\{\{([^}]+)\}\}/', $content, $matches);
        return array_unique($matches[1]);
    }

    /**
     * Replace variables in content
     */
    private function replaceVariables(string $content, array $variables): string
    {
        foreach ($variables as $key => $value) {
            $content = str_replace('{{' . $key . '}}', $value, $content);
        }

        return $content;
    }

    /**
     * Get contract performance metrics
     */
    public function getPerformanceMetrics(int $userId): array
    {
        $stats = $this->getContractStats($userId);
        $totalContracts = $stats['total_count'];
        
        if ($totalContracts === 0) {
            return [
                'conversion_rate' => 0,
                'avg_time_to_sign' => 0,
                'success_rate' => 0,
            ];
        }

        $conversionRate = ($stats['signed_count'] / $totalContracts) * 100;
        $successRate = (($stats['signed_count'] + $stats['sent_count']) / $totalContracts) * 100;

        return [
            'conversion_rate' => round($conversionRate, 2),
            'success_rate' => round($successRate, 2),
            'total_contracts' => $totalContracts,
            'signed_contracts' => $stats['signed_count'],
            'pending_contracts' => $stats['sent_count'],
        ];
    }
}
