<?php

namespace Database\Seeders;

use App\Models\ContractTemplate;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ContractTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // NDA Template
        ContractTemplate::create([
            'name' => 'Non-Disclosure Agreement (NDA)',
            'type' => 'nda',
            'content' => $this->getNdaTemplate(),
            'variables' => [
                'client_name',
                'client_company',
                'business_name',
                'project_description',
                'effective_date',
                'jurisdiction'
            ],
            'is_active' => true,
        ]);

        // Service Agreement Template
        ContractTemplate::create([
            'name' => 'Freelance Service Agreement',
            'type' => 'service_agreement',
            'content' => $this->getServiceAgreementTemplate(),
            'variables' => [
                'client_name',
                'client_company',
                'client_address',
                'business_name',
                'business_address',
                'project_description',
                'project_timeline',
                'payment_terms',
                'total_amount',
                'effective_date',
                'jurisdiction'
            ],
            'is_active' => true,
        ]);
    }

    private function getNdaTemplate(): string
    {
        return '
NON-DISCLOSURE AGREEMENT

This Non-Disclosure Agreement ("Agreement") is entered into on {{effective_date}} between {{business_name}} ("Disclosing Party") and {{client_name}} of {{client_company}} ("Receiving Party").

1. CONFIDENTIAL INFORMATION
The Disclosing Party may share confidential information related to {{project_description}} with the Receiving Party.

2. OBLIGATIONS
The Receiving Party agrees to:
- Keep all confidential information strictly confidential
- Not disclose any information to third parties
- Use the information solely for the intended project purpose

3. TERM
This agreement shall remain in effect for a period of 2 years from the effective date.

4. GOVERNING LAW
This agreement shall be governed by the laws of {{jurisdiction}}.

Disclosing Party: {{business_name}}
Signature: ___________________ Date: ___________

Receiving Party: {{client_name}}
Signature: ___________________ Date: ___________
        ';
    }

    private function getServiceAgreementTemplate(): string
    {
        return '
FREELANCE SERVICE AGREEMENT

This Service Agreement ("Agreement") is entered into on {{effective_date}} between:

BUSINESS: {{business_name}}
Address: {{business_address}}

CLIENT: {{client_name}}
Company: {{client_company}}
Address: {{client_address}}

1. SERVICES
The Business agrees to provide the following services:
{{project_description}}

2. TIMELINE
Project Timeline: {{project_timeline}}

3. COMPENSATION
Total Project Amount: ₹{{total_amount}}
Payment Terms: {{payment_terms}}

4. INTELLECTUAL PROPERTY
All work created under this agreement shall be the property of the Client upon full payment.

5. CONFIDENTIALITY
Both parties agree to maintain confidentiality of all project-related information.

6. TERMINATION
Either party may terminate this agreement with 7 days written notice.

7. GOVERNING LAW
This agreement shall be governed by the laws of {{jurisdiction}}.

BUSINESS: {{business_name}}
Signature: ___________________ Date: ___________

CLIENT: {{client_name}}
Signature: ___________________ Date: ___________
        ';
    }
}
