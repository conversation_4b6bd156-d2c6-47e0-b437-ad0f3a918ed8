<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lead_notes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lead_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            
            $table->text('content');
            $table->boolean('is_private')->default(false); // For team collaboration
            $table->boolean('is_important')->default(false); // Flag important notes
            $table->enum('type', ['general', 'meeting', 'call', 'email', 'reminder'])->default('general');
            
            // Tagging and categorization
            $table->json('tags')->nullable();
            $table->timestamp('reminder_at')->nullable(); // For follow-up reminders
            
            $table->timestamps();

            // Indexes for performance
            $table->index(['lead_id', 'created_at']);
            $table->index(['user_id', 'type']);
            $table->index(['is_important', 'is_private']);
            $table->index('reminder_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lead_notes');
    }
};
