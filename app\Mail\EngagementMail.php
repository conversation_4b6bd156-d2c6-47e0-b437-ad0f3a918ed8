<?php

namespace App\Mail;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class EngagementMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public User $user;
    public string $engagementType;
    public array $userData;

    /**
     * Create a new message instance.
     */
    public function __construct(User $user, string $engagementType, array $userData = [])
    {
        $this->user = $user;
        $this->engagementType = $engagementType;
        $this->userData = $userData;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $subjects = [
            'inactive_user' => "We miss you! Come back to " . config('app.name') . " 💙",
            'milestone_celebration' => "🎉 Congratulations on your milestone!",
            'feature_suggestion' => "💡 Features that could help your business",
            'success_tips' => "📈 Tips to grow your business faster",
            'community_highlight' => "🌟 You're part of something amazing",
        ];

        return new Envelope(
            subject: $subjects[$this->engagementType] ?? "Updates from " . config('app.name'),
            to: [$this->user->email],
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.engagement',
            with: [
                'user' => $this->user,
                'engagementType' => $this->engagementType,
                'userData' => $this->userData,
                'header_title' => $this->getHeaderTitle(),
                'header_subtitle' => $this->getHeaderSubtitle(),
            ],
        );
    }

    private function getHeaderTitle(): string
    {
        return match($this->engagementType) {
            'inactive_user' => 'We Miss You!',
            'milestone_celebration' => 'Congratulations!',
            'feature_suggestion' => 'Boost Your Productivity',
            'success_tips' => 'Grow Your Business',
            'community_highlight' => 'You\'re Amazing!',
            default => 'Hello from ' . config('app.name')
        };
    }

    private function getHeaderSubtitle(): string
    {
        return match($this->engagementType) {
            'inactive_user' => 'Your business tools are waiting for you',
            'milestone_celebration' => 'You\'ve achieved something incredible',
            'feature_suggestion' => 'Discover features tailored for your business',
            'success_tips' => 'Expert strategies to accelerate your growth',
            'community_highlight' => 'See what our community is achieving',
            default => 'Updates and insights for your business'
        };
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
