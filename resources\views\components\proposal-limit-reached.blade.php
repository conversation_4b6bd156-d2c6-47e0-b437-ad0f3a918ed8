@props(['currentCount', 'limit', 'planName' => 'Free'])

<div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 mb-4">
        <i class="fas fa-exclamation-triangle text-yellow-600 text-xl"></i>
    </div>
    
    <h3 class="text-lg font-medium text-yellow-900 mb-2">
        Proposal Limit Reached
    </h3>
    
    <p class="text-yellow-700 mb-4">
        You've reached your {{ $planName }} plan limit of {{ $limit }} proposals per month 
        ({{ $currentCount }}/{{ $limit }} used).
    </p>
    
    <div class="bg-white rounded-lg p-4 mb-4">
        <h4 class="font-medium text-gray-900 mb-2">Upgrade to unlock:</h4>
        <ul class="text-sm text-gray-600 space-y-1">
            <li><i class="fas fa-check text-green-500 mr-2"></i>Unlimited proposals</li>
            <li><i class="fas fa-check text-green-500 mr-2"></i>Professional templates</li>
            <li><i class="fas fa-check text-green-500 mr-2"></i>Custom branding</li>
            <li><i class="fas fa-check text-green-500 mr-2"></i>Priority support</li>
        </ul>
    </div>
    
    <div class="flex flex-col sm:flex-row gap-3 justify-center">
        <a href="{{ route('subscription.plans') }}" 
           class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg transition-colors">
            <i class="fas fa-arrow-up mr-2"></i>Upgrade Now
        </a>
        
        <a href="{{ route('proposals.index') }}" 
           class="bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium py-2 px-6 rounded-lg transition-colors">
            <i class="fas fa-arrow-left mr-2"></i>Back to Proposals
        </a>
    </div>
    
    <p class="text-xs text-yellow-600 mt-4">
        Your limit will reset on {{ now()->addMonth()->startOfMonth()->format('M d, Y') }}
    </p>
</div>
