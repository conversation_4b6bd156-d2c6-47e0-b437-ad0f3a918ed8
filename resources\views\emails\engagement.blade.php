@extends('emails.layouts.app')

@section('content')
    <!-- Engagement Header -->
    <div style="text-align: center; margin-bottom: 32px;">
        @if($engagementType === 'inactive_user')
            <div style="font-size: 64px; margin-bottom: 16px;">💙</div>
        @elseif($engagementType === 'milestone_celebration')
            <div style="font-size: 64px; margin-bottom: 16px;">🎉</div>
        @elseif($engagementType === 'feature_suggestion')
            <div style="font-size: 64px; margin-bottom: 16px;">💡</div>
        @elseif($engagementType === 'success_tips')
            <div style="font-size: 64px; margin-bottom: 16px;">📈</div>
        @else
            <div style="font-size: 64px; margin-bottom: 16px;">🌟</div>
        @endif
        
        <h2 style="color: #10b981; margin-bottom: 8px;">{{ $header_title }}</h2>
        <p class="lead" style="color: #059669;">
            Hi {{ $user->name }}, {{ $header_subtitle }}
        </p>
    </div>

    @if($engagementType === 'inactive_user')
        <!-- Inactive User Re-engagement -->
        <p style="font-size: 18px; color: #374151; margin-bottom: 24px;">
            We noticed you haven't been active on {{ config('app.name') }} lately, and we wanted to check in. 
            Your business management tools are still here, ready to help you succeed!
        </p>
        
        <!-- What You're Missing -->
        <div class="info-box">
            <h3>🚀 What You're Missing Out On</h3>
            <p>While you've been away, here's what could have been helping your business:</p>
            <ul class="feature-list">
                <li><strong>Automated Invoicing</strong> - Save 5+ hours per week on billing tasks</li>
                <li><strong>Payment Tracking</strong> - Never lose track of who owes you money</li>
                <li><strong>Business Analytics</strong> - Understand your revenue patterns and growth</li>
                <li><strong>Client Management</strong> - Keep all your client information organized</li>
            </ul>
        </div>
        
        <!-- Special Offer -->
        <div style="background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); border: 2px solid #10b981; border-radius: 16px; padding: 32px; margin: 32px 0; text-align: center;">
            <div style="font-size: 48px; margin-bottom: 16px;">🎁</div>
            <h3 style="color: #166534; margin: 0 0 16px 0; font-size: 24px;">Welcome Back Offer!</h3>
            <p style="color: #15803d; margin: 0 0 24px 0; font-size: 18px;">
                Get 30% off your first month when you upgrade to Pro within the next 7 days.
            </p>
            <a href="{{ route('subscriptions.plans') }}?promo=WELCOME_BACK" class="btn btn-primary" style="font-size: 18px; padding: 16px 32px;">
                🚀 Claim Your Discount
            </a>
            <p style="color: #15803d; font-size: 14px; margin-top: 16px;">
                <em>Use code: WELCOME_BACK at checkout</em>
            </p>
        </div>

    @elseif($engagementType === 'milestone_celebration')
        <!-- Milestone Celebration -->
        <p style="font-size: 18px; color: #374151; margin-bottom: 24px;">
            We're thrilled to celebrate this amazing milestone with you! Your dedication and hard work have paid off.
        </p>
        
        <!-- Milestone Details -->
        <div style="background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); border: 2px solid #10b981; border-radius: 16px; padding: 32px; margin: 32px 0; text-align: center;">
            <div style="font-size: 64px; margin-bottom: 16px;">🏆</div>
            <h3 style="color: #166534; margin: 0 0 16px 0; font-size: 28px;">{{ $userData['milestone_title'] ?? 'Amazing Achievement!' }}</h3>
            <p style="color: #15803d; margin: 0 0 24px 0; font-size: 20px;">
                {{ $userData['milestone_description'] ?? 'You\'ve reached an incredible milestone in your business journey!' }}
            </p>
            
            @if(isset($userData['milestone_stats']))
                <div style="display: flex; justify-content: space-around; flex-wrap: wrap; margin: 24px 0;">
                    @foreach($userData['milestone_stats'] as $stat)
                        <div style="text-align: center; margin: 8px;">
                            <div style="font-size: 32px; font-weight: bold; color: #166534;">{{ $stat['value'] ?? '0' }}</div>
                            <div style="font-size: 14px; color: #15803d;">{{ $stat['label'] ?? 'Achievement' }}</div>
                        </div>
                    @endforeach
                </div>
            @endif
        </div>
        
        <!-- Share Achievement -->
        <div style="text-align: center; margin: 32px 0;">
            <h3 style="color: #1e293b; margin-bottom: 16px;">Share Your Success! 📢</h3>
            <p style="color: #64748b; margin-bottom: 20px;">Let others know about your amazing achievement</p>
            
            <div style="display: flex; flex-wrap: wrap; gap: 12px; justify-content: center;">
                <a href="https://twitter.com/intent/tweet?text=Just achieved {{ urlencode($userData['milestone_title'] ?? 'an amazing milestone') }} with @{{ config('app.name') }}! 🎉" class="btn btn-secondary" style="background-color: #1da1f2; color: white;">
                    🐦 Share on Twitter
                </a>
                <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode(config('app.url')) }}" class="btn btn-secondary" style="background-color: #0077b5; color: white;">
                    💼 Share on LinkedIn
                </a>
            </div>
        </div>

    @elseif($engagementType === 'feature_suggestion')
        <!-- Feature Suggestions -->
        <p style="font-size: 18px; color: #374151; margin-bottom: 24px;">
            Based on your usage patterns, we've identified some features that could significantly boost your productivity and business growth.
        </p>
        
        <!-- Suggested Features -->
        <h3>💡 Recommended Features for You</h3>
        <div style="margin: 24px 0;">
            @if(isset($userData['suggested_features']) && is_array($userData['suggested_features']))
                @foreach($userData['suggested_features'] as $feature)
                    <div style="display: flex; align-items: flex-start; margin-bottom: 20px; padding: 20px; background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); border-radius: 12px; border: 1px solid #e2e8f0;">
                        <div style="color: #10b981; font-size: 32px; margin-right: 16px;">{{ $feature['icon'] ?? '⭐' }}</div>
                        <div>
                            <h4 style="margin: 0 0 8px 0; color: #1e293b;">{{ $feature['title'] ?? 'Feature' }}</h4>
                            <p style="margin: 0 0 12px 0; color: #64748b; font-size: 14px;">{{ $feature['description'] ?? 'Helpful feature description' }}</p>
                            <div style="color: #10b981; font-size: 12px; font-weight: 600;">
                                💡 {{ $feature['benefit'] ?? 'Could save you time and increase efficiency' }}
                            </div>
                        </div>
                    </div>
                @endforeach
            @else
                <div style="display: flex; align-items: flex-start; margin-bottom: 20px; padding: 20px; background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); border-radius: 12px; border: 1px solid #e2e8f0;">
                    <div style="color: #10b981; font-size: 32px; margin-right: 16px;">⏰</div>
                    <div>
                        <h4 style="margin: 0 0 8px 0; color: #1e293b;">Time Tracking</h4>
                        <p style="margin: 0 0 12px 0; color: #64748b; font-size: 14px;">Track time spent on projects and automatically convert to billable hours</p>
                        <div style="color: #10b981; font-size: 12px; font-weight: 600;">
                            💡 Could increase your billable hours by 25%
                        </div>
                    </div>
                </div>
            @endif
        </div>

    @elseif($engagementType === 'success_tips')
        <!-- Success Tips -->
        <p style="font-size: 18px; color: #374151; margin-bottom: 24px;">
            Here are some proven strategies that successful {{ config('app.name') }} users employ to grow their businesses faster.
        </p>
        
        <!-- Tips -->
        <h3>📈 Growth Strategies That Work</h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px; margin: 24px 0;">
            <div style="background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); padding: 24px; border-radius: 12px; border: 1px solid #bbf7d0;">
                <div style="color: #166534; font-size: 32px; margin-bottom: 12px;">💰</div>
                <h4 style="margin: 0 0 12px 0; color: #166534;">Optimize Your Pricing</h4>
                <p style="margin: 0; color: #15803d; font-size: 14px;">Review and adjust your rates quarterly. Most successful users increase rates by 10-15% annually.</p>
            </div>
            
            <div style="background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%); padding: 24px; border-radius: 12px; border: 1px solid #bae6fd;">
                <div style="color: #1e40af; font-size: 32px; margin-bottom: 12px;">🔄</div>
                <h4 style="margin: 0 0 12px 0; color: #1e40af;">Automate Recurring Tasks</h4>
                <p style="margin: 0; color: #2563eb; font-size: 14px;">Set up recurring invoices and automated reminders to save 5+ hours per week.</p>
            </div>
            
            <div style="background: linear-gradient(135deg, #fdf4ff 0%, #fae8ff 100%); padding: 24px; border-radius: 12px; border: 1px solid #f5d0fe;">
                <div style="color: #a21caf; font-size: 32px; margin-bottom: 12px;">📊</div>
                <h4 style="margin: 0 0 12px 0; color: #a21caf;">Track Key Metrics</h4>
                <p style="margin: 0; color: #c026d3; font-size: 14px;">Monitor cash flow, client acquisition cost, and project profitability monthly.</p>
            </div>
        </div>

    @else
        <!-- Community Highlight -->
        <p style="font-size: 18px; color: #374151; margin-bottom: 24px;">
            You're part of an amazing community of entrepreneurs and business owners who are achieving incredible things with {{ config('app.name') }}.
        </p>
        
        <!-- Community Stats -->
        <div style="background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); border: 2px solid #10b981; border-radius: 16px; padding: 32px; margin: 32px 0; text-align: center;">
            <h3 style="color: #166534; margin: 0 0 24px 0; font-size: 24px;">Our Amazing Community</h3>
            
            <div style="display: flex; justify-content: space-around; flex-wrap: wrap; margin: 24px 0;">
                <div style="text-align: center; margin: 16px;">
                    <div style="font-size: 36px; font-weight: bold; color: #166534;">{{ $userData['total_users'] ?? '10,000+' }}</div>
                    <div style="font-size: 14px; color: #15803d;">Active Users</div>
                </div>
                <div style="text-align: center; margin: 16px;">
                    <div style="font-size: 36px; font-weight: bold; color: #166534;">{{ $userData['total_invoices'] ?? '₹50M+' }}</div>
                    <div style="font-size: 14px; color: #15803d;">Invoices Processed</div>
                </div>
                <div style="text-align: center; margin: 16px;">
                    <div style="font-size: 36px; font-weight: bold; color: #166534;">{{ $userData['time_saved'] ?? '100K+' }}</div>
                    <div style="font-size: 14px; color: #15803d;">Hours Saved</div>
                </div>
            </div>
        </div>
    @endif
    
    <!-- Call to Action -->
    <div style="text-align: center; margin: 40px 0;">
        @if($engagementType === 'inactive_user')
            <a href="{{ route('dashboard') }}" class="btn btn-primary" style="font-size: 18px; padding: 18px 36px;">
                💙 Welcome Back - Let's Get Started
            </a>
        @elseif($engagementType === 'milestone_celebration')
            <a href="{{ route('dashboard') }}" class="btn btn-primary" style="font-size: 18px; padding: 18px 36px;">
                🎉 Continue Your Success Journey
            </a>
        @elseif($engagementType === 'feature_suggestion')
            <a href="{{ route('dashboard') }}" class="btn btn-primary" style="font-size: 18px; padding: 18px 36px;">
                💡 Explore These Features
            </a>
        @elseif($engagementType === 'success_tips')
            <a href="{{ route('dashboard') }}" class="btn btn-primary" style="font-size: 18px; padding: 18px 36px;">
                📈 Apply These Strategies
            </a>
        @else
            <a href="{{ route('dashboard') }}" class="btn btn-primary" style="font-size: 18px; padding: 18px 36px;">
                🌟 Continue Building Success
            </a>
        @endif
    </div>
    
    <!-- Additional Resources -->
    <div style="background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; padding: 24px; margin: 32px 0;">
        <h3 style="color: #1e293b; margin: 0 0 16px 0;">📚 Helpful Resources</h3>
        <div style="display: flex; flex-wrap: wrap; gap: 16px;">
            <div style="flex: 1; min-width: 200px; text-align: center;">
                <div style="color: #10b981; font-size: 32px; margin-bottom: 8px;">📖</div>
                <h4 style="color: #1e293b; margin: 0 0 4px 0; font-size: 16px;">Knowledge Base</h4>
                <p style="color: #64748b; margin: 0 0 8px 0; font-size: 14px;">Step-by-step guides and tutorials</p>
                <a href="#" style="color: #10b981; text-decoration: none; font-weight: 600;">Browse Articles →</a>
            </div>
            
            <div style="flex: 1; min-width: 200px; text-align: center;">
                <div style="color: #10b981; font-size: 32px; margin-bottom: 8px;">🎥</div>
                <h4 style="color: #1e293b; margin: 0 0 4px 0; font-size: 16px;">Video Tutorials</h4>
                <p style="color: #64748b; margin: 0 0 8px 0; font-size: 14px;">Learn with our video library</p>
                <a href="#" style="color: #10b981; text-decoration: none; font-weight: 600;">Watch Videos →</a>
            </div>
            
            <div style="flex: 1; min-width: 200px; text-align: center;">
                <div style="color: #10b981; font-size: 32px; margin-bottom: 8px;">👥</div>
                <h4 style="color: #1e293b; margin: 0 0 4px 0; font-size: 16px;">Community Forum</h4>
                <p style="color: #64748b; margin: 0 0 8px 0; font-size: 14px;">Connect with other users</p>
                <a href="#" style="color: #10b981; text-decoration: none; font-weight: 600;">Join Discussion →</a>
            </div>
        </div>
    </div>
    
    <!-- Support Section -->
    <div style="text-align: center; background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; padding: 32px; margin: 32px 0;">
        <div style="font-size: 48px; margin-bottom: 16px;">🎧</div>
        <h3 style="color: #1e293b; margin: 0 0 12px 0;">We're Here to Help You Succeed</h3>
        <p style="color: #64748b; margin: 0 0 24px 0;">
            Our support team is dedicated to helping you achieve your business goals. Reach out anytime!
        </p>
        
        <div style="display: flex; flex-wrap: wrap; gap: 12px; justify-content: center;">
            <a href="mailto:support@{{ config('app.domain', 'freeligo.com') }}" class="btn btn-outline">
                📧 Email Support
            </a>
            <a href="#" class="btn btn-secondary">
                💬 Live Chat
            </a>
            <a href="#" class="btn btn-secondary">
                📞 Schedule Call
            </a>
        </div>
    </div>
    
    <!-- Thank You Message -->
    <div style="text-align: center; margin: 40px 0;">
        @if($engagementType === 'inactive_user')
            <h3 style="color: #1e293b; margin-bottom: 16px;">We Can't Wait to See You Back! 💙</h3>
            <p style="color: #64748b; font-size: 16px;">
                Your success is our success. Let's continue building something amazing together.
            </p>
        @elseif($engagementType === 'milestone_celebration')
            <h3 style="color: #1e293b; margin-bottom: 16px;">Here's to Many More Milestones! 🥂</h3>
            <p style="color: #64748b; font-size: 16px;">
                We're proud to be part of your journey and excited to see what you achieve next.
            </p>
        @else
            <h3 style="color: #1e293b; margin-bottom: 16px;">Thank You for Being Amazing! 🌟</h3>
            <p style="color: #64748b; font-size: 16px;">
                Your success inspires us to keep building better tools for entrepreneurs like you.
            </p>
        @endif
    </div>
    
    <!-- Footer Note -->
    <div style="border-top: 1px solid #e2e8f0; padding-top: 20px; margin-top: 32px;">
        <p style="color: #64748b; font-size: 14px; text-align: center; margin: 0;">
            This email was sent to {{ $user->email }} because you're a valued {{ config('app.name') }} user.
            <br>
            <a href="#" style="color: #64748b; text-decoration: none;">Manage email preferences</a> | 
            <a href="#" style="color: #64748b; text-decoration: none;">Unsubscribe from engagement emails</a>
        </p>
    </div>
@endsection
