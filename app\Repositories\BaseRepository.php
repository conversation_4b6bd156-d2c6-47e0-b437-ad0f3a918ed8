<?php

namespace App\Repositories;

use App\Contracts\RepositoryInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;

abstract class BaseRepository implements RepositoryInterface
{
    protected Model $model;
    protected Builder $query;

    public function __construct()
    {
        $this->model = $this->getModel();
        $this->resetQuery();
    }

    /**
     * Get the model instance
     */
    abstract protected function getModel(): Model;

    /**
     * Reset the query builder
     */
    protected function resetQuery(): void
    {
        $this->query = $this->model->newQuery();
    }

    /**
     * Get all records (with safety limit)
     */
    public function all(int $limit = 1000): Collection
    {
        $result = $this->query->limit($limit)->get();
        $this->resetQuery();
        return $result;
    }

    /**
     * Find a record by ID
     */
    public function find(int $id): ?Model
    {
        $result = $this->query->find($id);
        $this->resetQuery();
        return $result;
    }

    /**
     * Find a record by ID or fail
     */
    public function findOrFail(int $id): Model
    {
        $result = $this->query->findOrFail($id);
        $this->resetQuery();
        return $result;
    }

    /**
     * Create a new record
     */
    public function create(array $data): Model
    {
        return $this->model->create($data);
    }

    /**
     * Update a record
     */
    public function update(Model $model, array $data): bool
    {
        return $model->update($data);
    }

    /**
     * Delete a record
     */
    public function delete(Model $model): bool
    {
        return $model->delete();
    }

    /**
     * Get paginated records
     */
    public function paginate(int $perPage = 15): LengthAwarePaginator
    {
        $result = $this->query->paginate($perPage);
        $this->resetQuery();
        return $result;
    }

    /**
     * Find records by criteria
     */
    public function findBy(array $criteria): Collection
    {
        foreach ($criteria as $field => $value) {
            $this->query->where($field, $value);
        }
        
        $result = $this->query->get();
        $this->resetQuery();
        return $result;
    }

    /**
     * Find first record by criteria
     */
    public function findOneBy(array $criteria): ?Model
    {
        foreach ($criteria as $field => $value) {
            $this->query->where($field, $value);
        }
        
        $result = $this->query->first();
        $this->resetQuery();
        return $result;
    }

    /**
     * Count records
     */
    public function count(): int
    {
        $result = $this->query->count();
        $this->resetQuery();
        return $result;
    }

    /**
     * Get records with relationships
     */
    public function with(array $relations): self
    {
        $this->query->with($relations);
        return $this;
    }

    /**
     * Process large datasets in chunks to prevent memory leaks
     */
    public function chunk(int $count, callable $callback): bool
    {
        $result = $this->query->chunk($count, $callback);
        $this->resetQuery();
        return $result;
    }

    /**
     * Use cursor pagination for memory-efficient iteration
     */
    public function lazy(int $chunkSize = 1000): \Illuminate\Support\LazyCollection
    {
        $result = $this->query->lazy($chunkSize);
        $this->resetQuery();
        return $result;
    }

    /**
     * Apply where conditions
     */
    public function where(string $column, $operator = null, $value = null): self
    {
        $this->query->where($column, $operator, $value);
        return $this;
    }

    /**
     * Apply order by
     */
    public function orderBy(string $column, string $direction = 'asc'): self
    {
        $this->query->orderBy($column, $direction);
        return $this;
    }

    /**
     * Apply limit
     */
    public function limit(int $limit): self
    {
        $this->query->limit($limit);
        return $this;
    }

    /**
     * Apply user scope for multi-tenant data
     */
    public function forUser(int $userId): self
    {
        $this->query->where('user_id', $userId);
        return $this;
    }

    /**
     * Apply search functionality
     */
    public function search(string $term, array $fields): self
    {
        $this->query->where(function ($query) use ($term, $fields) {
            foreach ($fields as $field) {
                $query->orWhere($field, 'like', "%{$term}%");
            }
        });
        return $this;
    }

    /**
     * Apply date range filter
     */
    public function whereDateBetween(string $column, string $startDate, string $endDate): self
    {
        $this->query->whereBetween($column, [$startDate, $endDate]);
        return $this;
    }

    /**
     * Get the current query builder
     */
    public function getQuery(): Builder
    {
        return $this->query;
    }
}
