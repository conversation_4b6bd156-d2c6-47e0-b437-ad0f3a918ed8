<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Subscription Management') }}
            </h2>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="mb-6">
                <div class="flex justify-between items-center">
                    <div>
                        <p class="mt-1 text-sm text-gray-600">
                            Manage user subscriptions, payments, and plan changes
                        </p>
                    </div>
                    <div>
                        <a href="{{ route('admin.subscriptions.export') }}"
                           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-download mr-2"></i>
                            Export
                        </a>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-users text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Total Subscriptions</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ number_format($stats['total_subscriptions']) }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-check text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Active Subscriptions</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ number_format($stats['active_subscriptions']) }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-clock text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Pending Subscriptions</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ number_format($stats['pending_subscriptions']) }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-dollar-sign text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Monthly Revenue</dt>
                                    <dd class="text-lg font-medium text-gray-900">${{ number_format($stats['monthly_revenue'], 2) }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-4 py-5 sm:p-6">
                    <form method="GET" action="{{ route('admin.subscriptions.index') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                            <select name="status" id="status" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                <option value="all" {{ $status === 'all' ? 'selected' : '' }}>All Statuses</option>
                                <option value="active" {{ $status === 'active' ? 'selected' : '' }}>Active</option>
                                <option value="pending" {{ $status === 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="cancelled" {{ $status === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                <option value="expired" {{ $status === 'expired' ? 'selected' : '' }}>Expired</option>
                            </select>
                        </div>

                        <div>
                            <label for="plan" class="block text-sm font-medium text-gray-700">Plan</label>
                            <select name="plan" id="plan" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                <option value="all" {{ $plan === 'all' ? 'selected' : '' }}>All Plans</option>
                                @foreach($plans as $planOption)
                                    <option value="{{ $planOption->id }}" {{ $plan == $planOption->id ? 'selected' : '' }}>
                                        {{ $planOption->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div>
                            <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                            <input type="text" name="search" id="search" value="{{ $search }}" 
                                   placeholder="User name or email..." 
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div class="flex items-end">
                            <button type="submit" class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Subscriptions Table -->
            <div class="bg-white shadow overflow-hidden sm:rounded-md">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Subscriptions</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500">
                        {{ $subscriptions->total() }} total subscriptions found
                    </p>
                </div>
                <ul class="divide-y divide-gray-200">
                    @forelse($subscriptions as $subscription)
                        <li>
                            <div class="px-4 py-4 flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                            <span class="text-sm font-medium text-gray-700">
                                                {{ substr($subscription->user->name, 0, 2) }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="flex items-center">
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ $subscription->user->name }}
                                            </div>
                                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                {{ $subscription->status === 'active' ? 'bg-green-100 text-green-800' : '' }}
                                                {{ $subscription->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : '' }}
                                                {{ $subscription->status === 'cancelled' ? 'bg-red-100 text-red-800' : '' }}
                                                {{ $subscription->status === 'expired' ? 'bg-gray-100 text-gray-800' : '' }}">
                                                {{ ucfirst($subscription->status) }}
                                            </span>
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            {{ $subscription->user->email }} • {{ $subscription->plan->name }}
                                        </div>
                                        <div class="text-xs text-gray-400">
                                            Started: {{ $subscription->starts_at?->format('M d, Y') }} • 
                                            Amount: ${{ number_format($subscription->amount_paid, 2) }}
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <a href="{{ route('admin.subscriptions.show', $subscription) }}" 
                                       class="text-blue-600 hover:text-blue-900 text-sm font-medium">
                                        View Details
                                    </a>
                                </div>
                            </div>
                        </li>
                    @empty
                        <li class="px-4 py-12 text-center">
                            <div class="text-gray-500">
                                <i class="fas fa-inbox text-4xl mb-4"></i>
                                <p>No subscriptions found</p>
                            </div>
                        </li>
                    @endforelse
                </ul>
            </div>

            <!-- Pagination -->
            @if($subscriptions->hasPages())
                <div class="mt-6">
                    {{ $subscriptions->appends(request()->query())->links() }}
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
