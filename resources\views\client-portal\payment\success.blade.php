@extends('client-portal.layout')

@section('content')
<div class="max-w-2xl mx-auto">
    <div class="text-center">
        <!-- Success Icon -->
        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
            <i class="fas fa-check text-green-600 text-2xl"></i>
        </div>

        <!-- Success Message -->
        <h2 class="text-3xl font-bold text-gray-900 mb-2">Payment Successful!</h2>
        <p class="text-lg text-gray-600 mb-8">
            Thank you for your payment. Your transaction has been processed successfully.
        </p>

        <!-- Payment Details -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8 text-left">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Payment Details</h3>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-gray-600">Invoice Number:</span>
                    <span class="font-medium text-gray-900">{{ $invoice->invoice_number }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Payment Amount:</span>
                    <span class="font-medium text-gray-900">{{ $payment->formatted_amount }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Payment Method:</span>
                    <span class="font-medium text-gray-900">{{ ucfirst($payment->gateway) }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Transaction ID:</span>
                    <span class="font-medium text-gray-900">{{ $payment->payment_id }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Payment Date:</span>
                    <span class="font-medium text-gray-900">{{ $payment->paid_at->format('M d, Y g:i A') }}</span>
                </div>
            </div>
        </div>

        <!-- Updated Payment Summary -->
        @if(!$paymentSummary['is_fully_paid'])
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <div class="flex items-center justify-center mb-2">
                    <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                    <span class="text-blue-800 font-medium">Partial Payment Received</span>
                </div>
                <p class="text-blue-700 text-sm">
                    Remaining amount: {{ config('services.currency.symbol', '₹') }}{{ number_format($paymentSummary['remaining_amount'], 2) }}
                </p>
            </div>
        @else
            <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <div class="flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-600 mr-2"></i>
                    <span class="text-green-800 font-medium">Invoice Fully Paid</span>
                </div>
            </div>
        @endif

        <!-- Action Buttons -->
        <div class="space-y-4">
            <a href="{{ route('client-portal.invoice.view', ['invoice' => $invoice->id, 'token' => $invoice->public_token]) }}" 
               class="w-full inline-flex justify-center items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500">
                <i class="fas fa-file-invoice mr-2"></i>
                View Invoice
            </a>
            
            <a href="{{ route('client-portal.invoice.download', ['invoice' => $invoice->id, 'token' => $invoice->public_token]) }}" 
               class="w-full inline-flex justify-center items-center px-6 py-3 border border-gray-300 shadow-sm text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500">
                <i class="fas fa-download mr-2"></i>
                Download Invoice PDF
            </a>
        </div>

        <!-- Additional Information -->
        <div class="mt-8 text-sm text-gray-500">
            <p>
                A payment confirmation has been sent to {{ $invoice->client->email }}. 
                Please keep this page for your records.
            </p>
        </div>
    </div>
</div>
@endsection
