<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ $project->name }}</h1>
                <p class="text-gray-600 mt-1">Project Dashboard</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('projects.show', $project) }}" class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2.5 px-4 rounded-lg transition-colors">
                    <i class="fas fa-eye mr-2"></i>View Details
                </a>
                @can('update', $project)
                    <a href="{{ route('projects.edit', $project) }}" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-lg transition-colors">
                        <i class="fas fa-edit mr-2"></i>Edit Project
                    </a>
                @endcan
                <a href="{{ route('projects.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2.5 px-4 rounded-lg transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Projects
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Project Status Overview -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Project Status -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-project-diagram text-blue-600"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Status</p>
                            <p class="text-lg font-semibold text-gray-900">
                                {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Total Tasks -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-tasks text-green-600"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Tasks</p>
                            <p class="text-lg font-semibold text-gray-900">{{ $projectStats['total_tasks'] }}</p>
                        </div>
                    </div>
                </div>

                <!-- Completed Tasks -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-check-circle text-purple-600"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Completed</p>
                            <p class="text-lg font-semibold text-gray-900">{{ $projectStats['completed_tasks'] }}</p>
                        </div>
                    </div>
                </div>

                <!-- Time Spent -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-clock text-orange-600"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Time Spent</p>
                            <p class="text-lg font-semibold text-gray-900">
                                {{ number_format($projectStats['total_time_spent'] / 60, 1) }}h
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Progress and Budget Overview -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- Progress Chart -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Project Progress</h3>
                    
                    <!-- Progress Bar -->
                    <div class="mb-4">
                        <div class="flex justify-between text-sm text-gray-600 mb-2">
                            <span>Completion</span>
                            <span>{{ $projectStats['completion_percentage'] }}%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <div class="bg-blue-600 h-3 rounded-full transition-all duration-300" 
                                 style="width: {{ $projectStats['completion_percentage'] }}%"></div>
                        </div>
                    </div>

                    <!-- Task Breakdown -->
                    <div class="grid grid-cols-2 gap-4 text-center">
                        <div class="p-3 bg-green-50 rounded-lg">
                            <p class="text-2xl font-bold text-green-600">{{ $projectStats['completed_tasks'] }}</p>
                            <p class="text-sm text-green-700">Completed</p>
                        </div>
                        <div class="p-3 bg-yellow-50 rounded-lg">
                            <p class="text-2xl font-bold text-yellow-600">{{ $projectStats['pending_tasks'] }}</p>
                            <p class="text-sm text-yellow-700">Pending</p>
                        </div>
                    </div>
                </div>

                <!-- Budget Overview -->
                @if($project->budget)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Budget Overview</h3>
                    
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Total Budget</span>
                            <span class="font-semibold text-gray-900">₹{{ number_format($project->budget, 0) }}</span>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Amount Used</span>
                            <span class="font-semibold text-gray-900">₹{{ number_format($projectStats['total_budget_used'], 0) }}</span>
                        </div>
                        
                        @if($projectStats['budget_remaining'])
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Remaining</span>
                            <span class="font-semibold {{ $projectStats['is_over_budget'] ? 'text-red-600' : 'text-green-600' }}">
                                ₹{{ number_format($projectStats['budget_remaining'], 0) }}
                            </span>
                        </div>
                        @endif

                        <!-- Budget Progress Bar -->
                        @php
                            $budgetPercentage = $project->budget > 0 ? min(($projectStats['total_budget_used'] / $project->budget) * 100, 100) : 0;
                        @endphp
                        <div class="mt-4">
                            <div class="flex justify-between text-sm text-gray-600 mb-2">
                                <span>Budget Used</span>
                                <span>{{ number_format($budgetPercentage, 1) }}%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-3">
                                <div class="h-3 rounded-full transition-all duration-300 {{ $projectStats['is_over_budget'] ? 'bg-red-500' : 'bg-green-500' }}" 
                                     style="width: {{ $budgetPercentage }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                @else
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Budget Overview</h3>
                    <div class="text-center py-8">
                        <i class="fas fa-dollar-sign text-gray-300 text-4xl mb-4"></i>
                        <p class="text-gray-500">No budget set for this project</p>
                        @can('update', $project)
                            <a href="{{ route('projects.edit', $project) }}" class="text-blue-600 hover:text-blue-800 text-sm mt-2 inline-block">
                                Set Budget
                            </a>
                        @endcan
                    </div>
                </div>
                @endif
            </div>

            <!-- Recent Activity and Team -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Recent Tasks -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Recent Tasks</h3>
                        <a href="{{ route('projects.show', $project) }}#tasks" class="text-blue-600 hover:text-blue-800 text-sm">
                            View All
                        </a>
                    </div>
                    
                    @if($project->tasks->count() > 0)
                        <div class="space-y-3">
                            @foreach($project->tasks->take(5) as $task)
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div class="flex-1">
                                        <p class="font-medium text-gray-900">{{ $task->title }}</p>
                                        <p class="text-sm text-gray-600">
                                            {{ $task->assignedUser ? $task->assignedUser->name : 'Unassigned' }}
                                        </p>
                                    </div>
                                    <span class="px-2 py-1 text-xs font-medium rounded-full
                                        @if($task->status === 'completed') bg-green-100 text-green-800
                                        @elseif($task->status === 'in_progress') bg-blue-100 text-blue-800
                                        @elseif($task->status === 'review') bg-yellow-100 text-yellow-800
                                        @else bg-gray-100 text-gray-800
                                        @endif">
                                        {{ ucfirst(str_replace('_', ' ', $task->status)) }}
                                    </span>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <i class="fas fa-tasks text-gray-300 text-4xl mb-4"></i>
                            <p class="text-gray-500">No tasks created yet</p>
                            @can('create', App\Models\Task::class)
                                <a href="{{ route('projects.tasks.create', $project) }}" class="text-blue-600 hover:text-blue-800 text-sm mt-2 inline-block">
                                    Create First Task
                                </a>
                            @endcan
                        </div>
                    @endif
                </div>

                <!-- Team Members -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Team Members</h3>
                        <span class="text-sm text-gray-600">{{ $projectStats['team_members'] }} members</span>
                    </div>
                    
                    @if($project->projectMembers->count() > 0)
                        <div class="space-y-3">
                            @foreach($project->projectMembers as $member)
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                            <span class="text-sm font-medium text-blue-600">
                                                {{ substr($member->user->name, 0, 1) }}
                                            </span>
                                        </div>
                                        <div class="ml-3">
                                            <p class="font-medium text-gray-900">{{ $member->user->name }}</p>
                                            <p class="text-sm text-gray-600">{{ $member->user->email }}</p>
                                        </div>
                                    </div>
                                    <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                                        {{ ucfirst($member->role) }}
                                    </span>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <i class="fas fa-users text-gray-300 text-4xl mb-4"></i>
                            <p class="text-gray-500">No team members added yet</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
