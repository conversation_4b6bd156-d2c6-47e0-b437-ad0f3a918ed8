<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('businesses', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->enum('type', ['freelancer', 'startup', 'small_business'])->default('freelancer');
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('website')->nullable();
            $table->text('address')->nullable();
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('country')->nullable();
            $table->string('postal_code')->nullable();
            $table->string('tax_number')->nullable(); // GST/VAT/Tax ID
            $table->string('registration_number')->nullable();
            $table->string('logo_path')->nullable();
            $table->text('description')->nullable();
            $table->string('industry')->nullable();
            $table->integer('employee_count')->nullable();
            $table->year('founded_year')->nullable();
            $table->boolean('is_active')->default(true);
            $table->foreignId('subscription_plan_id')->nullable()->constrained('plans')->onDelete('set null');
            $table->timestamp('trial_ends_at')->nullable();
            $table->json('settings')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['type', 'is_active']);
            $table->index('slug');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('businesses');
    }
};
