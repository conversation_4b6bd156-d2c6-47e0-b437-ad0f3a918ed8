<?php

namespace Database\Seeders;

use App\Models\SiteSetting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SiteSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'site.name',
                'value' => env('APP_NAME', 'Freeligo'),
                'type' => 'string',
                'category' => 'general',
                'label' => 'Site Name',
                'description' => 'The name of your website',
                'is_public' => true,
                'is_required' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'site.description',
                'value' => 'Run Your Freelance Business Like a Pro',
                'type' => 'string',
                'category' => 'general',
                'label' => 'Site Description',
                'description' => 'A brief description of your website',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'site.timezone',
                'value' => 'UTC',
                'type' => 'string',
                'category' => 'general',
                'label' => 'Default Timezone',
                'description' => 'Default timezone for the application',
                'sort_order' => 3,
            ],
            [
                'key' => 'site.locale',
                'value' => 'en',
                'type' => 'string',
                'category' => 'general',
                'label' => 'Default Language',
                'description' => 'Default language for the application',
                'sort_order' => 4,
            ],

            // Branding Settings
            [
                'key' => 'brand.primary_color',
                'value' => env('BRAND_COLOR', '#10B981'),
                'type' => 'string',
                'category' => 'branding',
                'label' => 'Primary Color',
                'description' => 'Main brand color (hex code)',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'brand.secondary_color',
                'value' => env('BRAND_GRADIENT_FROM', '#059669'),
                'type' => 'string',
                'category' => 'branding',
                'label' => 'Secondary Color',
                'description' => 'Secondary brand color (hex code)',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'brand.logo_url',
                'value' => '',
                'type' => 'string',
                'category' => 'branding',
                'label' => 'Logo URL',
                'description' => 'URL to your site logo',
                'is_public' => true,
                'sort_order' => 3,
            ],
            [
                'key' => 'brand.favicon_url',
                'value' => '',
                'type' => 'string',
                'category' => 'branding',
                'label' => 'Favicon URL',
                'description' => 'URL to your site favicon',
                'is_public' => true,
                'sort_order' => 4,
            ],

            // Currency Settings
            [
                'key' => 'currency.code',
                'value' => env('APP_CURRENCY', 'USD'),
                'type' => 'string',
                'category' => 'currency',
                'label' => 'Currency Code',
                'description' => 'Default currency code (USD, EUR, INR, etc.)',
                'is_public' => true,
                'is_required' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'currency.symbol',
                'value' => env('APP_CURRENCY_SYMBOL', '$'),
                'type' => 'string',
                'category' => 'currency',
                'label' => 'Currency Symbol',
                'description' => 'Currency symbol to display',
                'is_public' => true,
                'is_required' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'currency.position',
                'value' => 'before',
                'type' => 'string',
                'category' => 'currency',
                'label' => 'Symbol Position',
                'description' => 'Position of currency symbol',
                'options' => ['before' => 'Before Amount ($100)', 'after' => 'After Amount (100$)'],
                'sort_order' => 3,
            ],
            [
                'key' => 'currency.decimal_places',
                'value' => '2',
                'type' => 'integer',
                'category' => 'currency',
                'label' => 'Decimal Places',
                'description' => 'Number of decimal places to show',
                'sort_order' => 4,
            ],

            // Email Settings
            [
                'key' => 'mail.from_name',
                'value' => env('MAIL_FROM_NAME', env('APP_NAME')),
                'type' => 'string',
                'category' => 'email',
                'label' => 'From Name',
                'description' => 'Default sender name for emails',
                'is_required' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'mail.from_address',
                'value' => env('MAIL_FROM_ADDRESS', '<EMAIL>'),
                'type' => 'string',
                'category' => 'email',
                'label' => 'From Email Address',
                'description' => 'Default sender email address',
                'validation_rules' => json_encode(['required', 'email']),
                'is_required' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'mail.reply_to',
                'value' => '',
                'type' => 'string',
                'category' => 'email',
                'label' => 'Reply To Email',
                'description' => 'Email address for replies (optional)',
                'validation_rules' => json_encode(['nullable', 'email']),
                'sort_order' => 3,
            ],
        ];

        foreach ($settings as $setting) {
            SiteSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
