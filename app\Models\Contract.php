<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Contract extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'client_id',
        'proposal_id',
        'contract_template_id',
        'title',
        'content',
        'variables',
        'status',
        'pdf_path',
        'sent_date',
        'signed_date',
        'expiry_date',
        'signed_by',
        'cancelled_date',
    ];

    protected function casts(): array
    {
        return [
            'variables' => 'array',
            'sent_date' => 'date',
            'signed_date' => 'date',
            'expiry_date' => 'date',
            'cancelled_date' => 'date',
        ];
    }

    /**
     * Get the user that owns the contract.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the client that owns the contract.
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Get the proposal that created this contract.
     */
    public function proposal(): BelongsTo
    {
        return $this->belongsTo(Proposal::class);
    }

    /**
     * Get the contract template that was used.
     */
    public function contractTemplate(): BelongsTo
    {
        return $this->belongsTo(ContractTemplate::class);
    }
}
