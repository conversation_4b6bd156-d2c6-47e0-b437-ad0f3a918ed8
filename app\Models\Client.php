<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

class Client extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'email',
        'phone',
        'company_name',
        'address',
        'gst_number',
        'contact_person',
        'default_tds_percentage',
    ];

    protected function casts(): array
    {
        return [
            'default_tds_percentage' => 'float',
        ];
    }

    /**
     * Get the business user that owns the client.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the invoices for the client.
     */
    public function invoices(): Has<PERSON>any
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Get the contracts for the client.
     */
    public function contracts(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Contract::class);
    }

    /**
     * Get the proposals for the client.
     */
    public function proposals(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(Proposal::class);
    }

    /**
     * Get the projects for the client.
     */
    public function projects(): HasMany
    {
        return $this->hasMany(Project::class);
    }

    /**
     * Get the TDS records for the client.
     */
    public function tdsRecords(): HasMany
    {
        return $this->hasMany(TdsRecord::class);
    }

    /**
     * Get the follow-ups for the client through invoices.
     */
    public function followUps(): HasManyThrough
    {
        return $this->hasManyThrough(FollowUp::class, Invoice::class);
    }

    /**
     * Get the expenses for the client.
     */
    public function expenses(): HasMany
    {
        return $this->hasMany(Expense::class);
    }

    /**
     * Get display name for client.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->company_name ?: $this->name;
    }

}
