<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LeadActivity extends Model
{
    use HasFactory;

    protected $fillable = [
        'lead_id',
        'user_id',
        'type',
        'title',
        'description',
        'scheduled_at',
        'completed_at',
        'status',
        'outcome',
        'outcome_notes',
        'duration_minutes',
        'is_automated',
        'automation_trigger',
        'metadata',
    ];

    protected function casts(): array
    {
        return [
            'scheduled_at' => 'datetime',
            'completed_at' => 'datetime',
            'duration_minutes' => 'integer',
            'is_automated' => 'boolean',
            'metadata' => 'array',
        ];
    }

    /**
     * Get the lead that owns the activity.
     */
    public function lead(): BelongsTo
    {
        return $this->belongsTo(Lead::class);
    }

    /**
     * Get the user that created the activity.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope a query to only include completed activities.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope a query to only include scheduled activities.
     */
    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    /**
     * Scope a query to only include overdue activities.
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', 'scheduled')
                    ->where('scheduled_at', '<', now());
    }

    /**
     * Scope a query by activity type.
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope a query for recent activities.
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Get the activity type icon.
     */
    public function getTypeIconAttribute(): string
    {
        return match($this->type) {
            'call' => 'fas fa-phone',
            'email' => 'fas fa-envelope',
            'meeting' => 'fas fa-calendar',
            'proposal_sent' => 'fas fa-file-alt',
            'follow_up' => 'fas fa-clock',
            'note' => 'fas fa-sticky-note',
            'stage_change' => 'fas fa-arrow-right',
            'score_change' => 'fas fa-chart-line',
            'document_sent' => 'fas fa-file-pdf',
            'website_visit' => 'fas fa-globe',
            'social_interaction' => 'fas fa-share-alt',
            'referral' => 'fas fa-user-friends',
            default => 'fas fa-circle',
        };
    }

    /**
     * Get the activity type color.
     */
    public function getTypeColorAttribute(): string
    {
        return match($this->type) {
            'call' => '#10B981',
            'email' => '#3B82F6',
            'meeting' => '#8B5CF6',
            'proposal_sent' => '#F59E0B',
            'follow_up' => '#06B6D4',
            'note' => '#6B7280',
            'stage_change' => '#10B981',
            'score_change' => '#8B5CF6',
            'document_sent' => '#EF4444',
            'website_visit' => '#F59E0B',
            'social_interaction' => '#8B5CF6',
            'referral' => '#10B981',
            default => '#6B7280',
        };
    }

    /**
     * Get the outcome color.
     */
    public function getOutcomeColorAttribute(): string
    {
        return match($this->outcome) {
            'positive' => '#10B981',
            'neutral' => '#F59E0B',
            'negative' => '#EF4444',
            default => '#6B7280',
        };
    }

    /**
     * Check if activity is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->status === 'scheduled' && 
               $this->scheduled_at && 
               $this->scheduled_at->isPast();
    }

    /**
     * Mark activity as completed.
     */
    public function markAsCompleted(string $outcome = null, string $notes = null): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
            'outcome' => $outcome,
            'outcome_notes' => $notes,
        ]);

        // Update lead's last contacted timestamp
        $this->lead->update(['last_contacted_at' => now()]);
        
        // Update lead score if this was a significant interaction
        if (in_array($this->type, ['call', 'meeting', 'proposal_sent'])) {
            $this->lead->updateLeadScore();
        }
    }

    /**
     * Mark activity as missed.
     */
    public function markAsMissed(string $reason = null): void
    {
        $this->update([
            'status' => 'missed',
            'outcome' => 'negative',
            'outcome_notes' => $reason,
        ]);
    }

    /**
     * Reschedule activity.
     */
    public function reschedule(\Carbon\Carbon $newDate): void
    {
        $this->update([
            'scheduled_at' => $newDate,
            'status' => 'scheduled',
        ]);
    }

    /**
     * Get formatted duration.
     */
    public function getFormattedDurationAttribute(): string
    {
        if (!$this->duration_minutes) {
            return 'N/A';
        }

        $hours = intval($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;

        if ($hours > 0) {
            return $hours . 'h ' . $minutes . 'm';
        }

        return $minutes . 'm';
    }
}
