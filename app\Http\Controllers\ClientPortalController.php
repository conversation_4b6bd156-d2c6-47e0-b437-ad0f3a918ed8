<?php

namespace App\Http\Controllers;

use App\Http\Requests\ClientPortalPaymentRequest;
use App\Models\Invoice;
use App\Models\InvoicePayment;
use App\Services\ClientPortalService;
use App\Services\PayPalService;
use App\Services\RazorpayService;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ClientPortalController extends Controller
{
    protected ClientPortalService $clientPortalService;

    public function __construct(ClientPortalService $clientPortalService)
    {
        $this->clientPortalService = $clientPortalService;
    }

    /**
     * Display invoice for client viewing.
     */
    public function viewInvoice(int $invoiceId, string $token)
    {
        $invoice = $this->clientPortalService->getInvoiceByPublicToken($invoiceId, $token);

        if (!$invoice) {
            abort(404, 'Invoice not found or invalid access token');
        }

        $paymentSummary = $this->clientPortalService->getPaymentSummary($invoice);
        $invoiceStats = $this->clientPortalService->getInvoiceStats($invoice);
        $canMakePayment = $this->clientPortalService->canMakePayment($invoice);

        return view('client-portal.invoice.view', compact(
            'invoice',
            'paymentSummary',
            'invoiceStats',
            'canMakePayment'
        ));
    }

    /**
     * Download invoice PDF for client.
     */
    public function downloadInvoice(int $invoiceId, string $token)
    {
        $invoice = $this->clientPortalService->getInvoiceByPublicToken($invoiceId, $token);

        if (!$invoice) {
            abort(404, 'Invoice not found or invalid access token');
        }

        try {
            $pdf = Pdf::loadView('invoices.pdf', compact('invoice'));
            return $pdf->download($invoice->invoice_number . '.pdf');
        } catch (\Exception $e) {
            Log::error('Client portal PDF generation failed', [
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage()
            ]);

            return redirect()->back()
                ->with('error', 'Failed to generate PDF. Please try again.');
        }
    }

    /**
     * Show payment gateway selection page.
     */
    public function showPaymentGateway(int $invoiceId, string $token)
    {
        $invoice = $this->clientPortalService->getInvoiceByPaymentToken($invoiceId, $token);

        if (!$invoice) {
            abort(404, 'Invoice not found or invalid payment token');
        }

        $canMakePayment = $this->clientPortalService->canMakePayment($invoice);
        if (!$canMakePayment['can_pay']) {
            return redirect()->route('client-portal.invoice.view', [
                'invoice' => $invoiceId,
                'token' => $invoice->public_token
            ])->with('error', $canMakePayment['reason']);
        }

        $paymentSummary = $this->clientPortalService->getPaymentSummary($invoice);

        // Check if international version (USD currency)
        $isInternational = config('services.currency.code') === 'USD';

        return view('client-portal.payment.gateway', compact(
            'invoice',
            'paymentSummary',
            'isInternational'
        ));
    }

    /**
     * Process PayPal payment for invoice.
     */
    public function processPayPalPayment(ClientPortalPaymentRequest $request, int $invoiceId, string $token, PayPalService $paypalService)
    {
        $invoice = $this->clientPortalService->getInvoiceByPaymentToken($invoiceId, $token);

        if (!$invoice) {
            abort(404, 'Invoice not found or invalid payment token');
        }

        $canMakePayment = $this->clientPortalService->canMakePayment($invoice);
        if (!$canMakePayment['can_pay']) {
            return redirect()->route('client-portal.invoice.view', [
                'invoice' => $invoiceId,
                'token' => $invoice->public_token
            ])->with('error', $canMakePayment['reason']);
        }

        $validated = $request->validated();
        $amount = $validated['amount'];

        try {
            // Create invoice payment record
            $payment = $this->clientPortalService->createInvoicePayment($invoice, [
                'gateway' => 'paypal',
                'amount' => $amount,
                'currency' => config('services.currency.code', 'USD'),
            ]);

            // Create PayPal order
            $orderData = $paypalService->createInvoicePaymentOrder($payment);

            return view('client-portal.payment.paypal-checkout', compact(
                'invoice',
                'payment',
                'orderData'
            ));

        } catch (\Exception $e) {
            Log::error('PayPal invoice payment creation failed', [
                'invoice_id' => $invoice->id,
                'amount' => $amount,
                'error' => $e->getMessage()
            ]);

            return redirect()->back()
                ->with('error', 'Payment processing failed. Please try again.');
        }
    }

    /**
     * Process Razorpay payment for invoice.
     */
    public function processRazorpayPayment(ClientPortalPaymentRequest $request, int $invoiceId, string $token, RazorpayService $razorpayService)
    {
        $invoice = $this->clientPortalService->getInvoiceByPaymentToken($invoiceId, $token);

        if (!$invoice) {
            abort(404, 'Invoice not found or invalid payment token');
        }

        $canMakePayment = $this->clientPortalService->canMakePayment($invoice);
        if (!$canMakePayment['can_pay']) {
            return redirect()->route('client-portal.invoice.view', [
                'invoice' => $invoiceId,
                'token' => $invoice->public_token
            ])->with('error', $canMakePayment['reason']);
        }

        $validated = $request->validated();
        $amount = $validated['amount'];

        try {
            // Create invoice payment record
            $payment = $this->clientPortalService->createInvoicePayment($invoice, [
                'gateway' => 'razorpay',
                'amount' => $amount,
                'currency' => 'INR',
            ]);

            // Create Razorpay order
            $orderData = $razorpayService->createInvoicePaymentOrder($payment);

            return view('client-portal.payment.razorpay-checkout', compact(
                'invoice',
                'payment',
                'orderData'
            ));

        } catch (\Exception $e) {
            Log::error('Razorpay invoice payment creation failed', [
                'invoice_id' => $invoice->id,
                'amount' => $amount,
                'error' => $e->getMessage()
            ]);

            return redirect()->back()
                ->with('error', 'Payment processing failed. Please try again.');
        }
    }

    /**
     * Handle successful payment callback.
     */
    public function paymentSuccess(Request $request, int $invoiceId, string $token)
    {
        $invoice = $this->clientPortalService->getInvoiceByPaymentToken($invoiceId, $token);

        if (!$invoice) {
            abort(404, 'Invoice not found or invalid payment token');
        }

        $paymentId = $request->input('payment_id');
        $payment = InvoicePayment::where('payment_id', $paymentId)
            ->where('invoice_id', $invoice->id)
            ->first();

        if (!$payment) {
            return redirect()->route('client-portal.invoice.view', [
                'invoice' => $invoiceId,
                'token' => $invoice->public_token
            ])->with('error', 'Payment record not found');
        }

        $paymentSummary = $this->clientPortalService->getPaymentSummary($invoice);

        return view('client-portal.payment.success', compact(
            'invoice',
            'payment',
            'paymentSummary'
        ));
    }

    /**
     * Handle failed payment callback.
     */
    public function paymentFailure(Request $request, int $invoiceId, string $token)
    {
        $invoice = $this->clientPortalService->getInvoiceByPaymentToken($invoiceId, $token);

        if (!$invoice) {
            abort(404, 'Invoice not found or invalid payment token');
        }

        $paymentId = $request->input('payment_id');
        $failureReason = $request->input('reason', 'Payment failed');

        try {
            $payment = InvoicePayment::where('payment_id', $paymentId)
                ->where('invoice_id', $invoice->id)
                ->first();

            if ($payment) {
                $this->clientPortalService->processFailedPayment($payment, $failureReason);
            }
        } catch (\Exception $e) {
            \Log::error('Failed to process payment failure', [
                'error' => $e->getMessage(),
                'invoice_id' => $invoice->id,
                'payment_id' => $paymentId,
                'reason' => $failureReason
            ]);
        }

        return view('client-portal.payment.failure', compact(
            'invoice',
            'failureReason'
        ));
    }
}
