<?php

namespace App\Services;

use App\Models\User;
use App\Repositories\InvoiceRepository;
use App\Repositories\ClientRepository;
use App\Repositories\ContractRepository;
use App\Repositories\ExpenseRepository;
use App\Repositories\LeadRepository;
use App\Repositories\TdsRepository;
use App\Traits\HasCalculations;
use App\Traits\HasDateRanges;
use App\Services\PlanChecker;
use Illuminate\Support\Facades\Auth;

class DashboardService
{
    use HasCalculations, HasDateRanges;

    protected InvoiceRepository $invoiceRepository;
    protected ClientRepository $clientRepository;
    protected ContractRepository $contractRepository;
    protected ExpenseRepository $expenseRepository;
    protected LeadRepository $leadRepository;
    protected TdsRepository $tdsRepository;

    public function __construct(
        InvoiceRepository $invoiceRepository,
        ClientRepository $clientRepository,
        ContractRepository $contractRepository,
        ExpenseRepository $expenseRepository,
        LeadRepository $leadRepository,
        TdsRepository $tdsRepository
    ) {
        $this->invoiceRepository = $invoiceRepository;
        $this->clientRepository = $clientRepository;
        $this->contractRepository = $contractRepository;
        $this->expenseRepository = $expenseRepository;
        $this->leadRepository = $leadRepository;
        $this->tdsRepository = $tdsRepository;
    }

    /**
     * Get complete dashboard data for user
     */
    public function getDashboardData(int $userId): array
    {
        return [
            'overview_stats' => $this->getOverviewStats($userId),
            'financial_metrics' => $this->getFinancialMetrics($userId),
            'recent_activities' => $this->getRecentActivities($userId),
            'analytics_data' => $this->getAnalyticsData($userId),
            'plan_limitations' => $this->getPlanLimitations($userId),
            'alerts_notifications' => $this->getAlertsAndNotifications($userId),
        ];
    }

    /**
     * Get overview statistics
     */
    public function getOverviewStats(int $userId): array
    {
        $invoiceStats = $this->invoiceRepository->getStatsForUser($userId);
        $clientStats = $this->clientRepository->getStatsForUser($userId);
        $contractStats = $this->contractRepository->getStatsForUser($userId);
        $expenseStats = $this->expenseRepository->getStatsForUser($userId);
        $leadStats = $this->leadRepository->getConversionAnalytics($userId);

        return [
            'total_clients' => $clientStats['total_count'],
            'total_invoices' => $invoiceStats['total_count'],
            'total_contracts' => $contractStats['total_count'],
            'total_expenses' => $expenseStats['total_expenses'],
            'total_leads' => $leadStats['total_leads'],
            'active_leads' => $leadStats['active_leads'],
            'converted_leads' => $leadStats['converted_leads'],
            'total_invoice_amount' => $invoiceStats['total_amount'],
            'total_expense_amount' => $expenseStats['total_expense_amount'],
            'paid_invoice_amount' => $invoiceStats['paid_amount'],
            'pending_invoice_amount' => $invoiceStats['pending_amount'],
            'overdue_invoice_amount' => $invoiceStats['overdue_amount'],
            'paid_invoices' => $invoiceStats['paid_count'],
            'pending_invoices' => $invoiceStats['pending_count'],
            'draft_invoices' => $invoiceStats['draft_count'],
            'overdue_invoices' => $invoiceStats['overdue_count'],
            'approved_expenses' => $expenseStats['approved_expenses'],
            'pending_expenses' => $expenseStats['pending_expenses'],
            'draft_expenses' => $expenseStats['draft_expenses'],
        ];
    }

    /**
     * Get financial metrics
     */
    public function getFinancialMetrics(int $userId): array
    {
        [$currentMonthStart, $currentMonthEnd] = $this->getCurrentMonthRange();
        [$previousMonthStart, $previousMonthEnd] = $this->getPreviousMonthRange();

        // Current month revenue
        $currentMonthInvoices = $this->invoiceRepository->searchInvoices($userId, [
            'status' => 'paid',
            'start_date' => $currentMonthStart->format('Y-m-d'),
            'end_date' => $currentMonthEnd->format('Y-m-d'),
        ]);
        $currentMonthRevenue = $currentMonthInvoices->sum('total_amount');

        // Previous month revenue
        $previousMonthInvoices = $this->invoiceRepository->searchInvoices($userId, [
            'status' => 'paid',
            'start_date' => $previousMonthStart->format('Y-m-d'),
            'end_date' => $previousMonthEnd->format('Y-m-d'),
        ]);
        $previousMonthRevenue = $previousMonthInvoices->sum('total_amount');

        // Growth calculations
        $revenueGrowth = $this->calculateGrowthPercentage($currentMonthRevenue, $previousMonthRevenue);
        $invoiceGrowth = $this->calculateGrowthPercentage(
            $currentMonthInvoices->count(),
            $previousMonthInvoices->count()
        );

        // Average invoice value
        $averageInvoiceValue = $this->invoiceRepository->getAverageValueForUser($userId);

        // TDS information
        $tdsStats = $this->tdsRepository->getStatsForUser($userId);
        $currentFinancialYear = $this->getCurrentFinancialYear();

        return [
            'current_month_revenue' => $currentMonthRevenue,
            'previous_month_revenue' => $previousMonthRevenue,
            'revenue_growth' => $revenueGrowth,
            'invoice_growth' => $invoiceGrowth,
            'average_invoice_value' => $averageInvoiceValue,
            'total_tds_amount' => $tdsStats['current_year_tds'],
            'current_financial_year' => $currentFinancialYear,
        ];
    }

    /**
     * Get recent activities
     */
    public function getRecentActivities(int $userId): array
    {
        return [
            'recent_invoices' => $this->invoiceRepository->getRecentForUser($userId, 5),
            'overdue_invoices' => $this->invoiceRepository->getOverdueForUser($userId, 5),
            'recent_contracts' => $this->contractRepository->getRecentForUser($userId, 5),
            'recent_expenses' => $this->expenseRepository->getRecentForUser($userId, 5),
            'recent_clients' => $this->clientRepository->getRecentForUser($userId, 5),
            'pending_follow_ups' => $this->getPendingFollowUps($userId),
        ];
    }

    /**
     * Get analytics data for charts
     */
    public function getAnalyticsData(int $userId): array
    {
        return [
            'monthly_revenue' => $this->getMonthlyRevenueData($userId),
            'monthly_expenses' => $this->getMonthlyExpenseData($userId),
            'expense_categories' => $this->getExpenseCategoryData($userId),
            'expense_analytics' => $this->getExpenseAnalytics($userId),
            'contract_stats' => $this->getContractAnalytics($userId),
            'top_clients' => $this->getTopClientsData($userId),
            'invoice_status_data' => $this->getInvoiceStatusData($userId),
        ];
    }

    /**
     * Get plan limitations and usage
     */
    public function getPlanLimitations(int $userId): array
    {
        $user = User::find($userId);

        // Get current usage
        $invoiceUsage = PlanChecker::getMonthlyInvoiceUsage($user);
        $contractUsage = PlanChecker::getContractUsage($user);
        $expenseUsage = PlanChecker::getMonthlyExpenseUsage($user);
        $planSummary = PlanChecker::getPlanSummary($user);

        // Get plan features based on current plan
        $currentPlan = $user->currentPlan;
        $features = [
            'tds_reports' => $currentPlan ? $currentPlan->features['tds_reports'] ?? false : false,
            'ai_assistant' => $currentPlan ? $currentPlan->features['ai_assistant'] ?? false : false,
        ];

        // Calculate remaining limits
        $remainingInvoices = 'unlimited';
        $remainingContracts = 'unlimited';

        if ($invoiceUsage['limit'] !== 'unlimited') {
            $remainingInvoices = max(0, $invoiceUsage['limit'] - $invoiceUsage['used']);
        }

        if ($contractUsage['limit'] !== 'unlimited') {
            $remainingContracts = max(0, $contractUsage['limit'] - $contractUsage['used']);
        }

        return [
            'invoice_usage' => $invoiceUsage,
            'contract_usage' => $contractUsage,
            'expense_usage' => $expenseUsage,
            'plan_name' => $planSummary['plan_name'],
            'features' => $features,
            'remaining_invoices' => $remainingInvoices,
            'remaining_contracts' => $remainingContracts,
        ];
    }

    /**
     * Get alerts and notifications
     */
    public function getAlertsAndNotifications(int $userId): array
    {
        $overdueInvoices = $this->invoiceRepository->getOverdueForUser($userId);
        $expiringSoonContracts = $this->contractRepository->getExpiringSoon($userId, 30);

        return [
            'overdue_invoices_count' => $overdueInvoices->count(),
            'expiring_contracts_count' => $expiringSoonContracts->count(),
            'payment_reminders' => $this->getPaymentReminders($userId),
            'system_notifications' => $this->getSystemNotifications($userId),
        ];
    }

    /**
     * Get monthly revenue data for charts
     */
    private function getMonthlyRevenueData(int $userId, int $months = 6): array
    {
        $monthlyData = $this->invoiceRepository->getMonthlyRevenueForUser($userId, $months);
        
        return $monthlyData->map(function ($item) {
            return [
                'month' => sprintf('%04d-%02d', $item->year, $item->month),
                'revenue' => $item->revenue,
                'count' => $item->count,
            ];
        })->reverse()->values()->toArray();
    }

    /**
     * Get contract analytics
     */
    private function getContractAnalytics(int $userId): array
    {
        $contractStats = $this->contractRepository->getStatsForUser($userId);
        
        return [
            'total' => $contractStats['total_count'],
            'draft' => $contractStats['draft_count'],
            'sent' => $contractStats['sent_count'],
            'signed' => $contractStats['signed_count'],
            'expired' => $contractStats['expired_count'],
            'cancelled' => $contractStats['cancelled_count'],
        ];
    }

    /**
     * Get top clients data
     */
    private function getTopClientsData(int $userId): array
    {
        $topClients = $this->clientRepository->getTopClientsByRevenue($userId, 5);
        
        return $topClients->map(function ($client) {
            return [
                'name' => $client->name,
                'company_name' => $client->company_name,
                'total_revenue' => $client->total_revenue ?? 0,
            ];
        })->toArray();
    }

    /**
     * Get invoice status data for pie chart
     */
    private function getInvoiceStatusData(int $userId): array
    {
        $invoiceStats = $this->invoiceRepository->getStatsForUser($userId);
        
        return [
            'paid' => $invoiceStats['paid_count'],
            'pending' => $invoiceStats['pending_count'],
            'draft' => $invoiceStats['draft_count'],
            'overdue' => $invoiceStats['overdue_count'],
        ];
    }

    /**
     * Get pending follow-ups (placeholder - implement based on your follow-up model)
     */
    private function getPendingFollowUps(int $userId): array
    {
        // This would need to be implemented based on your FollowUp model
        return [];
    }

    /**
     * Get payment reminders
     */
    private function getPaymentReminders(int $userId): array
    {
        $overdueInvoices = $this->invoiceRepository->getOverdueForUser($userId, 10);
        
        return $overdueInvoices->map(function ($invoice) {
            return [
                'invoice_number' => $invoice->invoice_number,
                'client_name' => $invoice->client->name,
                'amount' => $invoice->total_amount,
                'days_overdue' => now()->diffInDays($invoice->due_date),
            ];
        })->toArray();
    }

    /**
     * Get system notifications
     */
    private function getSystemNotifications(int $userId): array
    {
        $notifications = [];

        // Check plan usage
        $invoiceUsage = $this->invoiceRepository->getCurrentMonthCountForUser($userId);
        if ($invoiceUsage >= 3) { // Free plan limit
            $notifications[] = [
                'type' => 'warning',
                'message' => 'You have reached your monthly invoice limit. Upgrade to Pro for unlimited invoices.',
            ];
        }

        return $notifications;
    }

    /**
     * Get AJAX data for specific dashboard components
     */
    public function getAjaxData(int $userId, string $type): array
    {
        switch ($type) {
            case 'monthly_revenue':
                return $this->getMonthlyRevenueData($userId, 12);

            case 'invoice_status':
                return $this->getInvoiceStatusData($userId);

            case 'client_revenue':
                return $this->getTopClientsData($userId);

            case 'contract_stats':
                return $this->getContractAnalytics($userId);

            case 'monthly_expenses':
                return $this->getMonthlyExpenseData($userId, 12);

            case 'expense_categories':
                return $this->getExpenseCategoryData($userId);

            case 'expense_analytics':
                return $this->getExpenseAnalytics($userId);

            default:
                return [];
        }
    }

    /**
     * Get monthly expense data for charts
     */
    private function getMonthlyExpenseData(int $userId, int $months = 6): array
    {
        $monthlyData = [];
        $currentDate = now();

        for ($i = $months - 1; $i >= 0; $i--) {
            $date = $currentDate->copy()->subMonths($i);
            $monthKey = $date->format('M Y');
            $monthlyData[$monthKey] = 0;
        }

        $expenseData = $this->expenseRepository->getMonthlyTotals($userId, $currentDate->year);

        foreach ($expenseData as $month => $total) {
            $monthKey = $currentDate->copy()->month($month)->format('M Y');
            if (isset($monthlyData[$monthKey])) {
                $monthlyData[$monthKey] = $total;
            }
        }

        return $monthlyData;
    }

    /**
     * Get expense category breakdown data
     */
    private function getExpenseCategoryData(int $userId): array
    {
        return $this->expenseRepository->getTotalsByCategory($userId);
    }

    /**
     * Get expense analytics data
     */
    private function getExpenseAnalytics(int $userId): array
    {
        $currentMonth = now()->startOfMonth();
        $previousMonth = now()->subMonth()->startOfMonth();
        $currentYear = now()->startOfYear();

        // Current month expenses
        $currentMonthExpenses = $this->expenseRepository->getTotalForPeriod($userId, $currentMonth, now());
        $previousMonthExpenses = $this->expenseRepository->getTotalForPeriod($userId, $previousMonth, $currentMonth);

        // Calculate growth
        $expenseGrowth = $previousMonthExpenses > 0
            ? (($currentMonthExpenses - $previousMonthExpenses) / $previousMonthExpenses) * 100
            : 0;

        // Tax deductible vs non-deductible
        $taxDeductibleExpenses = $this->expenseRepository->getTaxDeductibleTotal($userId, $currentYear);
        $totalYearExpenses = $this->expenseRepository->getTotalForPeriod($userId, $currentYear, now());

        // Top expense categories
        $topCategories = $this->expenseRepository->getTopCategories($userId, 5);

        // Billable vs non-billable
        $billableExpenses = $this->expenseRepository->getBillableTotal($userId, $currentYear);

        return [
            'current_month_expenses' => $currentMonthExpenses,
            'previous_month_expenses' => $previousMonthExpenses,
            'expense_growth' => round($expenseGrowth, 1),
            'tax_deductible_expenses' => $taxDeductibleExpenses,
            'total_year_expenses' => $totalYearExpenses,
            'tax_deductible_percentage' => $totalYearExpenses > 0 ? round(($taxDeductibleExpenses / $totalYearExpenses) * 100, 1) : 0,
            'top_categories' => $topCategories,
            'billable_expenses' => $billableExpenses,
            'billable_percentage' => $totalYearExpenses > 0 ? round(($billableExpenses / $totalYearExpenses) * 100, 1) : 0,
        ];
    }
}
