@props([
    'type' => 'text',
    'label' => null,
    'error' => null,
    'hint' => null,
    'required' => false,
    'icon' => null,
    'iconPosition' => 'left',
    'size' => 'md'
])

@php
    $inputId = $attributes->get('id', 'input_' . uniqid());
    
    $sizes = [
        'sm' => 'px-3 py-2 text-sm',
        'md' => 'px-4 py-2.5 text-sm',
        'lg' => 'px-4 py-3 text-base'
    ];
    
    $baseClasses = 'form-input';
    $inputClasses = $baseClasses . ' ' . $sizes[$size];

    if ($error) {
        $inputClasses .= ' form-input-error';
    }
    
    if ($icon) {
        if ($iconPosition === 'left') {
            $inputClasses .= ' pl-10';
        } else {
            $inputClasses .= ' pr-10';
        }
    }
@endphp

<div class="space-y-1">
    @if($label)
        <label for="{{ $inputId }}" class="form-label">
            {{ $label }}
            @if($required)
                <span class="text-danger-500">*</span>
            @endif
        </label>
    @endif
    
    <div class="relative">
        @if($icon)
            <div class="absolute inset-y-0 {{ $iconPosition === 'left' ? 'left-0 pl-3' : 'right-0 pr-3' }} flex items-center pointer-events-none">
                <i class="{{ $icon }} text-secondary-400"></i>
            </div>
        @endif
        
        @if($type === 'textarea')
            <textarea 
                id="{{ $inputId }}"
                {{ $attributes->merge(['class' => $inputClasses]) }}
                @if($required) required @endif
            >{{ $slot }}</textarea>
        @elseif($type === 'select')
            <select 
                id="{{ $inputId }}"
                {{ $attributes->merge(['class' => $inputClasses]) }}
                @if($required) required @endif
            >
                {{ $slot }}
            </select>
        @else
            <input 
                type="{{ $type }}"
                id="{{ $inputId }}"
                {{ $attributes->merge(['class' => $inputClasses]) }}
                @if($required) required @endif
            />
        @endif
    </div>
    
    @if($hint && !$error)
        <p class="form-hint">{{ $hint }}</p>
    @endif

    @if($error)
        <p class="form-error">{{ $error }}</p>
    @endif
</div>
