<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ExpenseCategory;
use App\Services\ExpenseCategoryService;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class ExpenseCategoryController extends Controller
{
    protected ExpenseCategoryService $categoryService;

    public function __construct(ExpenseCategoryService $categoryService)
    {
        $this->categoryService = $categoryService;
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('viewAny', ExpenseCategory::class);
        $query = ExpenseCategory::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Filter by tax deductible
        if ($request->filled('tax_deductible')) {
            $taxDeductible = $request->get('tax_deductible');
            if ($taxDeductible === 'yes') {
                $query->where('is_tax_deductible', true);
            } elseif ($taxDeductible === 'no') {
                $query->where('is_tax_deductible', false);
            }
        }

        $categories = $query->withCount('expenses')
                           ->orderBy('sort_order')
                           ->orderBy('name')
                           ->paginate(15);

        return view('admin.expense-categories.index', compact('categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.expense-categories.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:expense_categories,name',
            'description' => 'nullable|string|max:1000',
            'icon' => 'required|string|max:50',
            'color' => 'required|string|max:20',
            'is_tax_deductible' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0|max:999',
        ]);

        $this->categoryService->createCategory($validated);

        return redirect()->route('admin.expense-categories.index')
                        ->with('success', 'Expense category created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(ExpenseCategory $expenseCategory)
    {
        $expenseCategory->loadCount('expenses');
        
        // Get recent expenses for this category
        $recentExpenses = $expenseCategory->expenses()
                                         ->with(['user', 'client'])
                                         ->latest()
                                         ->take(10)
                                         ->get();

        return view('admin.expense-categories.show', compact('expenseCategory', 'recentExpenses'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ExpenseCategory $expenseCategory)
    {
        return view('admin.expense-categories.edit', compact('expenseCategory'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ExpenseCategory $expenseCategory)
    {
        $validated = $request->validate([
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('expense_categories', 'name')->ignore($expenseCategory->id)
            ],
            'description' => 'nullable|string|max:1000',
            'icon' => 'required|string|max:50',
            'color' => 'required|string|max:20',
            'is_tax_deductible' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0|max:999',
        ]);

        $this->categoryService->updateCategory($expenseCategory, $validated);

        return redirect()->route('admin.expense-categories.show', $expenseCategory)
                        ->with('success', 'Expense category updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ExpenseCategory $expenseCategory)
    {
        try {
            $this->categoryService->deleteCategory($expenseCategory);
            return redirect()->route('admin.expense-categories.index')
                            ->with('success', 'Expense category deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->route('admin.expense-categories.index')
                            ->with('error', $e->getMessage());
        }
    }

    /**
     * Toggle the active status of a category
     */
    public function toggleStatus(ExpenseCategory $expenseCategory)
    {
        $this->categoryService->toggleStatus($expenseCategory);

        $status = !$expenseCategory->is_active ? 'activated' : 'deactivated';

        return redirect()->back()
                        ->with('success', "Expense category {$status} successfully.");
    }

    /**
     * Update sort order of categories
     */
    public function updateSortOrder(Request $request)
    {
        $validated = $request->validate([
            'categories' => 'required|array',
            'categories.*.id' => 'required|exists:expense_categories,id',
            'categories.*.sort_order' => 'required|integer|min:0',
        ]);

        $success = $this->categoryService->updateSortOrder($validated['categories']);

        return response()->json(['success' => $success]);
    }
}
