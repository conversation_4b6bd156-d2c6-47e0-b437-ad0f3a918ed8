<x-app-layout>
    <x-slot name="header">
        <div class="bg-gradient-to-r from-emerald-50 to-blue-50 -mx-6 -my-6 px-6 py-12 text-center">
            <div class="max-w-3xl mx-auto">
                <div class="mb-6">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-emerald-500 to-blue-500 rounded-2xl mb-4">
                        <i class="fas fa-rocket text-white text-2xl"></i>
                    </div>
                </div>
                <h1 class="text-4xl font-bold text-gray-900 mb-4">Choose Your Business Type</h1>
                <p class="text-xl text-gray-600 leading-relaxed">Select the option that best describes your business to get started with the right features and tools tailored for your needs.</p>
            </div>
        </div>
    </x-slot>

    <div class="py-16">
        <div class="max-w-6xl mx-auto px-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Freelancer Option -->
                <div class="bg-white overflow-hidden shadow-xl rounded-2xl border border-gray-100 hover:border-emerald-300 hover:shadow-2xl transition-all duration-500 cursor-pointer group transform hover:-translate-y-2"
                     onclick="selectBusinessType('freelancer')">
                    <div class="p-8 text-center">
                        <div class="w-20 h-20 bg-gradient-to-br from-emerald-100 to-emerald-200 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:from-emerald-200 group-hover:to-emerald-300 transition-all duration-300">
                            <i class="fas fa-user text-3xl text-emerald-600"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-3">Freelancer</h3>
                        <p class="text-gray-600 mb-6 leading-relaxed">Perfect for individual professionals working independently and managing their own clients</p>

                        <div class="text-left space-y-3 mb-8">
                            <div class="flex items-center text-gray-700">
                                <div class="w-5 h-5 bg-emerald-100 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-check text-emerald-600 text-xs"></i>
                                </div>
                                <span class="font-medium">Professional invoicing</span>
                            </div>
                            <div class="flex items-center text-gray-700">
                                <div class="w-5 h-5 bg-emerald-100 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-check text-emerald-600 text-xs"></i>
                                </div>
                                <span class="font-medium">Time tracking & billing</span>
                            </div>
                            <div class="flex items-center text-gray-700">
                                <div class="w-5 h-5 bg-emerald-100 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-check text-emerald-600 text-xs"></i>
                                </div>
                                <span class="font-medium">Expense management</span>
                            </div>
                            <div class="flex items-center text-gray-700">
                                <div class="w-5 h-5 bg-emerald-100 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-check text-emerald-600 text-xs"></i>
                                </div>
                                <span class="font-medium">Financial reporting</span>
                            </div>
                        </div>

                        <button type="button" class="w-full bg-gradient-to-r from-emerald-500 to-emerald-600 text-white py-3 px-6 rounded-xl hover:from-emerald-600 hover:to-emerald-700 transition-all duration-300 font-semibold shadow-lg hover:shadow-xl">
                            Choose Freelancer
                        </button>
                    </div>
                </div>

                <!-- Startup Option -->
                <div class="bg-white overflow-hidden shadow-lg rounded-lg border-2 border-transparent hover:border-blue-500 transition-all duration-300 cursor-pointer group" 
                     onclick="selectBusinessType('startup')">
                    <div class="p-8 text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-200 transition-colors">
                            <i class="fas fa-rocket text-2xl text-blue-600"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Startup</h3>
                        <p class="text-gray-600 mb-4">Growing teams with collaborative needs</p>
                        
                        <div class="text-left space-y-2 mb-6">
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-blue-500 mr-2"></i>
                                <span>Team collaboration</span>
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-blue-500 mr-2"></i>
                                <span>Client portal</span>
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-blue-500 mr-2"></i>
                                <span>Advanced reporting</span>
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-blue-500 mr-2"></i>
                                <span>Project management</span>
                            </div>
                        </div>
                        
                        <button type="button" class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                            Choose Startup
                        </button>
                    </div>
                </div>

                <!-- Small Business Option -->
                <div class="bg-white overflow-hidden shadow-lg rounded-lg border-2 border-transparent hover:border-purple-500 transition-all duration-300 cursor-pointer group" 
                     onclick="selectBusinessType('small_business')">
                    <div class="p-8 text-center">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-purple-200 transition-colors">
                            <i class="fas fa-building text-2xl text-purple-600"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Small Business</h3>
                        <p class="text-gray-600 mb-4">Established businesses with advanced requirements</p>
                        
                        <div class="text-left space-y-2 mb-6">
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-purple-500 mr-2"></i>
                                <span>Multi-user access</span>
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-purple-500 mr-2"></i>
                                <span>API access</span>
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-purple-500 mr-2"></i>
                                <span>Custom branding</span>
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-purple-500 mr-2"></i>
                                <span>Priority support</span>
                            </div>
                        </div>
                        
                        <button type="button" class="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors">
                            Choose Small Business
                        </button>
                    </div>
                </div>
            </div>

            <div class="text-center mt-8">
                <p class="text-gray-500 text-sm">Don't worry, you can always change this later in your settings.</p>
            </div>
        </div>
    </div>

    <script>
        function selectBusinessType(type) {
            window.location.href = `{{ route('business.register') }}?type=${type}`;
        }
    </script>
</x-app-layout>
