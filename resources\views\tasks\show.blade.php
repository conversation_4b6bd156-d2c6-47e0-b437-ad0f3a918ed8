<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Task Details') }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('tasks.edit', $task) }}" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    <i class="fas fa-edit mr-2"></i>Edit
                </a>
                <a href="{{ route('tasks.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Tasks
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-6xl mx-auto sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Main Content -->
                <div class="lg:col-span-2">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <!-- Task Header -->
                            <div class="flex items-start justify-between mb-6">
                                <div>
                                    <h1 class="text-2xl font-bold text-gray-900 mb-2">{{ $task->title }}</h1>
                                    <div class="flex items-center space-x-4 text-sm text-gray-600">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            @if($task->priority === 'urgent') bg-red-100 text-red-800
                                            @elseif($task->priority === 'high') bg-orange-100 text-orange-800
                                            @elseif($task->priority === 'medium') bg-yellow-100 text-yellow-800
                                            @else bg-green-100 text-green-800 @endif">
                                            {{ ucfirst($task->priority) }} Priority
                                        </span>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            @if($task->status === 'completed') bg-green-100 text-green-800
                                            @elseif($task->status === 'in_progress') bg-blue-100 text-blue-800
                                            @elseif($task->status === 'cancelled') bg-red-100 text-red-800
                                            @else bg-gray-100 text-gray-800 @endif">
                                            {{ ucfirst(str_replace('_', ' ', $task->status)) }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Description -->
                            @if($task->description)
                                <div class="mb-6">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Description</h3>
                                    <div class="prose max-w-none">
                                        <p class="text-gray-700 leading-relaxed">{{ $task->description }}</p>
                                    </div>
                                </div>
                            @endif

                            <!-- Tags -->
                            @if($task->tags && count($task->tags) > 0)
                                <div class="mb-6">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Tags</h3>
                                    <div class="flex flex-wrap gap-2">
                                        @foreach($task->tags as $tag)
                                            <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">{{ $tag }}</span>
                                        @endforeach
                                    </div>
                                </div>
                            @endif

                            <!-- Time Tracking -->
                            @if($task->timeEntries && $task->timeEntries->count() > 0)
                                <div class="mb-6">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Time Entries</h3>
                                    <div class="space-y-3">
                                        @foreach($task->timeEntries as $entry)
                                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                                <div>
                                                    <p class="font-medium text-gray-900">{{ $entry->description ?? 'No description' }}</p>
                                                    <p class="text-sm text-gray-600">{{ $entry->start_time->format('M d, Y g:i A') }}</p>
                                                </div>
                                                <div class="text-right">
                                                    <p class="font-medium text-gray-900">{{ $entry->duration_formatted }}</p>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                    <div class="mt-3 p-3 bg-blue-50 rounded-lg">
                                        <p class="text-sm text-blue-800">
                                            <strong>Total Time:</strong> {{ $task->total_time_formatted }}
                                        </p>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Task Details -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Task Details</h3>
                            <dl class="space-y-3">
                                @if($task->project)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Project</dt>
                                        <dd class="text-sm text-gray-900">
                                            <a href="{{ route('projects.show', $task->project) }}" class="text-blue-600 hover:text-blue-800">
                                                {{ $task->project->name }}
                                            </a>
                                        </dd>
                                    </div>
                                @endif

                                @if($task->assignedUser)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Assigned To</dt>
                                        <dd class="text-sm text-gray-900">{{ $task->assignedUser->name }}</dd>
                                    </div>
                                @endif

                                @if($task->due_date)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Due Date</dt>
                                        <dd class="text-sm text-gray-900">{{ $task->due_date->format('M d, Y') }}</dd>
                                    </div>
                                @endif

                                @if($task->estimated_hours)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Estimated Hours</dt>
                                        <dd class="text-sm text-gray-900">{{ $task->estimated_hours }} hours</dd>
                                    </div>
                                @endif

                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Created</dt>
                                    <dd class="text-sm text-gray-900">{{ $task->created_at->format('M d, Y g:i A') }}</dd>
                                </div>

                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                                    <dd class="text-sm text-gray-900">{{ $task->updated_at->format('M d, Y g:i A') }}</dd>
                                </div>
                            </dl>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                            <div class="space-y-3">
                                @if($task->status !== 'completed')
                                    <form method="POST" action="{{ route('tasks.update', $task) }}" class="w-full">
                                        @csrf
                                        @method('PATCH')
                                        <input type="hidden" name="status" value="completed">
                                        <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                            <i class="fas fa-check mr-2"></i>Mark Complete
                                        </button>
                                    </form>
                                @endif

                                @if($task->status === 'pending')
                                    <form method="POST" action="{{ route('tasks.update', $task) }}" class="w-full">
                                        @csrf
                                        @method('PATCH')
                                        <input type="hidden" name="status" value="in_progress">
                                        <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                            <i class="fas fa-play mr-2"></i>Start Task
                                        </button>
                                    </form>
                                @endif

                                <a href="{{ route('time-tracking.create', ['task_id' => $task->id]) }}" class="block w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded text-center">
                                    <i class="fas fa-clock mr-2"></i>Track Time
                                </a>

                                <form method="POST" action="{{ route('tasks.destroy', $task) }}" class="w-full" 
                                      onsubmit="return confirm('Are you sure you want to delete this task?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                        <i class="fas fa-trash mr-2"></i>Delete Task
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
