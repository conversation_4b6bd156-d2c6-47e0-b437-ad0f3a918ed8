<?php

namespace App\Mail;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class OnboardingSequenceMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public User $user;
    public int $sequenceStep;
    public array $emailData;

    /**
     * Create a new message instance.
     */
    public function __construct(User $user, int $sequenceStep, array $emailData = [])
    {
        $this->user = $user;
        $this->sequenceStep = $sequenceStep;
        $this->emailData = $emailData;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $subjects = [
            1 => "Welcome to " . config('app.name') . "! Let's get you started 🚀",
            2 => "Your first week with " . config('app.name') . " - Tips & tricks 💡",
            3 => "Unlock the full potential of " . config('app.name') . " 🔓",
            4 => "Success stories from " . config('app.name') . " users 📈",
            5 => "Advanced features to grow your business 🌟",
        ];

        return new Envelope(
            subject: $subjects[$this->sequenceStep] ?? "Tips from " . config('app.name'),
            to: [$this->user->email],
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.onboarding-sequence',
            with: [
                'user' => $this->user,
                'sequenceStep' => $this->sequenceStep,
                'emailData' => $this->emailData,
                'header_title' => $this->getHeaderTitle(),
                'header_subtitle' => $this->getHeaderSubtitle(),
            ],
        );
    }

    private function getHeaderTitle(): string
    {
        return match($this->sequenceStep) {
            1 => 'Welcome to Your Journey!',
            2 => 'Week 1 Complete!',
            3 => 'Unlock Your Potential',
            4 => 'Success Stories',
            5 => 'Advanced Features',
            default => 'Your Business Journey'
        };
    }

    private function getHeaderSubtitle(): string
    {
        return match($this->sequenceStep) {
            1 => 'Let\'s help you get the most out of ' . config('app.name'),
            2 => 'Here are some tips to supercharge your productivity',
            3 => 'Discover features that will transform your business',
            4 => 'See how others are succeeding with ' . config('app.name'),
            5 => 'Take your business to the next level',
            default => 'Continuing your success journey'
        };
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
