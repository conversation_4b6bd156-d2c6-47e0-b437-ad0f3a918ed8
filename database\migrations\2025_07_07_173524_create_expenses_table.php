<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expenses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('category_id')->constrained('expense_categories')->onDelete('restrict');
            $table->foreignId('client_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('project_id')->nullable()->constrained()->onDelete('set null');
            $table->string('expense_number')->unique();
            $table->decimal('amount', 12, 2);
            $table->string('description');
            $table->text('notes')->nullable();
            $table->date('expense_date');
            $table->decimal('tax_amount', 12, 2)->default(0);
            $table->decimal('tax_percentage', 5, 2)->default(0);
            $table->boolean('is_billable')->default(false);
            $table->boolean('is_billed')->default(false); // If billable expense has been billed to client
            $table->string('receipt_path')->nullable();
            $table->enum('status', ['draft', 'submitted', 'approved', 'rejected'])->default('draft');
            $table->string('payment_method')->nullable(); // cash, card, bank_transfer, etc.
            $table->string('vendor_name')->nullable();
            $table->string('reference_number')->nullable(); // Invoice/receipt reference
            $table->timestamp('approved_at')->nullable();
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');

            // Performance indexes
            $table->index('expense_date');
            $table->index('status');
            $table->index(['user_id', 'status']);
            $table->index(['user_id', 'expense_date']);
            $table->index(['client_id', 'is_billable']);
            $table->index(['project_id', 'is_billable']);
            $table->index('is_billable');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expenses');
    }
};
