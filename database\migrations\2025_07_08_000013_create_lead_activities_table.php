<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lead_activities', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lead_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            
            // Activity details
            $table->enum('type', [
                'call', 'email', 'meeting', 'proposal_sent', 'follow_up', 
                'note', 'stage_change', 'score_change', 'document_sent',
                'website_visit', 'social_interaction', 'referral'
            ]);
            $table->string('title');
            $table->text('description')->nullable();
            
            // Scheduling and completion
            $table->timestamp('scheduled_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->enum('status', ['scheduled', 'completed', 'cancelled', 'missed'])->default('completed');
            
            // Outcome tracking
            $table->enum('outcome', ['positive', 'neutral', 'negative'])->nullable();
            $table->text('outcome_notes')->nullable();
            $table->integer('duration_minutes')->nullable(); // For calls/meetings
            
            // Automation and tracking
            $table->boolean('is_automated')->default(false);
            $table->string('automation_trigger')->nullable();
            $table->json('metadata')->nullable(); // For storing additional data
            
            $table->timestamps();

            // Indexes for performance
            $table->index(['lead_id', 'created_at']);
            $table->index(['user_id', 'type']);
            $table->index(['scheduled_at', 'status']);
            $table->index(['type', 'outcome']);
            $table->index(['completed_at', 'outcome']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lead_activities');
    }
};
