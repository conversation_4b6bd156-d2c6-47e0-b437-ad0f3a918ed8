<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Exception;

class GroqService
{
    private ?string $apiKey;
    private ?string $model;
    private ?int $maxTokens;
    private ?float $temperature;
    private ?int $timeout;
    private string $baseUrl;

    public function __construct()
    {
        $this->apiKey = config('services.groq.api_key');
        $this->model = config('services.groq.model');
        $this->maxTokens = config('services.groq.max_tokens');
        $this->temperature = config('services.groq.temperature');
        $this->timeout = config('services.groq.timeout');
        $this->baseUrl = config('services.groq.base_url');
    }

    /**
     * Generate text completion using Groq API
     */
    public function generateCompletion(string $prompt, array $options = []): array
    {
        if (empty($this->apiKey)) {
            return ['success' => false, 'error' => 'Groq API key is not configured'];
        }

        try {
            $response = Http::timeout($this->timeout)
                ->withHeaders([
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'Content-Type' => 'application/json',
                ])
                ->post($this->baseUrl . '/chat/completions', [
                    'model' => $options['model'] ?? $this->model,
                    'messages' => [
                        [
                            'role' => 'user',
                            'content' => $prompt
                        ]
                    ],
                    'max_tokens' => $options['max_tokens'] ?? $this->maxTokens,
                    'temperature' => $options['temperature'] ?? $this->temperature,
                    'stream' => false,
                ]);

            if ($response->successful()) {
                $data = $response->json();
                
                if (isset($data['choices'][0]['message']['content'])) {
                    return [
                        'success' => true,
                        'content' => trim($data['choices'][0]['message']['content']),
                        'usage' => $data['usage'] ?? null,
                    ];
                }
                
                return ['success' => false, 'error' => 'Invalid response format'];
            }

            return [
                'success' => false,
                'error' => 'API request failed: ' . $response->body(),
                'status' => $response->status()
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Request failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Generate invoice description using Groq
     */
    public function generateInvoiceDescription(array $context): array
    {
        $prompt = $this->buildInvoiceDescriptionPrompt($context);
        
        return $this->generateCompletion($prompt, [
            'max_tokens' => 200,
            'temperature' => 0.7,
        ]);
    }

    /**
     * Generate follow-up message using Groq
     */
    public function generateFollowUpMessage(array $context): array
    {
        $prompt = $this->buildFollowUpPrompt($context);
        
        return $this->generateCompletion($prompt, [
            'max_tokens' => 300,
            'temperature' => 0.8,
        ]);
    }

    /**
     * Generate contract recommendations using Groq
     */
    public function generateContractRecommendations(array $context): array
    {
        $prompt = $this->buildContractPrompt($context);
        
        return $this->generateCompletion($prompt, [
            'max_tokens' => 500,
            'temperature' => 0.6,
        ]);
    }

    /**
     * Generate business insights using Groq
     */
    public function generateBusinessInsights(array $data): array
    {
        $prompt = $this->buildBusinessInsightsPrompt($data);

        return $this->generateCompletion($prompt, [
            'max_tokens' => 600,
            'temperature' => 0.7,
        ]);
    }

    /**
     * Generate proposal content based on project details
     */
    public function generateProposalContent(array $context): array
    {
        $prompt = $this->buildProposalContentPrompt($context);

        return $this->generateCompletion($prompt, [
            'max_tokens' => 1000,
            'temperature' => 0.7,
        ]);
    }

    /**
     * Generate proposal sections (introduction, scope, timeline, etc.)
     */
    public function generateProposalSections(array $context): array
    {
        $prompt = $this->buildProposalSectionsPrompt($context);

        return $this->generateCompletion($prompt, [
            'max_tokens' => 1200,
            'temperature' => 0.6,
        ]);
    }

    /**
     * Improve existing proposal content
     */
    public function improveProposalContent(string $content, array $context = []): array
    {
        $prompt = $this->buildProposalImprovementPrompt($content, $context);

        return $this->generateCompletion($prompt, [
            'max_tokens' => 800,
            'temperature' => 0.5,
        ]);
    }

    /**
     * Build invoice description prompt
     */
    private function buildInvoiceDescriptionPrompt(array $context): string
    {
        return "Generate a professional invoice description for the following context:\n" .
               "Client: {$context['client_name']}\n" .
               "Service Type: {$context['service_type']}\n" .
               "Project Details: {$context['project_details']}\n" .
               "Duration: {$context['duration']}\n\n" .
               "Create a clear, professional description that explains the work completed. " .
               "Keep it concise but informative (2-3 sentences).";
    }

    /**
     * Build follow-up message prompt
     */
    private function buildFollowUpPrompt(array $context): string
    {
        return "Generate a professional follow-up message for an overdue invoice:\n" .
               "Client Name: {$context['client_name']}\n" .
               "Invoice Number: {$context['invoice_number']}\n" .
               "Amount: ₹{$context['amount']}\n" .
               "Days Overdue: {$context['days_overdue']}\n" .
               "Relationship: {$context['relationship']}\n" .
               "Type: {$context['type']}\n\n" .
               "Create a polite but firm message appropriate for the relationship and overdue period. " .
               "Keep it professional and include a clear call to action.";
    }

    /**
     * Build contract recommendations prompt
     */
    private function buildContractPrompt(array $context): string
    {
        return "Generate contract recommendations for a freelance project:\n" .
               "Project Type: {$context['project_type']}\n" .
               "Duration: {$context['duration']}\n" .
               "Budget Range: {$context['budget_range']}\n" .
               "Client Type: {$context['client_type']}\n\n" .
               "Provide key contract clauses and terms that should be included. " .
               "Focus on payment terms, deliverables, and protection clauses. " .
               "Format as bullet points for easy reading.";
    }

    /**
     * Build business insights prompt
     */
    private function buildBusinessInsightsPrompt(array $data): string
    {
        return "Analyze the following business data and provide actionable insights:\n" .
               "Total Revenue: ₹{$data['total_revenue']}\n" .
               "Client Count: {$data['client_count']}\n" .
               "Average Invoice Value: ₹{$data['avg_invoice_value']}\n" .
               "Payment Delays: {$data['avg_payment_delay']} days\n" .
               "Growth Rate: {$data['growth_rate']}%\n\n" .
               "Provide 3-4 specific, actionable business insights and recommendations. " .
               "Focus on revenue optimization, client management, and growth opportunities. " .
               "Keep insights practical and implementable.";
    }

    /**
     * Build proposal content generation prompt
     */
    private function buildProposalContentPrompt(array $context): string
    {
        $prompt = "Generate a professional project proposal with the following details:\n\n";

        if (!empty($context['client_name'])) {
            $prompt .= "Client: {$context['client_name']}\n";
        }

        if (!empty($context['project_type'])) {
            $prompt .= "Project Type: {$context['project_type']}\n";
        }

        if (!empty($context['project_description'])) {
            $prompt .= "Project Description: {$context['project_description']}\n";
        }

        if (!empty($context['budget'])) {
            $prompt .= "Budget: {$context['budget']}\n";
        }

        if (!empty($context['timeline'])) {
            $prompt .= "Timeline: {$context['timeline']}\n";
        }

        $prompt .= "\nGenerate a comprehensive proposal that includes:\n";
        $prompt .= "- Professional introduction\n";
        $prompt .= "- Project overview and understanding\n";
        $prompt .= "- Detailed scope of work\n";
        $prompt .= "- Timeline and milestones\n";
        $prompt .= "- Investment/pricing information\n";
        $prompt .= "- Next steps\n\n";
        $prompt .= "Make it professional, persuasive, and tailored to the client's needs.";

        return $prompt;
    }

    /**
     * Build proposal sections generation prompt
     */
    private function buildProposalSectionsPrompt(array $context): string
    {
        $prompt = "Generate structured proposal sections for a {$context['project_type']} project.\n\n";

        if (!empty($context['client_name'])) {
            $prompt .= "Client: {$context['client_name']}\n";
        }

        $prompt .= "\nGenerate the following sections:\n";
        $prompt .= "1. INTRODUCTION - Brief, personalized introduction\n";
        $prompt .= "2. PROJECT_OVERVIEW - Understanding of client's needs\n";
        $prompt .= "3. SCOPE_OF_WORK - Detailed deliverables and tasks\n";
        $prompt .= "4. TIMELINE - Project phases and milestones\n";
        $prompt .= "5. INVESTMENT - Pricing structure and payment terms\n";
        $prompt .= "6. NEXT_STEPS - Clear call to action\n\n";
        $prompt .= "Format each section clearly with headers.";

        return $prompt;
    }

    /**
     * Build proposal improvement prompt
     */
    private function buildProposalImprovementPrompt(string $content, array $context): string
    {
        $prompt = "Improve the following proposal content to make it more professional, persuasive, and client-focused:\n\n";
        $prompt .= "ORIGINAL CONTENT:\n{$content}\n\n";

        $prompt .= "IMPROVEMENT GUIDELINES:\n";
        $prompt .= "- Make it more client-centric and benefit-focused\n";
        $prompt .= "- Improve clarity and readability\n";
        $prompt .= "- Add persuasive elements without being pushy\n";
        $prompt .= "- Ensure professional tone throughout\n";
        $prompt .= "- Include specific value propositions\n\n";
        $prompt .= "Provide the improved version:";

        return $prompt;
    }

    /**
     * Test API connection
     */
    public function testConnection(): array
    {
        return $this->generateCompletion("Say 'Hello from Groq!' to test the connection.", [
            'max_tokens' => 50,
            'temperature' => 0.1,
        ]);
    }
}
