@extends('emails.layouts.app')

@section('content')
    <!-- Onboarding Header -->
    <div style="text-align: center; margin-bottom: 32px;">
        @if($sequenceStep === 1)
            <div style="font-size: 64px; margin-bottom: 16px;">🎉</div>
        @elseif($sequenceStep === 2)
            <div style="font-size: 64px; margin-bottom: 16px;">💡</div>
        @elseif($sequenceStep === 3)
            <div style="font-size: 64px; margin-bottom: 16px;">🔓</div>
        @elseif($sequenceStep === 4)
            <div style="font-size: 64px; margin-bottom: 16px;">📈</div>
        @else
            <div style="font-size: 64px; margin-bottom: 16px;">🌟</div>
        @endif
        
        <h2 style="color: #10b981; margin-bottom: 8px;">{{ $header_title }}</h2>
        <p class="lead" style="color: #059669;">
            Hi {{ $user->name }}, {{ $header_subtitle }}
        </p>
    </div>

    @if($sequenceStep === 1)
        <!-- Step 1: Welcome & Getting Started -->
        <p style="font-size: 18px; color: #374151; margin-bottom: 24px;">
            Welcome to {{ config('app.name') }}! We're thrilled to have you on board. Let's make sure you get the most out of your new business management platform.
        </p>
        
        <!-- Quick Start Checklist -->
        <div class="success-box">
            <h3 style="color: #166534; margin: 0 0 16px 0;">🚀 Your Quick Start Checklist</h3>
            <div style="margin: 20px 0;">
                <div style="display: flex; align-items: center; margin-bottom: 12px; padding: 12px; background-color: rgba(255, 255, 255, 0.7); border-radius: 8px;">
                    <div style="color: #166534; font-size: 20px; margin-right: 12px;">✅</div>
                    <span style="color: #15803d; font-weight: 600;">Account created - You're here!</span>
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 12px; padding: 12px; background-color: rgba(255, 255, 255, 0.7); border-radius: 8px;">
                    <div style="color: #166534; font-size: 20px; margin-right: 12px;">📝</div>
                    <span style="color: #15803d;">Complete your business profile</span>
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 12px; padding: 12px; background-color: rgba(255, 255, 255, 0.7); border-radius: 8px;">
                    <div style="color: #166534; font-size: 20px; margin-right: 12px;">👥</div>
                    <span style="color: #15803d;">Add your first client</span>
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 12px; padding: 12px; background-color: rgba(255, 255, 255, 0.7); border-radius: 8px;">
                    <div style="color: #166534; font-size: 20px; margin-right: 12px;">📄</div>
                    <span style="color: #15803d;">Create your first invoice</span>
                </div>
            </div>
        </div>

    @elseif($sequenceStep === 2)
        <!-- Step 2: Week 1 Tips -->
        <p style="font-size: 18px; color: #374151; margin-bottom: 24px;">
            Congratulations on completing your first week! Here are some pro tips to help you work more efficiently and get better results.
        </p>
        
        <!-- Pro Tips -->
        <h3>💡 Pro Tips for Maximum Productivity</h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 16px; margin: 24px 0;">
            <div style="background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); padding: 20px; border-radius: 12px; border: 1px solid #bbf7d0;">
                <div style="color: #166534; font-size: 24px; margin-bottom: 8px;">⚡</div>
                <h4 style="margin: 0 0 8px 0; color: #166534; font-size: 16px;">Keyboard Shortcuts</h4>
                <p style="margin: 0; color: #15803d; font-size: 14px;">Press 'Ctrl+N' to quickly create a new invoice</p>
            </div>
            
            <div style="background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%); padding: 20px; border-radius: 12px; border: 1px solid #bae6fd;">
                <div style="color: #1e40af; font-size: 24px; margin-bottom: 8px;">📊</div>
                <h4 style="margin: 0 0 8px 0; color: #1e40af; font-size: 16px;">Dashboard Widgets</h4>
                <p style="margin: 0; color: #2563eb; font-size: 14px;">Customize your dashboard to show what matters most</p>
            </div>
            
            <div style="background: linear-gradient(135deg, #fdf4ff 0%, #fae8ff 100%); padding: 20px; border-radius: 12px; border: 1px solid #f5d0fe;">
                <div style="color: #a21caf; font-size: 24px; margin-bottom: 8px;">🔄</div>
                <h4 style="margin: 0 0 8px 0; color: #a21caf; font-size: 16px;">Recurring Invoices</h4>
                <p style="margin: 0; color: #c026d3; font-size: 14px;">Set up recurring invoices for regular clients</p>
            </div>
            
            <div style="background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%); padding: 20px; border-radius: 12px; border: 1px solid #fde68a;">
                <div style="color: #92400e; font-size: 24px; margin-bottom: 8px;">📱</div>
                <h4 style="margin: 0 0 8px 0; color: #92400e; font-size: 16px;">Mobile App</h4>
                <p style="margin: 0; color: #b45309; font-size: 14px;">Manage your business on the go with our mobile app</p>
            </div>
        </div>

    @elseif($sequenceStep === 3)
        <!-- Step 3: Unlock Potential -->
        <p style="font-size: 18px; color: #374151; margin-bottom: 24px;">
            You're doing great! Now let's unlock some powerful features that will take your business management to the next level.
        </p>
        
        <!-- Advanced Features -->
        <h3>🔓 Unlock These Powerful Features</h3>
        <div style="margin: 24px 0;">
            <div style="display: flex; align-items: flex-start; margin-bottom: 20px; padding: 20px; background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); border-radius: 12px; border: 1px solid #bbf7d0;">
                <div style="color: #166534; font-size: 32px; margin-right: 16px;">📊</div>
                <div>
                    <h4 style="margin: 0 0 8px 0; color: #166534;">Advanced Reporting</h4>
                    <p style="margin: 0; color: #15803d; font-size: 14px;">Get detailed insights into your business performance with custom reports and analytics.</p>
                </div>
            </div>
            
            <div style="display: flex; align-items: flex-start; margin-bottom: 20px; padding: 20px; background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%); border-radius: 12px; border: 1px solid #bae6fd;">
                <div style="color: #1e40af; font-size: 32px; margin-right: 16px;">⏰</div>
                <div>
                    <h4 style="margin: 0 0 8px 0; color: #1e40af;">Time Tracking</h4>
                    <p style="margin: 0; color: #2563eb; font-size: 14px;">Track time spent on projects and automatically convert it to billable hours.</p>
                </div>
            </div>
            
            <div style="display: flex; align-items: flex-start; margin-bottom: 20px; padding: 20px; background: linear-gradient(135deg, #fdf4ff 0%, #fae8ff 100%); border-radius: 12px; border: 1px solid #f5d0fe;">
                <div style="color: #a21caf; font-size: 32px; margin-right: 16px;">🎨</div>
                <div>
                    <h4 style="margin: 0 0 8px 0; color: #a21caf;">Custom Branding</h4>
                    <p style="margin: 0; color: #c026d3; font-size: 14px;">Add your logo and brand colors to invoices and make them uniquely yours.</p>
                </div>
            </div>
        </div>

    @elseif($sequenceStep === 4)
        <!-- Step 4: Success Stories -->
        <p style="font-size: 18px; color: #374151; margin-bottom: 24px;">
            See how other business owners like you are achieving amazing results with {{ config('app.name') }}.
        </p>
        
        <!-- Success Stories -->
        <h3>📈 Real Success Stories</h3>
        <div style="margin: 24px 0;">
            <div style="background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); border: 1px solid #e2e8f0; border-radius: 12px; padding: 24px; margin-bottom: 20px;">
                <div style="display: flex; align-items: center; margin-bottom: 16px;">
                    <div style="width: 48px; height: 48px; background: linear-gradient(135deg, #10b981 0%, #059669 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 16px;">
                        <span style="color: white; font-weight: bold; font-size: 18px;">S</span>
                    </div>
                    <div>
                        <h4 style="margin: 0; color: #1e293b;">Sarah, Freelance Designer</h4>
                        <p style="margin: 0; color: #64748b; font-size: 14px;">Increased revenue by 40% in 3 months</p>
                    </div>
                </div>
                <p style="color: #64748b; font-style: italic; margin: 0;">
                    "{{ config('app.name') }} helped me streamline my invoicing process. I now spend 80% less time on admin work and can focus on what I love - designing!"
                </p>
            </div>
            
            <div style="background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); border: 1px solid #e2e8f0; border-radius: 12px; padding: 24px; margin-bottom: 20px;">
                <div style="display: flex; align-items: center; margin-bottom: 16px;">
                    <div style="width: 48px; height: 48px; background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 16px;">
                        <span style="color: white; font-weight: bold; font-size: 18px;">M</span>
                    </div>
                    <div>
                        <h4 style="margin: 0; color: #1e293b;">Mike, Marketing Consultant</h4>
                        <p style="margin: 0; color: #64748b; font-size: 14px;">Reduced payment delays by 60%</p>
                    </div>
                </div>
                <p style="color: #64748b; font-style: italic; margin: 0;">
                    "The automated payment reminders are a game-changer. My clients pay faster, and I don't have to chase payments anymore."
                </p>
            </div>
        </div>

    @else
        <!-- Step 5: Advanced Features -->
        <p style="font-size: 18px; color: #374151; margin-bottom: 24px;">
            You're now a {{ config('app.name') }} pro! Let's explore some advanced features that will help you scale your business even further.
        </p>
        
        <!-- Advanced Features -->
        <h3>🌟 Advanced Features for Business Growth</h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px; margin: 24px 0;">
            <div style="background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); padding: 24px; border-radius: 12px; border: 1px solid #bbf7d0;">
                <div style="color: #166534; font-size: 32px; margin-bottom: 12px;">🤖</div>
                <h4 style="margin: 0 0 12px 0; color: #166534;">Automation Rules</h4>
                <p style="margin: 0; color: #15803d; font-size: 14px;">Set up automated workflows to handle repetitive tasks and save hours every week.</p>
            </div>
            
            <div style="background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%); padding: 24px; border-radius: 12px; border: 1px solid #bae6fd;">
                <div style="color: #1e40af; font-size: 32px; margin-bottom: 12px;">🔗</div>
                <h4 style="margin: 0 0 12px 0; color: #1e40af;">API Integration</h4>
                <p style="margin: 0; color: #2563eb; font-size: 14px;">Connect with your favorite tools and create a seamless workflow across all platforms.</p>
            </div>
            
            <div style="background: linear-gradient(135deg, #fdf4ff 0%, #fae8ff 100%); padding: 24px; border-radius: 12px; border: 1px solid #f5d0fe;">
                <div style="color: #a21caf; font-size: 32px; margin-bottom: 12px;">👥</div>
                <h4 style="margin: 0 0 12px 0; color: #a21caf;">Team Collaboration</h4>
                <p style="margin: 0; color: #c026d3; font-size: 14px;">Add team members and collaborate on projects with role-based permissions.</p>
            </div>
        </div>
    @endif
    
    <!-- Call to Action -->
    <div style="text-align: center; margin: 40px 0;">
        @if($sequenceStep === 1)
            <a href="{{ route('profile.edit') }}" class="btn btn-primary" style="font-size: 18px; padding: 18px 36px;">
                🚀 Complete Your Profile
            </a>
        @elseif($sequenceStep === 2)
            <a href="{{ route('dashboard') }}" class="btn btn-primary" style="font-size: 18px; padding: 18px 36px;">
                💡 Try These Tips Now
            </a>
        @elseif($sequenceStep === 3)
            <a href="{{ route('dashboard') }}" class="btn btn-primary" style="font-size: 18px; padding: 18px 36px;">
                🔓 Explore Advanced Features
            </a>
        @elseif($sequenceStep === 4)
            <a href="{{ route('dashboard') }}" class="btn btn-primary" style="font-size: 18px; padding: 18px 36px;">
                📈 Start Your Success Story
            </a>
        @else
            <a href="{{ route('subscriptions.plans') }}" class="btn btn-primary" style="font-size: 18px; padding: 18px 36px;">
                🌟 Upgrade for More Features
            </a>
        @endif
    </div>
    
    <!-- Progress Indicator -->
    <div style="background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px; margin: 32px 0; text-align: center;">
        <h3 style="color: #1e293b; margin: 0 0 16px 0;">Your Onboarding Progress</h3>
        <div style="display: flex; justify-content: center; align-items: center; margin: 16px 0;">
            @for($i = 1; $i <= 5; $i++)
                <div style="width: 32px; height: 32px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 8px; {{ $i <= $sequenceStep ? 'background-color: #10b981; color: white;' : 'background-color: #e5e7eb; color: #9ca3af;' }}">
                    {{ $i }}
                </div>
                @if($i < 5)
                    <div style="width: 40px; height: 2px; {{ $i < $sequenceStep ? 'background-color: #10b981;' : 'background-color: #e5e7eb;' }}"></div>
                @endif
            @endfor
        </div>
        <p style="color: #64748b; margin: 0; font-size: 14px;">
            Step {{ $sequenceStep }} of 5 - You're {{ round(($sequenceStep / 5) * 100) }}% complete!
        </p>
    </div>
    
    <!-- Help Section -->
    <div style="text-align: center; background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; padding: 32px; margin: 32px 0;">
        <div style="font-size: 48px; margin-bottom: 16px;">🎧</div>
        <h3 style="color: #1e293b; margin: 0 0 12px 0;">Need Help Along the Way?</h3>
        <p style="color: #64748b; margin: 0 0 24px 0;">
            Our support team is here to help you succeed. Don't hesitate to reach out if you have any questions!
        </p>
        
        <div style="display: flex; flex-wrap: wrap; gap: 12px; justify-content: center;">
            <a href="mailto:support@{{ config('app.domain', 'freeligo.com') }}" class="btn btn-outline">
                📧 Email Support
            </a>
            <a href="#" class="btn btn-secondary">
                💬 Live Chat
            </a>
            <a href="#" class="btn btn-secondary">
                📚 Help Center
            </a>
        </div>
    </div>
    
    <!-- Footer Message -->
    <div style="text-align: center; margin: 40px 0;">
        <h3 style="color: #1e293b; margin-bottom: 16px;">We're Here to Support Your Success! 🌟</h3>
        <p style="color: #64748b; font-size: 16px;">
            Thank you for choosing {{ config('app.name') }}. We're committed to helping you build and grow your business.
        </p>
    </div>
    
    <!-- Footer Note -->
    <div style="border-top: 1px solid #e2e8f0; padding-top: 20px; margin-top: 32px;">
        <p style="color: #64748b; font-size: 14px; text-align: center; margin: 0;">
            This is email {{ $sequenceStep }} of 5 in your {{ config('app.name') }} onboarding series.
            <br>
            <a href="#" style="color: #64748b; text-decoration: none;">Manage email preferences</a> | 
            <a href="#" style="color: #64748b; text-decoration: none;">Unsubscribe from onboarding emails</a>
        </p>
    </div>
@endsection
