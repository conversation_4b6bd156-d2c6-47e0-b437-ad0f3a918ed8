<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WorkflowVariable extends Model
{
    use HasFactory;

    protected $fillable = [
        'workflow_id',
        'name',
        'type',
        'description',
        'default_value',
        'current_value',
        'is_required',
        'validation_rules',
        'source',
        'calculation_formula',
    ];

    protected function casts(): array
    {
        return [
            'default_value' => 'array',
            'current_value' => 'array',
            'is_required' => 'boolean',
            'validation_rules' => 'array',
            'calculation_formula' => 'array',
        ];
    }

    /**
     * Get the workflow that owns the variable.
     */
    public function workflow(): BelongsTo
    {
        return $this->belongsTo(Workflow::class);
    }

    /**
     * Get the variable value with type casting.
     */
    public function getValue()
    {
        $value = $this->current_value ?? $this->default_value;
        
        if (is_null($value)) {
            return null;
        }

        return match ($this->type) {
            'string' => (string)$value,
            'number' => is_numeric($value) ? (float)$value : 0,
            'boolean' => (bool)$value,
            'date' => $value instanceof \Carbon\Carbon ? $value : \Carbon\Carbon::parse($value),
            'array' => is_array($value) ? $value : [$value],
            'object' => is_array($value) ? (object)$value : $value,
            default => $value,
        };
    }

    /**
     * Set the variable value with validation.
     */
    public function setValue($value): bool
    {
        if (!$this->validateValue($value)) {
            return false;
        }

        $this->update(['current_value' => $value]);
        return true;
    }

    /**
     * Validate the variable value.
     */
    public function validateValue($value): bool
    {
        // Check required
        if ($this->is_required && (is_null($value) || $value === '')) {
            return false;
        }

        // Type validation
        if (!$this->validateType($value)) {
            return false;
        }

        // Custom validation rules
        if ($this->validation_rules) {
            return $this->validateRules($value);
        }

        return true;
    }

    /**
     * Validate value type.
     */
    protected function validateType($value): bool
    {
        if (is_null($value)) {
            return !$this->is_required;
        }

        return match ($this->type) {
            'string' => is_string($value) || is_numeric($value),
            'number' => is_numeric($value),
            'boolean' => is_bool($value) || in_array($value, [0, 1, '0', '1', 'true', 'false']),
            'date' => $this->isValidDate($value),
            'array' => is_array($value),
            'object' => is_array($value) || is_object($value),
            'email' => filter_var($value, FILTER_VALIDATE_EMAIL) !== false,
            'url' => filter_var($value, FILTER_VALIDATE_URL) !== false,
            default => true,
        };
    }

    /**
     * Validate custom rules.
     */
    protected function validateRules($value): bool
    {
        foreach ($this->validation_rules as $rule => $ruleValue) {
            if (!$this->validateRule($value, $rule, $ruleValue)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Validate a single rule.
     */
    protected function validateRule($value, string $rule, $ruleValue): bool
    {
        return match ($rule) {
            'min' => $this->type === 'number' ? $value >= $ruleValue : strlen($value) >= $ruleValue,
            'max' => $this->type === 'number' ? $value <= $ruleValue : strlen($value) <= $ruleValue,
            'min_length' => strlen($value) >= $ruleValue,
            'max_length' => strlen($value) <= $ruleValue,
            'pattern' => @preg_match('/' . addslashes($ruleValue) . '/', $value),
            'in' => in_array($value, $ruleValue),
            'not_in' => !in_array($value, $ruleValue),
            'unique' => $this->validateUnique($value),
            default => true,
        };
    }

    /**
     * Check if date is valid.
     */
    protected function isValidDate($value): bool
    {
        try {
            \Carbon\Carbon::parse($value);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Validate uniqueness within workflow.
     */
    protected function validateUnique($value): bool
    {
        return !static::where('workflow_id', $this->workflow_id)
            ->where('name', '!=', $this->name)
            ->where('current_value', $value)
            ->exists();
    }

    /**
     * Calculate variable value based on formula.
     */
    public function calculateValue(array $context = []): mixed
    {
        if (!$this->calculation_formula || $this->source !== 'calculated') {
            return $this->getValue();
        }

        $formula = $this->calculation_formula;
        $operation = $formula['operation'] ?? 'value';

        return match ($operation) {
            'sum' => $this->calculateSum($formula, $context),
            'average' => $this->calculateAverage($formula, $context),
            'count' => $this->calculateCount($formula, $context),
            'concat' => $this->calculateConcat($formula, $context),
            'conditional' => $this->calculateConditional($formula, $context),
            'date_diff' => $this->calculateDateDiff($formula, $context),
            'percentage' => $this->calculatePercentage($formula, $context),
            default => $this->getValue(),
        };
    }

    /**
     * Calculate sum operation.
     */
    protected function calculateSum(array $formula, array $context): float
    {
        $fields = $formula['fields'] ?? [];
        $sum = 0;

        foreach ($fields as $field) {
            $value = data_get($context, $field, 0);
            $sum += is_numeric($value) ? $value : 0;
        }

        return $sum;
    }

    /**
     * Calculate average operation.
     */
    protected function calculateAverage(array $formula, array $context): float
    {
        $sum = $this->calculateSum($formula, $context);
        $count = count($formula['fields'] ?? []);
        
        return $count > 0 ? $sum / $count : 0;
    }

    /**
     * Calculate count operation.
     */
    protected function calculateCount(array $formula, array $context): int
    {
        $field = $formula['field'] ?? null;
        $condition = $formula['condition'] ?? null;

        if (!$field) {
            return 0;
        }

        $data = data_get($context, $field, []);
        
        if (!is_array($data)) {
            return 0;
        }

        if (!$condition) {
            return count($data);
        }

        $count = 0;
        foreach ($data as $item) {
            if ($this->evaluateCondition($condition, $item)) {
                $count++;
            }
        }

        return $count;
    }

    /**
     * Calculate concatenation operation.
     */
    protected function calculateConcat(array $formula, array $context): string
    {
        $fields = $formula['fields'] ?? [];
        $separator = $formula['separator'] ?? '';
        $values = [];

        foreach ($fields as $field) {
            $value = data_get($context, $field, '');
            if (!empty($value)) {
                $values[] = $value;
            }
        }

        return implode($separator, $values);
    }

    /**
     * Calculate conditional operation.
     */
    protected function calculateConditional(array $formula, array $context): mixed
    {
        $condition = $formula['condition'] ?? null;
        $trueValue = $formula['true_value'] ?? null;
        $falseValue = $formula['false_value'] ?? null;

        if (!$condition) {
            return $falseValue;
        }

        $conditionResult = $this->evaluateCondition($condition, $context);
        return $conditionResult ? $trueValue : $falseValue;
    }

    /**
     * Calculate date difference operation.
     */
    protected function calculateDateDiff(array $formula, array $context): int
    {
        $startField = $formula['start_field'] ?? null;
        $endField = $formula['end_field'] ?? null;
        $unit = $formula['unit'] ?? 'days';

        if (!$startField || !$endField) {
            return 0;
        }

        $startDate = data_get($context, $startField);
        $endDate = data_get($context, $endField);

        try {
            $start = \Carbon\Carbon::parse($startDate);
            $end = \Carbon\Carbon::parse($endDate);

            return match ($unit) {
                'seconds' => $end->diffInSeconds($start),
                'minutes' => $end->diffInMinutes($start),
                'hours' => $end->diffInHours($start),
                'days' => $end->diffInDays($start),
                'weeks' => $end->diffInWeeks($start),
                'months' => $end->diffInMonths($start),
                'years' => $end->diffInYears($start),
                default => $end->diffInDays($start),
            };
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Calculate percentage operation.
     */
    protected function calculatePercentage(array $formula, array $context): float
    {
        $numeratorField = $formula['numerator_field'] ?? null;
        $denominatorField = $formula['denominator_field'] ?? null;

        if (!$numeratorField || !$denominatorField) {
            return 0;
        }

        $numerator = data_get($context, $numeratorField, 0);
        $denominator = data_get($context, $denominatorField, 1);

        if ($denominator == 0) {
            return 0;
        }

        return ($numerator / $denominator) * 100;
    }

    /**
     * Evaluate condition for calculations.
     */
    protected function evaluateCondition(array $condition, $data): bool
    {
        $field = $condition['field'] ?? null;
        $operator = $condition['operator'] ?? '=';
        $value = $condition['value'] ?? null;

        if (!$field) {
            return false;
        }

        $fieldValue = is_array($data) ? data_get($data, $field) : $data;

        return match ($operator) {
            '=' => $fieldValue == $value,
            '!=' => $fieldValue != $value,
            '>' => $fieldValue > $value,
            '<' => $fieldValue < $value,
            '>=' => $fieldValue >= $value,
            '<=' => $fieldValue <= $value,
            'contains' => str_contains((string)$fieldValue, (string)$value),
            'in' => in_array($fieldValue, (array)$value),
            default => false,
        };
    }

    /**
     * Scope for required variables.
     */
    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }

    /**
     * Scope for calculated variables.
     */
    public function scopeCalculated($query)
    {
        return $query->where('source', 'calculated');
    }

    /**
     * Scope for variables by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }
}
