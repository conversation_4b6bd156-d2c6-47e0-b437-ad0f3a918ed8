<?php

namespace App\Services;

use App\Models\Lead;
use App\Models\LeadSource;
use App\Models\LeadStage;
use App\Models\LeadActivity;
use App\Models\LeadNote;
use App\Models\Client;
use App\Repositories\LeadRepository;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Carbon\Carbon;

class LeadService
{
    protected LeadRepository $leadRepository;

    public function __construct(LeadRepository $leadRepository)
    {
        $this->leadRepository = $leadRepository;
    }

    /**
     * Get leads for user with filters.
     */
    public function getLeadsForUser(int $userId, Request $request): LengthAwarePaginator
    {
        return $this->leadRepository->getLeadsForUser($userId, $request);
    }

    /**
     * Create a new lead.
     */
    public function createLead(array $data, int $userId): Lead
    {
        // Ensure user has default source and stage
        $this->ensureDefaultSourceAndStage($userId);

        // Set default source if not provided
        if (!isset($data['lead_source_id'])) {
            $defaultSource = LeadSource::forUser($userId)->where('name', 'Website')->first();
            $data['lead_source_id'] = $defaultSource->id;
        }

        // Set default stage if not provided
        if (!isset($data['lead_stage_id'])) {
            $defaultStage = LeadStage::forUser($userId)->where('name', 'New Lead')->first();
            $data['lead_stage_id'] = $defaultStage->id;
        }

        $data['user_id'] = $userId;
        $lead = Lead::create($data);

        // Calculate initial lead score
        $lead->updateLeadScore();

        // Log creation activity
        $lead->logActivity('note', 'Lead created');

        // Update source statistics
        $lead->leadSource->updateConversionStats();

        return $lead->load(['leadSource', 'leadStage']);
    }

    /**
     * Update a lead.
     */
    public function updateLead(Lead $lead, array $data): Lead
    {
        $originalStageId = $lead->lead_stage_id;
        
        $lead->update($data);

        // Log stage change if stage was updated
        if (isset($data['lead_stage_id']) && $data['lead_stage_id'] != $originalStageId) {
            $newStage = LeadStage::find($data['lead_stage_id']);
            $lead->logActivity('stage_change', "Moved to {$newStage->name}");
            
            // Auto-convert if moved to won stage
            if ($newStage->is_won_stage && $lead->status === 'active') {
                $this->convertLead($lead);
            }
            
            // Auto-mark as lost if moved to lost stage
            if ($newStage->is_lost_stage && $lead->status === 'active') {
                $lead->markAsLost('Moved to lost stage');
            }
        }

        // Recalculate lead score
        $lead->updateLeadScore();

        return $lead->load(['leadSource', 'leadStage']);
    }

    /**
     * Convert lead to client.
     */
    public function convertLead(Lead $lead): Client
    {
        if ($lead->status !== 'active') {
            throw new \Exception('Only active leads can be converted');
        }

        $client = $lead->convertToClient();
        
        // Move to won stage if not already there
        $wonStage = LeadStage::forUser($lead->user_id)->where('is_won_stage', true)->first();
        if ($wonStage && $lead->lead_stage_id !== $wonStage->id) {
            $lead->update(['lead_stage_id' => $wonStage->id]);
        }

        return $client;
    }

    /**
     * Mark lead as lost.
     */
    public function markLeadAsLost(Lead $lead, string $reason = null): void
    {
        $lead->markAsLost($reason);
        
        // Move to lost stage if not already there
        $lostStage = LeadStage::forUser($lead->user_id)->where('is_lost_stage', true)->first();
        if ($lostStage && $lead->lead_stage_id !== $lostStage->id) {
            $lead->update(['lead_stage_id' => $lostStage->id]);
        }
    }

    /**
     * Move lead to specific stage.
     */
    public function moveLeadToStage(Lead $lead, int $stageId): Lead
    {
        $stage = LeadStage::findOrFail($stageId);
        
        if ($stage->user_id !== $lead->user_id) {
            throw new \Exception('Stage does not belong to the same user');
        }

        $lead->update(['lead_stage_id' => $stageId]);
        $lead->logActivity('stage_change', "Moved to {$stage->name}");

        // Handle automatic conversions
        if ($stage->is_won_stage && $lead->status === 'active') {
            $this->convertLead($lead);
        } elseif ($stage->is_lost_stage && $lead->status === 'active') {
            $lead->markAsLost('Moved to lost stage');
        }

        return $lead->load(['leadSource', 'leadStage']);
    }

    /**
     * Add activity to lead.
     */
    public function addActivity(Lead $lead, array $data): LeadActivity
    {
        $data['lead_id'] = $lead->id;
        $data['user_id'] = $lead->user_id;
        
        if (!isset($data['completed_at']) && $data['status'] === 'completed') {
            $data['completed_at'] = now();
        }

        $activity = LeadActivity::create($data);

        // Update lead's last contacted timestamp if this is a contact activity
        if (in_array($data['type'], ['call', 'email', 'meeting'])) {
            $lead->update(['last_contacted_at' => now()]);
            $lead->increment('contact_attempts');
        }

        // Update lead score for significant activities
        if (in_array($data['type'], ['call', 'meeting', 'proposal_sent'])) {
            $lead->updateLeadScore();
        }

        return $activity;
    }

    /**
     * Add note to lead.
     */
    public function addNote(Lead $lead, array $data): LeadNote
    {
        $data['lead_id'] = $lead->id;
        $data['user_id'] = $lead->user_id;

        return LeadNote::create($data);
    }

    /**
     * Schedule follow-up for lead.
     */
    public function scheduleFollowUp(Lead $lead, Carbon $date, string $description = null): void
    {
        $lead->scheduleFollowUp($date, $description);
    }

    /**
     * Get pipeline data for user.
     */
    public function getPipelineData(int $userId): array
    {
        return $this->leadRepository->getLeadsPipeline($userId);
    }

    /**
     * Get leads needing follow-up.
     */
    public function getLeadsNeedingFollowUp(int $userId): Collection
    {
        return $this->leadRepository->getLeadsNeedingFollowUp($userId);
    }

    /**
     * Get lead analytics.
     */
    public function getLeadAnalytics(int $userId, array $filters = []): array
    {
        $analytics = $this->leadRepository->getConversionAnalytics($userId, $filters);
        $sourcePerformance = $this->leadRepository->getSourcePerformance($userId);
        $stagePerformance = $this->leadRepository->getStagePerformance($userId);
        $monthlyTrends = $this->leadRepository->getMonthlyTrends($userId);

        return [
            'overview' => $analytics,
            'source_performance' => $sourcePerformance,
            'stage_performance' => $stagePerformance,
            'monthly_trends' => $monthlyTrends,
        ];
    }

    /**
     * Import leads from CSV data.
     */
    public function importLeads(array $csvData, int $userId): array
    {
        $imported = 0;
        $errors = [];

        foreach ($csvData as $index => $row) {
            try {
                $leadData = $this->mapCsvRowToLeadData($row, $userId);
                $this->createLead($leadData, $userId);
                $imported++;
            } catch (\Exception $e) {
                $errors[] = "Row " . ($index + 1) . ": " . $e->getMessage();
            }
        }

        return [
            'imported' => $imported,
            'errors' => $errors,
        ];
    }

    /**
     * Export leads to CSV format.
     */
    public function exportLeads(int $userId, array $filters = []): array
    {
        $leads = $this->leadRepository->searchLeads($userId, $filters);

        return $leads->map(function($lead) {
            return [
                'Name' => $lead->name,
                'Email' => $lead->email,
                'Phone' => $lead->phone,
                'Company' => $lead->company_name,
                'Title' => $lead->title,
                'Source' => $lead->leadSource->name,
                'Stage' => $lead->leadStage->name,
                'Status' => ucfirst($lead->status),
                'Priority' => ucfirst($lead->priority),
                'Score' => $lead->lead_score,
                'Estimated Value' => $lead->estimated_value,
                'Created At' => $lead->created_at->format('Y-m-d H:i:s'),
                'Last Contacted' => $lead->last_contacted_at?->format('Y-m-d H:i:s'),
                'Next Follow Up' => $lead->next_follow_up_at?->format('Y-m-d H:i:s'),
            ];
        })->toArray();
    }

    /**
     * Check if user can create more leads based on plan.
     */
    public function canCreateLead(int $userId): bool
    {
        $user = \App\Models\User::find($userId);
        return PlanChecker::canCreateLead($user);
    }

    /**
     * Ensure user has default source and stage.
     */
    private function ensureDefaultSourceAndStage(int $userId): void
    {
        // Create default source if none exists
        if (LeadSource::forUser($userId)->count() === 0) {
            LeadSource::create([
                'user_id' => $userId,
                'name' => 'Website',
                'description' => 'Leads from website',
                'color' => '#3B82F6',
                'is_system_source' => true,
            ]);
        }

        // Create default stages if none exist
        if (LeadStage::forUser($userId)->count() === 0) {
            $stages = [
                ['name' => 'New Lead', 'sort_order' => 1, 'conversion_probability' => 10],
                ['name' => 'Qualified', 'sort_order' => 2, 'conversion_probability' => 25],
                ['name' => 'Proposal', 'sort_order' => 3, 'conversion_probability' => 50],
                ['name' => 'Won', 'sort_order' => 4, 'conversion_probability' => 100, 'is_won_stage' => true],
                ['name' => 'Lost', 'sort_order' => 5, 'conversion_probability' => 0, 'is_lost_stage' => true],
            ];

            foreach ($stages as $stage) {
                LeadStage::create(array_merge($stage, [
                    'user_id' => $userId,
                    'color' => '#3B82F6',
                    'is_system_stage' => true,
                ]));
            }
        }
    }

    /**
     * Map CSV row to lead data.
     */
    private function mapCsvRowToLeadData(array $row, int $userId): array
    {
        // This is a basic mapping - can be customized based on CSV format
        return [
            'name' => $row['name'] ?? $row['Name'] ?? '',
            'email' => $row['email'] ?? $row['Email'] ?? null,
            'phone' => $row['phone'] ?? $row['Phone'] ?? null,
            'company_name' => $row['company'] ?? $row['Company'] ?? null,
            'title' => $row['title'] ?? $row['Title'] ?? null,
            'estimated_value' => $row['value'] ?? $row['Estimated Value'] ?? null,
            'notes' => $row['notes'] ?? $row['Notes'] ?? null,
        ];
    }
}
