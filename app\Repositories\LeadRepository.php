<?php

namespace App\Repositories;

use App\Models\Lead;
use App\Models\LeadSource;
use App\Models\LeadStage;
use App\Models\LeadActivity;
use App\Models\LeadNote;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Http\Request;

class LeadRepository extends BaseRepository
{
    /**
     * Get the model instance
     */
    protected function getModel(): Model
    {
        return new Lead();
    }

    /**
     * Get leads for a user with filters and pagination.
     */
    public function getLeadsForUser(int $userId, Request $request): LengthAwarePaginator
    {
        $query = Lead::forUser($userId)
            ->with(['leadSource', 'leadStage', 'activities' => function($q) {
                $q->latest()->limit(3);
            }]);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('stage_id')) {
            $query->where('lead_stage_id', $request->stage_id);
        }

        if ($request->filled('source_id')) {
            $query->where('lead_source_id', $request->source_id);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('company_name', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        return $query->paginate($request->get('per_page', 15));
    }

    /**
     * Get leads grouped by stage for pipeline view.
     */
    public function getLeadsPipeline(int $userId): array
    {
        $stages = LeadStage::forUser($userId)
            ->active()
            ->ordered()
            ->get();

        $pipeline = [];
        foreach ($stages as $stage) {
            $leads = Lead::forUser($userId)
                ->inStage($stage->id)
                ->active()
                ->with(['leadSource'])
                ->orderBy('lead_score', 'desc')
                ->get();

            $pipeline[] = [
                'stage' => $stage,
                'leads' => $leads,
                'total_value' => $leads->sum('estimated_value'),
                'count' => $leads->count(),
            ];
        }

        return $pipeline;
    }

    /**
     * Get leads needing follow-up.
     */
    public function getLeadsNeedingFollowUp(int $userId): Collection
    {
        return Lead::forUser($userId)
            ->needsFollowUp()
            ->with(['leadSource', 'leadStage'])
            ->orderBy('next_follow_up_at')
            ->get();
    }

    /**
     * Get recent lead activities.
     */
    public function getRecentActivities(int $userId, int $limit = 10): Collection
    {
        return LeadActivity::whereHas('lead', function($q) use ($userId) {
                $q->where('user_id', $userId);
            })
            ->with(['lead', 'user'])
            ->latest()
            ->limit($limit)
            ->get();
    }

    /**
     * Get lead conversion analytics.
     */
    public function getConversionAnalytics(int $userId, array $filters = []): array
    {
        $query = Lead::forUser($userId);

        // Apply date filters
        if (isset($filters['start_date'])) {
            $query->where('created_at', '>=', $filters['start_date']);
        }
        if (isset($filters['end_date'])) {
            $query->where('created_at', '<=', $filters['end_date']);
        }

        $totalLeads = $query->count();
        $convertedLeads = (clone $query)->converted()->count();
        $lostLeads = (clone $query)->lost()->count();
        $activeLeads = (clone $query)->active()->count();

        $conversionRate = $totalLeads > 0 ? ($convertedLeads / $totalLeads) * 100 : 0;
        $lossRate = $totalLeads > 0 ? ($lostLeads / $totalLeads) * 100 : 0;

        return [
            'total_leads' => $totalLeads,
            'converted_leads' => $convertedLeads,
            'lost_leads' => $lostLeads,
            'active_leads' => $activeLeads,
            'conversion_rate' => round($conversionRate, 2),
            'loss_rate' => round($lossRate, 2),
            'total_estimated_value' => $query->sum('estimated_value') ?? 0,
            'converted_value' => (clone $query)->converted()->sum('estimated_value') ?? 0,
            'average_lead_score' => $query->avg('lead_score') ?? 0,
        ];
    }

    /**
     * Get source performance analytics.
     */
    public function getSourcePerformance(int $userId): Collection
    {
        return LeadSource::forUser($userId)
            ->with(['leads' => function($q) {
                $q->select('id', 'lead_source_id', 'status', 'estimated_value', 'lead_score');
            }])
            ->get()
            ->map(function($source) {
                $metrics = $source->getPerformanceMetrics();
                return array_merge($source->toArray(), $metrics);
            });
    }

    /**
     * Get stage performance analytics.
     */
    public function getStagePerformance(int $userId): Collection
    {
        return LeadStage::forUser($userId)
            ->active()
            ->ordered()
            ->with(['leads' => function($q) {
                $q->select('id', 'lead_stage_id', 'status', 'estimated_value', 'lead_score');
            }])
            ->get()
            ->map(function($stage) {
                $stats = $stage->getStageStats();
                return array_merge($stage->toArray(), $stats);
            });
    }

    /**
     * Get monthly lead trends.
     */
    public function getMonthlyTrends(int $userId, int $months = 12): array
    {
        $trends = [];
        
        for ($i = $months - 1; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $monthStart = $date->copy()->startOfMonth();
            $monthEnd = $date->copy()->endOfMonth();

            $monthData = Lead::forUser($userId)
                ->whereBetween('created_at', [$monthStart, $monthEnd])
                ->selectRaw('
                    COUNT(*) as total_leads,
                    COUNT(CASE WHEN status = "converted" THEN 1 END) as converted_leads,
                    COUNT(CASE WHEN status = "lost" THEN 1 END) as lost_leads,
                    AVG(lead_score) as avg_score,
                    SUM(estimated_value) as total_value
                ')
                ->first();

            $trends[] = [
                'month' => $date->format('M Y'),
                'month_key' => $date->format('Y-m'),
                'total_leads' => $monthData->total_leads ?? 0,
                'converted_leads' => $monthData->converted_leads ?? 0,
                'lost_leads' => $monthData->lost_leads ?? 0,
                'avg_score' => round($monthData->avg_score ?? 0, 1),
                'total_value' => $monthData->total_value ?? 0,
                'conversion_rate' => $monthData->total_leads > 0 
                    ? round(($monthData->converted_leads / $monthData->total_leads) * 100, 1) 
                    : 0,
            ];
        }

        return $trends;
    }

    /**
     * Get top performing leads.
     */
    public function getTopPerformingLeads(int $userId, int $limit = 10): Collection
    {
        return Lead::forUser($userId)
            ->active()
            ->with(['leadSource', 'leadStage'])
            ->orderBy('lead_score', 'desc')
            ->orderBy('estimated_value', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Search leads with advanced filters.
     */
    public function searchLeads(int $userId, array $filters): Collection
    {
        $query = Lead::forUser($userId);

        if (isset($filters['name'])) {
            $query->where('name', 'like', "%{$filters['name']}%");
        }

        if (isset($filters['email'])) {
            $query->where('email', 'like', "%{$filters['email']}%");
        }

        if (isset($filters['company'])) {
            $query->where('company_name', 'like', "%{$filters['company']}%");
        }

        if (isset($filters['min_score'])) {
            $query->where('lead_score', '>=', $filters['min_score']);
        }

        if (isset($filters['max_score'])) {
            $query->where('lead_score', '<=', $filters['max_score']);
        }

        if (isset($filters['min_value'])) {
            $query->where('estimated_value', '>=', $filters['min_value']);
        }

        if (isset($filters['max_value'])) {
            $query->where('estimated_value', '<=', $filters['max_value']);
        }

        return $query->with(['leadSource', 'leadStage'])->get();
    }
}
