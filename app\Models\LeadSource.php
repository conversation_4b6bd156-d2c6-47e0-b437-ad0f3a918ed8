<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class LeadSource extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'description',
        'color',
        'is_active',
        'conversion_rate',
        'total_leads',
        'converted_leads',
        'is_system_source',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'is_system_source' => 'boolean',
            'conversion_rate' => 'decimal:2',
            'total_leads' => 'integer',
            'converted_leads' => 'integer',
        ];
    }

    /**
     * Get the user that owns the lead source.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the leads for this source.
     */
    public function leads(): HasMany
    {
        return $this->hasMany(Lead::class);
    }

    /**
     * Scope a query to only include active sources.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include user's sources.
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Update conversion statistics.
     */
    public function updateConversionStats(): void
    {
        $totalLeads = $this->leads()->count();
        $convertedLeads = $this->leads()->where('status', 'converted')->count();
        
        $this->update([
            'total_leads' => $totalLeads,
            'converted_leads' => $convertedLeads,
            'conversion_rate' => $totalLeads > 0 ? ($convertedLeads / $totalLeads) * 100 : 0,
        ]);
    }

    /**
     * Get performance metrics for this source.
     */
    public function getPerformanceMetrics(): array
    {
        $leads = $this->leads();
        
        return [
            'total_leads' => $leads->count(),
            'converted_leads' => $leads->where('status', 'converted')->count(),
            'active_leads' => $leads->where('status', 'active')->count(),
            'lost_leads' => $leads->where('status', 'lost')->count(),
            'conversion_rate' => $this->conversion_rate,
            'average_lead_score' => $leads->avg('lead_score') ?? 0,
            'total_estimated_value' => $leads->sum('estimated_value') ?? 0,
            'converted_value' => $leads->where('status', 'converted')->sum('estimated_value') ?? 0,
        ];
    }

    /**
     * Check if source can be deleted.
     */
    public function canBeDeleted(): bool
    {
        return !$this->is_system_source && $this->leads()->count() === 0;
    }
}
