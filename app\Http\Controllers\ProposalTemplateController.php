<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreProposalTemplateRequest;
use App\Models\ProposalTemplate;
use App\Services\ProposalTemplateService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProposalTemplateController extends Controller
{
    protected ProposalTemplateService $templateService;

    public function __construct(ProposalTemplateService $templateService)
    {
        $this->templateService = $templateService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $templates = $this->templateService->getTemplatesForUser(Auth::id(), $request);
        $categories = $this->templateService->getCategories();

        return view('proposal-templates.index', compact('templates', 'categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = $this->templateService->getCategories();
        return view('proposal-templates.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreProposalTemplateRequest $request)
    {
        $validated = $request->validated();
        $template = $this->templateService->createTemplate($validated);

        return redirect()->route('proposal-templates.index')
                        ->with('success', 'Template created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(ProposalTemplate $proposalTemplate)
    {
        // Only allow viewing user's own templates or system templates
        if (!$proposalTemplate->is_system_template && $proposalTemplate->user_id !== Auth::id()) {
            abort(403);
        }

        return view('proposal-templates.show', compact('proposalTemplate'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ProposalTemplate $proposalTemplate)
    {
        // Only allow editing user's own templates
        if ($proposalTemplate->is_system_template || $proposalTemplate->user_id !== Auth::id()) {
            return redirect()->route('proposal-templates.index')
                ->with('error', 'You can only edit your own templates.');
        }

        $categories = $this->templateService->getCategories();
        return view('proposal-templates.edit', compact('proposalTemplate', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(StoreProposalTemplateRequest $request, ProposalTemplate $proposalTemplate)
    {
        try {
            $validated = $request->validated();
            $this->templateService->updateTemplate($proposalTemplate, $validated);

            return redirect()->route('proposal-templates.index')
                            ->with('success', 'Template updated successfully.');
        } catch (\Exception $e) {
            return redirect()->route('proposal-templates.index')
                            ->with('error', $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ProposalTemplate $proposalTemplate)
    {
        $result = $this->templateService->deleteTemplate($proposalTemplate);

        if (!$result['success']) {
            return redirect()->route('proposal-templates.index')
                            ->with('error', $result['message']);
        }

        return redirect()->route('proposal-templates.index')
                        ->with('success', 'Template deleted successfully.');
    }

    /**
     * Get template content for AJAX requests.
     */
    public function getContent(ProposalTemplate $proposalTemplate)
    {
        // Only allow accessing user's own templates or system templates
        if (!$proposalTemplate->is_system_template && $proposalTemplate->user_id !== Auth::id()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        return response()->json([
            'template' => $proposalTemplate,
            'variables' => $proposalTemplate->getTemplateVariables()
        ]);
    }
}
