<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BusinessSubscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'plan_id',
        'status',
        'starts_at',
        'ends_at',
        'cancelled_at',
        'amount_paid',
        'currency',
        'payment_gateway',
        'gateway_subscription_id',
        'gateway_data',
        'usage_limits',
        'max_users',
    ];

    protected function casts(): array
    {
        return [
            'starts_at' => 'datetime',
            'ends_at' => 'datetime',
            'cancelled_at' => 'datetime',
            'amount_paid' => 'decimal:2',
            'gateway_data' => 'array',
            'usage_limits' => 'array',
            'max_users' => 'integer',
        ];
    }

    /**
     * Get the business that owns the subscription.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the plan for this subscription.
     */
    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    /**
     * Check if subscription is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' &&
               ($this->ends_at === null || $this->ends_at->isFuture());
    }

    /**
     * Check if subscription is on trial.
     */
    public function isTrial(): bool
    {
        return $this->status === 'trial';
    }

    /**
     * Check if subscription is expired.
     */
    public function isExpired(): bool
    {
        return $this->ends_at && $this->ends_at->isPast();
    }

    /**
     * Cancel the subscription.
     */
    public function cancel(): void
    {
        $this->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
        ]);
    }

    /**
     * Get usage for a specific feature.
     */
    public function getUsage(string $featureKey): int
    {
        return $this->usage_limits[$featureKey] ?? 0;
    }

    /**
     * Increment usage for a feature.
     */
    public function incrementUsage(string $featureKey, int $amount = 1): void
    {
        $usage = $this->usage_limits ?? [];
        $usage[$featureKey] = ($usage[$featureKey] ?? 0) + $amount;
        $this->update(['usage_limits' => $usage]);
    }

    /**
     * Reset usage for all features (typically done monthly).
     */
    public function resetUsage(): void
    {
        $this->update(['usage_limits' => []]);
    }

    /**
     * Check if business can add more users.
     */
    public function canAddUser(): bool
    {
        $currentUserCount = $this->business->users()->count();
        return $currentUserCount < $this->max_users;
    }

    /**
     * Scope for active subscriptions.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where(function ($q) {
                        $q->whereNull('ends_at')
                          ->orWhere('ends_at', '>', now());
                    });
    }
}
