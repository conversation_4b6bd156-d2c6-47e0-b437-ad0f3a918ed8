<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SiteSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Artisan;

class SiteSettingsController extends Controller
{
    /**
     * Display site settings management page
     */
    public function index(Request $request)
    {
        $category = $request->get('category', 'general');

        // Get all settings grouped by category
        $allSettings = SiteSetting::orderBy('category')->orderBy('sort_order')->get()->groupBy('category');

        // Get settings for current category
        $settings = $allSettings->get($category, collect());

        // Available categories
        $categories = [
            'general' => 'General Settings',
            'branding' => 'Branding & Appearance',
            'email' => 'Email Configuration',
            'currency' => 'Currency & Pricing',
            'payment' => 'Payment Gateways',
            'features' => 'Feature Toggles',
            'security' => 'Security Settings',
            'performance' => 'Performance & Caching',
        ];

        return view('admin.settings.index', compact('settings', 'categories', 'category', 'allSettings'));
    }

    /**
     * Update site settings
     */
    public function update(Request $request)
    {
        $category = $request->get('category', 'general');
        $settings = $request->get('settings', []);

        foreach ($settings as $key => $value) {
            $setting = SiteSetting::where('key', $key)->first();

            if ($setting) {
                // Validate the setting value
                if ($setting->validation_rules) {
                    $rules = json_decode($setting->validation_rules, true);
                    $validator = Validator::make([$key => $value], [$key => $rules]);

                    if ($validator->fails()) {
                        return back()->withErrors($validator)->withInput();
                    }
                }

                $setting->setTypedValue($value);
                $setting->save();
            }
        }

        // Clear application cache if performance settings changed
        if ($category === 'performance') {
            Artisan::call('cache:clear');
            Artisan::call('config:clear');
        }

        return back()->with('success', 'Settings updated successfully!');
    }

    /**
     * Reset settings to default values
     */
    public function reset(Request $request)
    {
        $category = $request->get('category');

        if ($category) {
            SiteSetting::where('category', $category)->delete();
            $this->seedDefaultSettings($category);
        }

        return back()->with('success', "Settings for {$category} category have been reset to defaults!");
    }

    /**
     * Export settings as JSON
     */
    public function export(Request $request)
    {
        $category = $request->get('category');

        $query = SiteSetting::query();
        if ($category) {
            $query->where('category', $category);
        }

        $settings = $query->get()->map(function ($setting) {
            return [
                'key' => $setting->key,
                'value' => $setting->getTypedValue(),
                'type' => $setting->type,
                'category' => $setting->category,
                'label' => $setting->label,
                'description' => $setting->description,
            ];
        });

        $filename = $category ? "settings_{$category}.json" : 'settings_all.json';

        return response()->json($settings, 200, [
            'Content-Disposition' => "attachment; filename={$filename}",
        ]);
    }

    /**
     * Import settings from JSON
     */
    public function import(Request $request)
    {
        $request->validate([
            'settings_file' => 'required|file|mimes:json',
        ]);

        $file = $request->file('settings_file');
        $content = file_get_contents($file->getPathname());
        $settings = json_decode($content, true);

        if (!$settings) {
            return back()->withErrors(['settings_file' => 'Invalid JSON file format.']);
        }

        foreach ($settings as $settingData) {
            SiteSetting::updateOrCreate(
                ['key' => $settingData['key']],
                [
                    'value' => $settingData['value'],
                    'type' => $settingData['type'],
                    'category' => $settingData['category'],
                    'label' => $settingData['label'],
                    'description' => $settingData['description'] ?? null,
                ]
            );
        }

        return back()->with('success', 'Settings imported successfully!');
    }

    /**
     * Test email configuration
     */
    public function testEmail(Request $request)
    {
        $request->validate([
            'test_email' => 'required|email',
        ]);

        try {
            // Send test email using current settings
            \Mail::raw('This is a test email from your site settings configuration.', function ($message) use ($request) {
                $message->to($request->test_email)
                        ->subject('Test Email - Site Settings');
            });

            return response()->json(['success' => true, 'message' => 'Test email sent successfully!']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to send test email: ' . $e->getMessage()]);
        }
    }

    /**
     * Seed default settings for a category
     */
    private function seedDefaultSettings(string $category): void
    {
        $defaults = $this->getDefaultSettings();

        if (isset($defaults[$category])) {
            foreach ($defaults[$category] as $setting) {
                SiteSetting::create($setting);
            }
        }
    }

    /**
     * Get default settings configuration
     */
    private function getDefaultSettings(): array
    {
        return [
            'general' => [
                [
                    'key' => 'site.name',
                    'value' => env('APP_NAME', 'Freeligo'),
                    'type' => 'string',
                    'category' => 'general',
                    'label' => 'Site Name',
                    'description' => 'The name of your website',
                    'is_public' => true,
                    'is_required' => true,
                    'sort_order' => 1,
                ],
                [
                    'key' => 'site.description',
                    'value' => 'Run Your Freelance Business Like a Pro',
                    'type' => 'string',
                    'category' => 'general',
                    'label' => 'Site Description',
                    'description' => 'A brief description of your website',
                    'is_public' => true,
                    'sort_order' => 2,
                ],
                [
                    'key' => 'site.timezone',
                    'value' => 'UTC',
                    'type' => 'string',
                    'category' => 'general',
                    'label' => 'Default Timezone',
                    'description' => 'Default timezone for the application',
                    'sort_order' => 3,
                ],
            ],
            'branding' => [
                [
                    'key' => 'brand.primary_color',
                    'value' => env('BRAND_COLOR', '#10B981'),
                    'type' => 'string',
                    'category' => 'branding',
                    'label' => 'Primary Color',
                    'description' => 'Main brand color (hex code)',
                    'is_public' => true,
                    'sort_order' => 1,
                ],
                [
                    'key' => 'brand.logo_url',
                    'value' => '',
                    'type' => 'string',
                    'category' => 'branding',
                    'label' => 'Logo URL',
                    'description' => 'URL to your site logo',
                    'is_public' => true,
                    'sort_order' => 2,
                ],
            ],
            'currency' => [
                [
                    'key' => 'currency.code',
                    'value' => env('APP_CURRENCY', 'USD'),
                    'type' => 'string',
                    'category' => 'currency',
                    'label' => 'Currency Code',
                    'description' => 'Default currency code (USD, EUR, INR, etc.)',
                    'is_public' => true,
                    'is_required' => true,
                    'sort_order' => 1,
                ],
                [
                    'key' => 'currency.symbol',
                    'value' => env('APP_CURRENCY_SYMBOL', '$'),
                    'type' => 'string',
                    'category' => 'currency',
                    'label' => 'Currency Symbol',
                    'description' => 'Currency symbol to display',
                    'is_public' => true,
                    'is_required' => true,
                    'sort_order' => 2,
                ],
            ],
            'email' => [
                [
                    'key' => 'mail.from_name',
                    'value' => env('MAIL_FROM_NAME', env('APP_NAME')),
                    'type' => 'string',
                    'category' => 'email',
                    'label' => 'From Name',
                    'description' => 'Default sender name for emails',
                    'is_required' => true,
                    'sort_order' => 1,
                ],
                [
                    'key' => 'mail.from_address',
                    'value' => env('MAIL_FROM_ADDRESS', '<EMAIL>'),
                    'type' => 'string',
                    'category' => 'email',
                    'label' => 'From Email Address',
                    'description' => 'Default sender email address',
                    'validation_rules' => '["required", "email"]',
                    'is_required' => true,
                    'sort_order' => 2,
                ],
            ],
        ];
    }
}
