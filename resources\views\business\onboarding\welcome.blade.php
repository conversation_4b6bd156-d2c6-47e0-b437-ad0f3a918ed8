<x-app-layout>
    <div class="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 py-12">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Progress Steps -->
            <div class="mb-12">
                <x-onboarding.progress
                    :steps="[
                        ['title' => 'Welcome', 'description' => 'Get started'],
                        ['title' => 'Setup', 'description' => 'Business info'],
                        ['title' => 'Features', 'description' => 'Customize'],
                        ['title' => 'Complete', 'description' => 'All done']
                    ]"
                    :current-step="1"
                />
            </div>

            <!-- Welcome Content -->
            <div class="card shadow-card-lg">
                <div class="card-body text-center">
                    <!-- Hero Icon -->
                    <div class="relative mb-8">
                        <div class="w-24 h-24 bg-gradient-to-br from-primary-500 to-primary-600 rounded-3xl flex items-center justify-center mx-auto shadow-glow-lg">
                            <i class="fas fa-rocket text-4xl text-white"></i>
                        </div>
                        <div class="absolute -top-2 -right-2 w-8 h-8 bg-success-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-check text-white text-sm"></i>
                        </div>
                    </div>

                    <!-- Welcome Message -->
                    <div class="mb-8">
                        <h1 class="text-4xl font-bold text-secondary-900 mb-4">
                            Welcome to {{ config('app.name') }}, {{ Auth::user()->name }}! 🎉
                        </h1>
                        <h2 class="text-xl font-semibold text-primary-600 mb-4">
                            Your {{ ucfirst(str_replace('_', ' ', $business->type)) }} account is ready
                        </h2>
                        <p class="text-secondary-600 text-lg max-w-2xl mx-auto leading-relaxed">
                            We've created your business profile and you're now ready to start managing your
                            {{ $business->type === 'freelancer' ? 'freelance work' : 'business operations' }}.
                            Let's take a few minutes to customize your experience and unlock the full potential of your business.
                        </p>
                    </div>

                    <!-- Business Type Benefits -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                        @if($business->type === 'freelancer')
                            <div class="text-center group">
                                <div class="w-16 h-16 bg-gradient-to-br from-primary-100 to-primary-200 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:shadow-glow transition-all duration-300">
                                    <i class="fas fa-file-invoice-dollar text-2xl text-primary-600"></i>
                                </div>
                                <h3 class="font-semibold text-secondary-900 mb-2 text-lg">Professional Invoicing</h3>
                                <p class="text-secondary-600">Create and send professional invoices to your clients</p>
                            </div>

                            <div class="text-center group">
                                <div class="w-16 h-16 bg-gradient-to-br from-success-100 to-success-200 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:shadow-glow transition-all duration-300">
                                    <i class="fas fa-clock text-2xl text-success-600"></i>
                                </div>
                                <h3 class="font-semibold text-secondary-900 mb-2 text-lg">Time Tracking</h3>
                                <p class="text-secondary-600">Track time spent on projects and tasks</p>
                            </div>

                            <div class="text-center group">
                                <div class="w-16 h-16 bg-gradient-to-br from-accent-100 to-accent-200 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:shadow-glow transition-all duration-300">
                                    <i class="fas fa-chart-line text-2xl text-accent-600"></i>
                                </div>
                                <h3 class="font-semibold text-secondary-900 mb-2 text-lg">Financial Reports</h3>
                                <p class="text-secondary-600">Monitor your income and expenses</p>
                            </div>
                        @elseif($business->type === 'startup')
                            <div class="text-center">
                                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-users text-blue-600"></i>
                                </div>
                                <h3 class="font-semibold text-gray-900 mb-1">Team Collaboration</h3>
                                <p class="text-sm text-gray-600">Work together with your team members</p>
                            </div>
                            
                            <div class="text-center">
                                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-project-diagram text-green-600"></i>
                                </div>
                                <h3 class="font-semibold text-gray-900 mb-1">Project Management</h3>
                                <p class="text-sm text-gray-600">Organize and track your projects</p>
                            </div>
                            
                            <div class="text-center">
                                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-external-link-alt text-purple-600"></i>
                                </div>
                                <h3 class="font-semibold text-gray-900 mb-1">Client Portal</h3>
                                <p class="text-sm text-gray-600">Give clients access to their projects</p>
                            </div>
                        @else
                            <div class="text-center">
                                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-cogs text-blue-600"></i>
                                </div>
                                <h3 class="font-semibold text-gray-900 mb-1">Advanced Features</h3>
                                <p class="text-sm text-gray-600">Access to all premium features</p>
                            </div>
                            
                            <div class="text-center">
                                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-code text-green-600"></i>
                                </div>
                                <h3 class="font-semibold text-gray-900 mb-1">API Access</h3>
                                <p class="text-sm text-gray-600">Integrate with your existing tools</p>
                            </div>
                            
                            <div class="text-center">
                                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-headset text-purple-600"></i>
                                </div>
                                <h3 class="font-semibold text-gray-900 mb-1">Priority Support</h3>
                                <p class="text-sm text-gray-600">Get help when you need it most</p>
                            </div>
                        @endif
                    </div>

                    <!-- Trial Information -->
                    <div class="bg-emerald-50 border border-emerald-200 rounded-lg p-6 mb-8">
                        <div class="flex items-center justify-center mb-2">
                            <i class="fas fa-gift text-emerald-600 mr-2"></i>
                            <span class="font-semibold text-emerald-800">30-Day Free Trial</span>
                        </div>
                        <p class="text-emerald-700 text-sm">
                            Your trial ends on {{ $business->trial_ends_at->format('F j, Y') }}. 
                            Explore all features and see how we can help grow your business.
                        </p>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4">
                        <x-ui.button
                            href="{{ route('business.onboarding.setup', $business) }}"
                            variant="primary"
                            size="lg"
                            class="w-full sm:w-auto px-8 py-4 text-lg font-semibold shadow-card-hover transform hover:scale-105"
                            icon="fas fa-rocket"
                        >
                            Continue Setup
                        </x-ui.button>

                        <a href="{{ route('dashboard') }}"
                           class="w-full sm:w-auto text-secondary-600 hover:text-secondary-800 font-medium py-3 px-4 rounded-xl hover:bg-secondary-100 transition-all duration-200 text-center">
                            Skip for Now
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
