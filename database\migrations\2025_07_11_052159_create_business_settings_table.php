<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('business_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained()->onDelete('cascade');
            $table->string('currency', 3)->default('USD');
            $table->string('timezone')->default('UTC');
            $table->string('date_format')->default('Y-m-d');
            $table->string('time_format')->default('H:i');
            $table->string('fiscal_year_start')->default('01-04'); // April 1st
            $table->string('invoice_prefix')->default('INV');
            $table->string('invoice_number_format')->default('{prefix}-{year}-{number:4}');
            $table->string('quote_prefix')->default('QUO');
            $table->string('quote_number_format')->default('{prefix}-{year}-{number:4}');
            $table->integer('payment_terms')->default(30); // days
            $table->decimal('late_fee_percentage', 5, 2)->default(0.00);
            $table->json('tax_settings')->nullable();
            $table->json('email_settings')->nullable();
            $table->json('notification_settings')->nullable();
            $table->json('branding_settings')->nullable();
            $table->json('feature_settings')->nullable();
            $table->json('integration_settings')->nullable();
            $table->json('workflow_settings')->nullable();
            $table->json('security_settings')->nullable();
            $table->timestamps();

            $table->unique('business_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('business_settings');
    }
};
