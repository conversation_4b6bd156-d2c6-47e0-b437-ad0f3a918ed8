<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateProposalRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:2000'],
            'content' => ['required', 'string'],
            'client_id' => ['nullable', 'exists:clients,id', function ($attribute, $value, $fail) {
                if ($value) {
                    $client = \App\Models\Client::find($value);
                    if ($client && $client->user_id !== auth()->id()) {
                        $fail('The selected client is invalid.');
                    }
                }
            }],
            'total_amount' => ['nullable', 'numeric', 'min:0', 'max:999999999'],
            'currency' => ['nullable', 'string', 'size:3'],
            'valid_until' => ['nullable', 'date', 'after:today'],
            'sections' => ['nullable', 'array'],
            'sections.*' => ['string', 'max:1000'],
            'custom_fields' => ['nullable', 'array'],
            'custom_fields.*' => ['string', 'max:1000'],
            'internal_notes' => ['nullable', 'string', 'max:5000'],
            'status' => ['nullable', 'in:draft,sent,viewed,accepted,rejected,expired'],
            'client_notes' => ['nullable', 'string', 'max:5000'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'title.required' => 'Proposal title is required.',
            'content.required' => 'Proposal content is required.',
            'client_id.exists' => 'The selected client does not exist.',
            'total_amount.numeric' => 'Total amount must be a valid number.',
            'total_amount.min' => 'Total amount cannot be negative.',
            'valid_until.after' => 'Valid until date must be in the future.',
            'currency.size' => 'Currency must be a 3-character code (e.g., USD, INR).',
            'status.in' => 'Invalid proposal status.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert empty strings to null for nullable fields
        $this->merge([
            'client_id' => $this->client_id ?: null,
            'total_amount' => $this->total_amount ?: null,
            'valid_until' => $this->valid_until ?: null,
            'description' => $this->description ?: null,
            'internal_notes' => $this->internal_notes ?: null,
            'client_notes' => $this->client_notes ?: null,
        ]);
    }
}
