<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    {{ __('Plan Management') }}
                </h2>
                <p class="text-sm text-gray-600 mt-1">Manage subscription plans and pricing</p>
            </div>
            <a href="{{ route('admin.plans.create') }}" 
               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                <i class="fas fa-plus mr-2"></i>
                Create Plan
            </a>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Plans Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @forelse($plans as $plan)
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                        <!-- Plan Header -->
                        <div class="p-6 {{ $plan->is_popular ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white' : 'bg-gray-50' }}">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h3 class="text-lg font-semibold {{ $plan->is_popular ? 'text-white' : 'text-gray-900' }}">
                                        {{ $plan->name }}
                                    </h3>
                                    @if($plan->is_popular)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white text-blue-600 mt-1">
                                            <i class="fas fa-star mr-1"></i>
                                            Popular
                                        </span>
                                    @endif
                                </div>
                                <div class="flex items-center space-x-2">
                                    @if($plan->is_active)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-check-circle mr-1"></i>
                                            Active
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <i class="fas fa-times-circle mr-1"></i>
                                            Inactive
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Plan Content -->
                        <div class="p-6">
                            <!-- Pricing -->
                            <div class="mb-4">
                                <div class="flex items-baseline">
                                    <span class="text-3xl font-bold text-gray-900">
                                        @if($plan->price == 0)
                                            Free
                                        @else
                                            ₹{{ number_format($plan->price) }}
                                        @endif
                                    </span>
                                    @if($plan->price > 0)
                                        <span class="text-gray-500 ml-1">/month</span>
                                    @endif
                                </div>
                                @if($plan->description)
                                    <p class="text-sm text-gray-600 mt-2">{{ $plan->description }}</p>
                                @endif
                            </div>

                            <!-- Features -->
                            @if($plan->planFeatures->count() > 0)
                                <div class="mb-6">
                                    <h4 class="text-sm font-medium text-gray-900 mb-3">Features:</h4>
                                    <ul class="space-y-2">
                                        @foreach($plan->planFeatures->take(5) as $feature)
                                            <li class="flex items-start">
                                                <i class="fas fa-check text-green-500 text-sm mt-0.5 mr-2 flex-shrink-0"></i>
                                                <span class="text-sm text-gray-700">
                                                    {{ $feature->feature_name }}
                                                    @if($feature->feature_value)
                                                        <span class="font-medium">{{ $feature->feature_value }}</span>
                                                    @endif
                                                </span>
                                            </li>
                                        @endforeach
                                        @if($plan->planFeatures->count() > 5)
                                            <li class="text-sm text-gray-500">
                                                +{{ $plan->planFeatures->count() - 5 }} more features
                                            </li>
                                        @endif
                                    </ul>
                                </div>
                            @endif

                            <!-- Statistics -->
                            <div class="mb-6 p-3 bg-gray-50 rounded-lg">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Subscribers:</span>
                                    <span class="font-medium text-gray-900">{{ $plan->userSubscriptions->count() }}</span>
                                </div>
                            </div>

                            <!-- Actions -->
                            <div class="flex space-x-2">
                                <a href="{{ route('admin.plans.show', $plan) }}" 
                                   class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded text-sm text-center transition-colors duration-200">
                                    <i class="fas fa-eye mr-1"></i>
                                    View
                                </a>
                                <a href="{{ route('admin.plans.edit', $plan) }}" 
                                   class="flex-1 bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-2 rounded text-sm text-center transition-colors duration-200">
                                    <i class="fas fa-edit mr-1"></i>
                                    Edit
                                </a>
                                <form action="{{ route('admin.plans.toggle-status', $plan) }}" method="POST" class="flex-1">
                                    @csrf
                                    @method('PATCH')
                                    <button type="submit" 
                                            class="w-full {{ $plan->is_active ? 'bg-red-100 hover:bg-red-200 text-red-700' : 'bg-green-100 hover:bg-green-200 text-green-700' }} px-3 py-2 rounded text-sm transition-colors duration-200">
                                        <i class="fas fa-{{ $plan->is_active ? 'pause' : 'play' }} mr-1"></i>
                                        {{ $plan->is_active ? 'Deactivate' : 'Activate' }}
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-span-full text-center py-12">
                        <i class="fas fa-credit-card text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No plans found</h3>
                        <p class="text-gray-500 mb-4">Get started by creating your first subscription plan.</p>
                        <a href="{{ route('admin.plans.create') }}" 
                           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 inline-flex items-center">
                            <i class="fas fa-plus mr-2"></i>
                            Create Plan
                        </a>
                    </div>
                @endforelse
            </div>
        </div>
    </div>
</x-app-layout>
