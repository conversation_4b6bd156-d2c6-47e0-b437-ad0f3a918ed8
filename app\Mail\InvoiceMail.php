<?php

namespace App\Mail;

use App\Models\Invoice;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Queue\SerializesModels;

class InvoiceMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public Invoice $invoice;
    public User $sender;
    public bool $isReminder;

    /**
     * Create a new message instance.
     */
    public function __construct(Invoice $invoice, bool $isReminder = false)
    {
        $this->invoice = $invoice;
        $this->sender = $invoice->user;
        $this->isReminder = $isReminder;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $subject = $this->isReminder 
            ? "Payment Reminder: Invoice #{$this->invoice->invoice_number}"
            : "New Invoice #{$this->invoice->invoice_number} from {$this->sender->business_name ?: $this->sender->name}";

        return new Envelope(
            subject: $subject,
            to: [$this->invoice->client->email],
            replyTo: [$this->sender->email],
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.invoice',
            with: [
                'invoice' => $this->invoice,
                'sender' => $this->sender,
                'client' => $this->invoice->client,
                'business_name' => $this->sender->business_name ?: $this->sender->name,
                'isReminder' => $this->isReminder,
                'header_title' => $this->isReminder ? 'Payment Reminder' : 'New Invoice',
                'header_subtitle' => $this->isReminder 
                    ? 'A friendly reminder about your pending payment'
                    : 'Thank you for your business',
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        $attachments = [];
        
        // Add PDF attachment if available
        if ($this->invoice->pdf_path && file_exists(storage_path('app/' . $this->invoice->pdf_path))) {
            $attachments[] = Attachment::fromStorageDisk('local', $this->invoice->pdf_path)
                ->as("Invoice-{$this->invoice->invoice_number}.pdf")
                ->withMime('application/pdf');
        }
        
        return $attachments;
    }
}
