<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Workflow extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'description',
        'trigger_type',
        'trigger_conditions',
        'actions',
        'status',
        'is_active',
        'execution_count',
        'last_executed_at',
        'category',
        'priority',
        'execution_settings',
        'success_rate',
        'consecutive_failures',
        'last_failure_at',
        'performance_metrics',
        'ai_optimization_enabled',
        'ai_insights',
        'template_id',
        'custom_variables',
        'auto_pause_on_failure',
        'max_executions_per_day',
        'executions_reset_date',
        'daily_execution_count',
    ];

    protected function casts(): array
    {
        return [
            'trigger_conditions' => 'array',
            'actions' => 'array',
            'is_active' => 'boolean',
            'execution_count' => 'integer',
            'last_executed_at' => 'datetime',
            'execution_settings' => 'array',
            'success_rate' => 'decimal:2',
            'consecutive_failures' => 'integer',
            'last_failure_at' => 'datetime',
            'performance_metrics' => 'array',
            'ai_optimization_enabled' => 'boolean',
            'ai_insights' => 'array',
            'custom_variables' => 'array',
            'auto_pause_on_failure' => 'boolean',
            'max_executions_per_day' => 'integer',
            'executions_reset_date' => 'date',
            'daily_execution_count' => 'integer',
            'priority' => 'integer',
        ];
    }

    /**
     * Get the user that owns the workflow.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }



    /**
     * Get the executions for this workflow.
     */
    public function executions(): HasMany
    {
        return $this->hasMany(WorkflowExecution::class);
    }



    /**
     * Get the workflow steps.
     */
    public function steps(): HasMany
    {
        return $this->hasMany(WorkflowStep::class)->orderBy('step_order');
    }

    /**
     * Get the workflow variables.
     */
    public function variables(): HasMany
    {
        return $this->hasMany(WorkflowVariable::class);
    }

    /**
     * Get the workflow metrics.
     */
    public function metrics(): HasMany
    {
        return $this->hasMany(WorkflowMetric::class);
    }

    /**
     * Get the workflow template.
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(WorkflowTemplate::class, 'template_id');
    }

    /**
     * Scope for active workflows.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for workflows by trigger type.
     */
    public function scopeByTrigger($query, string $triggerType)
    {
        return $query->where('trigger_type', $triggerType);
    }

    /**
     * Check if workflow conditions are met.
     */
    public function conditionsMet(array $data): bool
    {
        if (empty($this->trigger_conditions)) {
            return true;
        }

        foreach ($this->trigger_conditions as $condition) {
            if (!$this->evaluateCondition($condition, $data)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Evaluate a single condition.
     */
    protected function evaluateCondition(array $condition, array $data): bool
    {
        $field = $condition['field'];
        $operator = $condition['operator'];
        $value = $condition['value'];
        $dataValue = data_get($data, $field);

        return match ($operator) {
            'equals' => $dataValue == $value,
            'not_equals' => $dataValue != $value,
            'greater_than' => $dataValue > $value,
            'less_than' => $dataValue < $value,
            'contains' => str_contains($dataValue, $value),
            'starts_with' => str_starts_with($dataValue, $value),
            'ends_with' => str_ends_with($dataValue, $value),
            'in' => in_array($dataValue, (array) $value),
            'not_in' => !in_array($dataValue, (array) $value),
            default => false
        };
    }

    /**
     * Increment execution count.
     */
    public function incrementExecution(): void
    {
        $this->increment('execution_count');
        $this->incrementDailyExecution();
        $this->update(['last_executed_at' => now()]);
    }

    /**
     * Increment daily execution count.
     */
    public function incrementDailyExecution(): void
    {
        $today = now()->toDateString();

        if ($this->executions_reset_date?->toDateString() !== $today) {
            $this->update([
                'daily_execution_count' => 1,
                'executions_reset_date' => $today,
            ]);
        } else {
            $this->increment('daily_execution_count');
        }
    }

    /**
     * Check if workflow can execute today.
     */
    public function canExecuteToday(): bool
    {
        if (!$this->max_executions_per_day) {
            return true;
        }

        $today = now()->toDateString();

        if ($this->executions_reset_date?->toDateString() !== $today) {
            return true;
        }

        return $this->daily_execution_count < $this->max_executions_per_day;
    }

    /**
     * Record workflow failure.
     */
    public function recordFailure(string $errorMessage = null): void
    {
        $this->increment('consecutive_failures');
        $this->update(['last_failure_at' => now()]);

        // Auto-pause if enabled and threshold reached
        if ($this->auto_pause_on_failure && $this->consecutive_failures >= 3) {
            $this->update(['status' => 'paused']);
        }

        // Update success rate
        $this->updateSuccessRate();
    }

    /**
     * Record workflow success.
     */
    public function recordSuccess(): void
    {
        $this->update(['consecutive_failures' => 0]);
        $this->updateSuccessRate();
    }

    /**
     * Update success rate based on recent executions.
     */
    protected function updateSuccessRate(): void
    {
        $recentExecutions = $this->executions()
            ->where('created_at', '>=', now()->subDays(30))
            ->get();

        if ($recentExecutions->count() > 0) {
            $successfulExecutions = $recentExecutions->where('status', 'completed')->count();
            $successRate = ($successfulExecutions / $recentExecutions->count()) * 100;

            $this->update(['success_rate' => round($successRate, 2)]);
        }
    }

    /**
     * Get workflow performance score.
     */
    public function getPerformanceScore(): float
    {
        $metrics = [
            'success_rate' => $this->success_rate * 0.4, // 40% weight
            'execution_frequency' => min(($this->execution_count / 30), 10) * 0.2, // 20% weight
            'recent_activity' => $this->last_executed_at?->diffInDays(now()) <= 7 ? 20 : 0, // 20% weight
            'failure_penalty' => max(0, 20 - ($this->consecutive_failures * 5)), // 20% weight
        ];

        return array_sum($metrics);
    }

    /**
     * Check if workflow needs optimization.
     */
    public function needsOptimization(): bool
    {
        return $this->success_rate < 80 ||
               $this->consecutive_failures >= 2 ||
               ($this->ai_optimization_enabled && !$this->ai_insights);
    }

    /**
     * Scope for high priority workflows.
     */
    public function scopeHighPriority($query)
    {
        return $query->where('priority', '>=', 8);
    }

    /**
     * Scope for workflows by category.
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope for AI-enabled workflows.
     */
    public function scopeAiEnabled($query)
    {
        return $query->where('ai_optimization_enabled', true);
    }
}
