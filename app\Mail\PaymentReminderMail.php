<?php

namespace App\Mail;

use App\Models\Invoice;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class PaymentReminderMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public Invoice $invoice;
    public int $daysOverdue;
    public string $reminderType;

    /**
     * Create a new message instance.
     */
    public function __construct(Invoice $invoice, string $reminderType = 'gentle')
    {
        $this->invoice = $invoice;
        $this->daysOverdue = $invoice->due_date->isPast() ? $invoice->due_date->diffInDays(now()) : 0;
        $this->reminderType = $this->determineReminderType($reminderType);
    }

    private function determineReminderType(string $type): string
    {
        if ($type !== 'auto') {
            return $type;
        }

        // Auto-determine based on days overdue
        if ($this->daysOverdue <= 0) {
            return 'gentle';
        } elseif ($this->daysOverdue <= 7) {
            return 'urgent';
        } else {
            return 'final';
        }
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $subjects = [
            'gentle' => "Friendly Reminder: Invoice #{$this->invoice->invoice_number} Payment Due",
            'urgent' => "Urgent: Invoice #{$this->invoice->invoice_number} Payment Overdue",
            'final' => "Final Notice: Invoice #{$this->invoice->invoice_number} - Immediate Action Required"
        ];

        return new Envelope(
            subject: $subjects[$this->reminderType] ?? $subjects['gentle'],
            to: [$this->invoice->client->email],
            replyTo: [$this->invoice->user->email],
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.payment-reminder',
            with: [
                'invoice' => $this->invoice,
                'client' => $this->invoice->client,
                'sender' => $this->invoice->user,
                'business_name' => $this->invoice->user->business_name ?: $this->invoice->user->name,
                'daysOverdue' => $this->daysOverdue,
                'isOverdue' => $this->daysOverdue > 0,
                'reminderType' => $this->reminderType,
                'header_title' => $this->reminderType === 'final' ? 'Final Payment Notice' : 'Payment Reminder',
                'header_subtitle' => $this->getHeaderSubtitle(),
            ],
        );
    }

    private function getHeaderSubtitle(): string
    {
        return match($this->reminderType) {
            'gentle' => 'A friendly reminder about your upcoming payment',
            'urgent' => 'Your payment is now overdue - please pay as soon as possible',
            'final' => 'Immediate action required to avoid additional charges',
            default => 'Payment reminder'
        };
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
