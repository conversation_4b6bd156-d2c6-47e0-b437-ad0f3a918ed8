@extends('emails.layouts.app')

@section('content')
    <!-- <PERSON>mind<PERSON> -->
    <div style="text-align: center; margin-bottom: 32px;">
        @if($reminderType === 'final')
            <div style="font-size: 64px; margin-bottom: 16px;">🚨</div>
            <h2 style="color: #dc2626; margin-bottom: 8px;">Final Payment Notice</h2>
            <p class="lead" style="color: #ef4444;">
                Immediate action required - this is our final notice regarding your overdue payment.
            </p>
        @elseif($reminderType === 'urgent')
            <div style="font-size: 64px; margin-bottom: 16px;">⚠️</div>
            <h2 style="color: #f59e0b; margin-bottom: 8px;">Urgent Payment Reminder</h2>
            <p class="lead" style="color: #f59e0b;">
                Your payment is now overdue. Please settle this invoice as soon as possible.
            </p>
        @else
            <div style="font-size: 64px; margin-bottom: 16px;">🔔</div>
            <h2 style="color: #10b981; margin-bottom: 8px;">Friendly Payment Reminder</h2>
            <p class="lead" style="color: #059669;">
                Just a gentle reminder about your upcoming payment. Thank you for your business!
            </p>
        @endif
    </div>
    <!-- Personal Greeting -->
    <p style="font-size: 18px; color: #374151; margin-bottom: 24px;">
        Dear {{ $client->name }},
    </p>

    <!-- Payment Status Message -->
    @if($isOverdue)
        <div style="background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%); border: 1px solid #f87171; border-radius: 12px; padding: 20px; margin: 24px 0;">
            <p style="color: #dc2626; margin: 0; font-weight: 600;">
                ⚠️ Your payment for Invoice #{{ $invoice->invoice_number }} is now <strong>{{ $daysOverdue }} day(s) overdue</strong>.
                @if($reminderType === 'final')
                    This is our final notice before we may need to take further action.
                @endif
            </p>
        </div>
    @else
        <div style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); border: 1px solid #f59e0b; border-radius: 12px; padding: 20px; margin: 24px 0;">
            <p style="color: #92400e; margin: 0; font-weight: 600;">
                📅 Your payment for Invoice #{{ $invoice->invoice_number }} is due {{ abs($invoice->due_date->diffInDays(now(), false)) === 0 ? 'today' : 'in ' . abs($invoice->due_date->diffInDays(now(), false)) . ' day(s)' }}.
            </p>
        </div>
    @endif

    <!-- Invoice Details -->
    <div class="{{ $reminderType === 'final' ? 'warning-box' : ($isOverdue ? 'warning-box' : 'info-box') }}">
        <h3 style="color: {{ $reminderType === 'final' ? '#dc2626' : ($isOverdue ? '#f59e0b' : '#1e40af') }}; margin: 0 0 16px 0;">📋 Invoice Details</h3>
        <table style="width: 100%; border-collapse: collapse;">
            <tr style="border-bottom: 1px solid {{ $reminderType === 'final' ? '#fecaca' : ($isOverdue ? '#fde68a' : '#bae6fd') }};">
                <td style="padding: 12px 0; color: {{ $reminderType === 'final' ? '#dc2626' : ($isOverdue ? '#f59e0b' : '#1e40af') }}; font-weight: 600;">Invoice Number:</td>
                <td style="padding: 12px 0; color: {{ $reminderType === 'final' ? '#dc2626' : ($isOverdue ? '#f59e0b' : '#1e40af') }}; font-weight: 700; text-align: right; font-family: monospace;">#{{ $invoice->invoice_number }}</td>
            </tr>
            <tr style="border-bottom: 1px solid {{ $reminderType === 'final' ? '#fecaca' : ($isOverdue ? '#fde68a' : '#bae6fd') }};">
                <td style="padding: 12px 0; color: {{ $reminderType === 'final' ? '#dc2626' : ($isOverdue ? '#f59e0b' : '#1e40af') }}; font-weight: 600;">Original Due Date:</td>
                <td style="padding: 12px 0; color: {{ $reminderType === 'final' ? '#dc2626' : ($isOverdue ? '#f59e0b' : '#1e40af') }}; font-weight: 700; text-align: right;">{{ $invoice->due_date->format('M d, Y') }}</td>
            </tr>
            <tr>
                <td style="padding: 12px 0; color: {{ $reminderType === 'final' ? '#dc2626' : ($isOverdue ? '#f59e0b' : '#1e40af') }}; font-weight: 600;">Amount Due:</td>
                <td style="padding: 12px 0; color: {{ $reminderType === 'final' ? '#dc2626' : ($isOverdue ? '#f59e0b' : '#1e40af') }}; font-weight: 700; text-align: right; font-size: 20px;">₹{{ number_format($invoice->total_amount, 2) }}</td>
            </tr>
        </table>
    </div>
    <!-- Payment Action -->
    <div style="text-align: center; margin: 32px 0;">
        @if($reminderType === 'final')
            <a href="{{ route('invoices.pay', $invoice) }}" class="btn btn-primary" style="background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); font-size: 18px; padding: 18px 36px;">
                🚨 Pay Immediately - ₹{{ number_format($invoice->total_amount, 2) }}
            </a>
        @elseif($isOverdue)
            <a href="{{ route('invoices.pay', $invoice) }}" class="btn btn-primary" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); font-size: 18px; padding: 18px 36px;">
                ⚠️ Pay Now - ₹{{ number_format($invoice->total_amount, 2) }}
            </a>
        @else
            <a href="{{ route('invoices.pay', $invoice) }}" class="btn btn-primary" style="font-size: 18px; padding: 18px 36px;">
                💳 Make Payment - ₹{{ number_format($invoice->total_amount, 2) }}
            </a>
        @endif

        <br><br>
        <a href="{{ route('invoices.show', $invoice) }}" class="btn btn-outline">
            📄 View Full Invoice
        </a>
    </div>

    <!-- Payment Options -->
    <div class="success-box">
        <h3 style="color: #166534; margin: 0 0 16px 0;">💳 Easy Payment Options</h3>
        <p style="color: #15803d; margin: 0 0 16px 0;">
            We accept multiple secure payment methods for your convenience:
        </p>
        <div style="display: flex; flex-wrap: wrap; gap: 12px; justify-content: center;">
            <span style="background-color: rgba(255, 255, 255, 0.7); padding: 8px 16px; border-radius: 20px; font-size: 12px; color: #166534;">💳 Credit Cards</span>
            <span style="background-color: rgba(255, 255, 255, 0.7); padding: 8px 16px; border-radius: 20px; font-size: 12px; color: #166534;">🏦 Net Banking</span>
            <span style="background-color: rgba(255, 255, 255, 0.7); padding: 8px 16px; border-radius: 20px; font-size: 12px; color: #166534;">📱 UPI</span>
            <span style="background-color: rgba(255, 255, 255, 0.7); padding: 8px 16px; border-radius: 20px; font-size: 12px; color: #166534;">💰 Digital Wallets</span>
        </div>
    </div>

    @if($reminderType === 'final')
        <!-- Final Notice Warning -->
        <div style="background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%); border: 2px solid #dc2626; border-radius: 12px; padding: 24px; margin: 24px 0; text-align: center;">
            <div style="font-size: 48px; margin-bottom: 16px;">🚨</div>
            <h3 style="color: #dc2626; margin: 0 0 12px 0;">Final Notice - Immediate Action Required</h3>
            <p style="color: #dc2626; margin: 0 0 16px 0; font-weight: 600;">
                This is our final notice regarding this overdue payment. If payment is not received within 48 hours, we may need to:
            </p>
            <ul style="color: #dc2626; text-align: left; margin: 16px 0; padding-left: 20px;">
                <li>Apply late payment charges</li>
                <li>Suspend services until payment is received</li>
                <li>Initiate collection procedures</li>
                <li>Report to credit agencies (if applicable)</li>
            </ul>
            <p style="color: #dc2626; margin: 16px 0 0 0; font-style: italic;">
                Please contact us immediately if you need to discuss payment arrangements.
            </p>
        </div>
    @elseif($isOverdue)
        <!-- Overdue Notice -->
        <div class="warning-box">
            <h3 style="color: #92400e; margin: 0 0 12px 0;">⚠️ Payment Overdue</h3>
            <p style="color: #b45309; margin: 0 0 16px 0;">
                This invoice is now {{ $daysOverdue }} day(s) overdue. To avoid any late fees or service interruptions, please settle this amount as soon as possible.
            </p>
            <p style="color: #b45309; margin: 0; font-size: 14px;">
                If you're experiencing any payment difficulties, please contact us to discuss alternative arrangements.
            </p>
        </div>
    @endif

    <!-- Contact Information -->
    <div class="info-box">
        <h3>📞 Questions or Need Help?</h3>
        <p>If you have any questions about this invoice or need assistance with payment, please contact us:</p>

        <table style="width: 100%; border-collapse: collapse; margin: 16px 0;">
            <tr>
                <td style="padding: 8px 0; color: #64748b; width: 30%;">Business:</td>
                <td style="padding: 8px 0; color: #1e293b; font-weight: 600;">{{ $business_name }}</td>
            </tr>
            <tr>
                <td style="padding: 8px 0; color: #64748b;">Contact:</td>
                <td style="padding: 8px 0; color: #1e293b; font-weight: 600;">{{ $sender->name }}</td>
            </tr>
            <tr>
                <td style="padding: 8px 0; color: #64748b;">Email:</td>
                <td style="padding: 8px 0;">
                    <a href="mailto:{{ $sender->email }}" style="color: #10b981; text-decoration: none; font-weight: 600;">{{ $sender->email }}</a>
                </td>
            </tr>
            @if($sender->phone)
            <tr>
                <td style="padding: 8px 0; color: #64748b;">Phone:</td>
                <td style="padding: 8px 0; color: #1e293b; font-weight: 600;">{{ $sender->phone }}</td>
            </tr>
            @endif
        </table>
    </div>

    <!-- Thank You Message -->
    <div style="text-align: center; margin: 40px 0;">
        @if($reminderType === 'final')
            <h3 style="color: #dc2626; margin-bottom: 16px;">Immediate Action Required</h3>
            <p style="color: #64748b; font-size: 16px;">
                We value our business relationship and hope to resolve this matter immediately.
            </p>
        @elseif($isOverdue)
            <h3 style="color: #f59e0b; margin-bottom: 16px;">Thank You for Your Prompt Attention</h3>
            <p style="color: #64748b; font-size: 16px;">
                We appreciate your business and look forward to receiving your payment soon.
            </p>
        @else
            <h3 style="color: #10b981; margin-bottom: 16px;">Thank You for Your Business!</h3>
            <p style="color: #64748b; font-size: 16px;">
                We appreciate your prompt attention to this payment reminder.
            </p>
        @endif
    </div>

    <!-- Footer Note -->
    <div style="border-top: 1px solid #e2e8f0; padding-top: 20px; margin-top: 32px;">
        <p style="color: #64748b; font-size: 14px; text-align: center; margin: 0;">
            This payment reminder was sent by {{ $business_name }} via {{ config('app.name') }}.
            <br>
            If you believe this reminder was sent in error, please contact us immediately.
        </p>
    </div>
@endsection
