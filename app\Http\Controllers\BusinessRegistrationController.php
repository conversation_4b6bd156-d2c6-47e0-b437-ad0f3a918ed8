<?php

namespace App\Http\Controllers;

use App\Models\Business;
use App\Models\BusinessSettings;
use App\Models\BusinessUser;
use App\Models\Plan;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class BusinessRegistrationController extends Controller
{
    /**
     * Show business type selection page.
     */
    public function selectType(): View
    {
        return view('business.select-type');
    }

    /**
     * Show business registration form.
     */
    public function create(Request $request): View
    {
        $businessType = $request->get('type', 'freelancer');

        if (!in_array($businessType, ['freelancer', 'startup', 'small_business'])) {
            $businessType = 'freelancer';
        }

        return view('business.register', compact('businessType'));
    }

    /**
     * Store business registration.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'business_type' => ['required', Rule::in(['freelancer', 'startup', 'small_business'])],
            'business_name' => ['required', 'string', 'max:255'],
            'email' => ['nullable', 'email', 'max:255'],
            'phone' => ['nullable', 'string', 'max:20'],
            'website' => ['nullable', 'url', 'max:255'],
            'address' => ['nullable', 'string'],
            'city' => ['nullable', 'string', 'max:100'],
            'state' => ['nullable', 'string', 'max:100'],
            'country' => ['nullable', 'string', 'max:100'],
            'postal_code' => ['nullable', 'string', 'max:20'],
            'tax_number' => ['nullable', 'string', 'max:50'],
            'registration_number' => ['nullable', 'string', 'max:50'],
            'industry' => ['nullable', 'string', 'max:100'],
            'employee_count' => ['nullable', 'integer', 'min:1'],
            'founded_year' => ['nullable', 'integer', 'min:1900', 'max:' . date('Y')],
            'description' => ['nullable', 'string', 'max:1000'],
        ]);

        try {
            DB::beginTransaction();

            // Create business
            $business = Business::create([
                'name' => $validated['business_name'],
                'slug' => Business::generateSlug($validated['business_name']),
                'type' => $validated['business_type'],
                'email' => $validated['email'] ?? null,
                'phone' => $validated['phone'] ?? null,
                'website' => $validated['website'] ?? null,
                'address' => $validated['address'] ?? null,
                'city' => $validated['city'] ?? null,
                'state' => $validated['state'] ?? null,
                'country' => $validated['country'] ?? null,
                'postal_code' => $validated['postal_code'] ?? null,
                'tax_number' => $validated['tax_number'] ?? null,
                'registration_number' => $validated['registration_number'] ?? null,
                'industry' => $validated['industry'] ?? null,
                'employee_count' => $validated['employee_count'] ?? null,
                'founded_year' => $validated['founded_year'] ?? null,
                'description' => $validated['description'] ?? null,
                'is_active' => true,
                'trial_ends_at' => now()->addDays(30), // 30-day trial
            ]);

            // Create business settings with defaults
            $defaultSettings = BusinessSettings::getDefaultSettings($validated['business_type']);
            BusinessSettings::create(array_merge($defaultSettings, [
                'business_id' => $business->id,
            ]));

            // Add current user as business owner
            BusinessUser::create([
                'business_id' => $business->id,
                'user_id' => Auth::id(),
                'role' => 'owner',
                'is_owner' => true,
                'status' => 'active',
                'joined_at' => now(),
            ]);

            // Assign free plan to business
            $freePlan = Plan::getFreePlan();
            if ($freePlan) {
                $business->update(['subscription_plan_id' => $freePlan->id]);
            }

            DB::commit();

            // Set business context in session
            session(['current_business_id' => $business->id]);

            return redirect()->route('business.onboarding.welcome', $business)
                           ->with('success', 'Business registered successfully! Welcome to your new business account.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()
                        ->with('error', 'Failed to register business. Please try again.');
        }
    }
}
