<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\InvoicePayment;
use App\Services\ClientPortalService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PaymentTrackingController extends Controller
{
    protected ClientPortalService $clientPortalService;

    public function __construct(ClientPortalService $clientPortalService)
    {
        $this->clientPortalService = $clientPortalService;
    }

    /**
     * Display payment tracking dashboard.
     */
    public function index(Request $request)
    {
        $userId = Auth::id();

        // Get payment statistics
        $stats = $this->getPaymentStats($userId);

        // Get recent payments
        $recentPayments = $this->getRecentPayments($userId, 10);

        // Get invoices with payment activity
        $invoicesQuery = Invoice::with(['client', 'invoicePayments'])
            ->where('user_id', $userId)
            ->whereHas('invoicePayments');

        // Apply filters
        if ($request->filled('status')) {
            $invoicesQuery->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $invoicesQuery->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $invoicesQuery->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $invoicesQuery->where(function ($query) use ($search) {
                $query->where('invoice_number', 'like', "%{$search}%")
                      ->orWhereHas('client', function ($q) use ($search) {
                          $q->where('name', 'like', "%{$search}%")
                            ->orWhere('company_name', 'like', "%{$search}%");
                      });
            });
        }

        $invoices = $invoicesQuery->latest()->paginate(15);

        return view('payment-tracking.index', compact('stats', 'recentPayments', 'invoices'));
    }

    /**
     * Show detailed payment tracking for specific invoice.
     */
    public function show(Invoice $invoice)
    {
        $this->authorize('view', $invoice);

        $paymentSummary = $this->clientPortalService->getPaymentSummary($invoice);
        $paymentAnalytics = $this->clientPortalService->getPaymentAnalytics($invoice);
        $invoiceStats = $this->clientPortalService->getInvoiceStats($invoice);

        return view('payment-tracking.show', compact(
            'invoice',
            'paymentSummary',
            'paymentAnalytics',
            'invoiceStats'
        ));
    }

    /**
     * Get payment statistics for user.
     */
    private function getPaymentStats(int $userId): array
    {
        $invoicesWithPayments = Invoice::where('user_id', $userId)
            ->whereHas('invoicePayments')
            ->get();

        $totalPayments = InvoicePayment::whereHas('invoice', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })->get();

        $completedPayments = $totalPayments->where('status', 'completed');
        $pendingPayments = $totalPayments->where('status', 'pending');
        $failedPayments = $totalPayments->where('status', 'failed');

        return [
            'total_invoices_with_payments' => $invoicesWithPayments->count(),
            'total_payments' => $totalPayments->count(),
            'completed_payments' => $completedPayments->count(),
            'pending_payments' => $pendingPayments->count(),
            'failed_payments' => $failedPayments->count(),
            'total_amount_received' => $completedPayments->sum('amount'),
            'total_amount_pending' => $pendingPayments->sum('amount'),
            'success_rate' => $totalPayments->count() > 0 ?
                ($completedPayments->count() / $totalPayments->count()) * 100 : 0,
            'average_payment_amount' => $completedPayments->avg('amount') ?? 0,
            'this_month_payments' => $completedPayments->where('paid_at', '>=', now()->startOfMonth())->count(),
            'this_month_amount' => $completedPayments->where('paid_at', '>=', now()->startOfMonth())->sum('amount'),
        ];
    }

    /**
     * Get recent payments for user.
     */
    private function getRecentPayments(int $userId, int $limit = 10): \Illuminate\Support\Collection
    {
        return InvoicePayment::with(['invoice.client'])
            ->whereHas('invoice', function ($query) use ($userId) {
                $query->where('user_id', $userId);
            })
            ->latest('paid_at')
            ->limit($limit)
            ->get();
    }

    /**
     * Export payment data.
     */
    public function export(Request $request)
    {
        $userId = Auth::id();

        $paymentsQuery = InvoicePayment::with(['invoice.client'])
            ->whereHas('invoice', function ($query) use ($userId) {
                $query->where('user_id', $userId);
            });

        // Apply date filters
        if ($request->filled('date_from')) {
            $paymentsQuery->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $paymentsQuery->whereDate('created_at', '<=', $request->date_to);
        }

        $payments = $paymentsQuery->get();

        // Generate CSV
        $filename = 'payment-tracking-' . now()->format('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($payments) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Payment ID',
                'Invoice Number',
                'Client Name',
                'Amount',
                'Currency',
                'Gateway',
                'Status',
                'Payment Date',
                'Created Date'
            ]);

            // CSV data
            foreach ($payments as $payment) {
                fputcsv($file, [
                    $payment->payment_id,
                    $payment->invoice->invoice_number,
                    $payment->invoice->client->name,
                    $payment->amount,
                    $payment->currency,
                    $payment->gateway,
                    $payment->status,
                    $payment->paid_at ? $payment->paid_at->format('Y-m-d H:i:s') : '',
                    $payment->created_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
