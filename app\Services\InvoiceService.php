<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\TdsRecord;
use App\Models\Client;
use App\Models\User;
use App\Repositories\InvoiceRepository;
use App\Traits\HasCalculations;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Exception;

class InvoiceService
{
    use HasCalculations;

    protected InvoiceRepository $invoiceRepository;

    public function __construct(InvoiceRepository $invoiceRepository)
    {
        $this->invoiceRepository = $invoiceRepository;
    }

    /**
     * Create a new invoice
     */
    public function createInvoice(array $data): Invoice
    {
        return DB::transaction(function () use ($data) {
            $userId = Auth::id();
            if (!$userId) {
                throw new \Exception('User must be authenticated to create invoice');
            }

            $client = Client::where('id', $data['client_id'])
                ->where('user_id', $userId)
                ->firstOrFail();

            // Calculate totals
            $calculations = $this->calculateInvoiceTotals(
                $data['items'],
                $data['tax_percentage'] ?? 0,
                $data['tds_percentage'] ?? $client->default_tds_percentage ?? 0
            );

            // Create invoice
            $invoice = Invoice::create([
                'user_id' => $userId,
                'business_id' => $data['business_id'] ?? null,
                'client_id' => $data['client_id'],
                'invoice_number' => Invoice::generateInvoiceNumber(),
                'invoice_date' => $data['invoice_date'],
                'due_date' => $data['due_date'],
                'notes' => $data['notes'] ?? null,
                'status' => 'draft',
                ...$calculations
            ]);

            // Generate client portal tokens
            $invoice->generatePublicToken();
            $invoice->generatePaymentToken();

            // Create invoice items
            $this->createInvoiceItems($invoice, $data['items']);

            // Create TDS record if applicable
            if ($calculations['tds_amount'] > 0) {
                $this->createTdsRecord($invoice, $client, $calculations);
            }

            return $invoice->load(['client', 'items']);
        });
    }

    /**
     * Update an existing invoice
     */
    public function updateInvoice(Invoice $invoice, array $data): Invoice
    {
        return DB::transaction(function () use ($invoice, $data) {
            $client = Client::findOrFail($data['client_id']);

            // Calculate totals
            $calculations = $this->calculateInvoiceTotals(
                $data['items'],
                $data['tax_percentage'] ?? 0,
                $data['tds_percentage'] ?? $client->default_tds_percentage ?? 0
            );

            // Update invoice
            $invoice->update([
                'client_id' => $data['client_id'],
                'invoice_date' => $data['invoice_date'],
                'due_date' => $data['due_date'],
                'notes' => $data['notes'] ?? null,
                ...$calculations
            ]);

            // Delete existing items and create new ones
            $invoice->items()->delete();
            $this->createInvoiceItems($invoice, $data['items']);

            // Update TDS record
            $this->updateTdsRecord($invoice, $client, $calculations);

            return $invoice->load(['client', 'items']);
        });
    }

    /**
     * Mark invoice as sent
     */
    public function markAsSent(Invoice $invoice): Invoice
    {
        $invoice->update([
            'status' => 'pending',
            'sent_date' => now(),
        ]);

        return $invoice;
    }

    /**
     * Mark invoice as paid
     */
    public function markAsPaid(Invoice $invoice): Invoice
    {
        $invoice->update([
            'status' => 'paid',
            'paid_date' => now(),
        ]);

        return $invoice;
    }

    /**
     * Mark invoice as cancelled
     */
    public function markAsCancelled(Invoice $invoice): Invoice
    {
        $invoice->update(['status' => 'cancelled']);
        return $invoice;
    }

    /**
     * Duplicate an invoice
     */
    public function duplicateInvoice(Invoice $invoice): Invoice
    {
        return DB::transaction(function () use ($invoice) {
            $newInvoice = $invoice->replicate();
            $newInvoice->invoice_number = Invoice::generateInvoiceNumber();
            $newInvoice->status = 'draft';
            $newInvoice->sent_date = null;
            $newInvoice->paid_date = null;
            $newInvoice->save();

            // Duplicate items
            foreach ($invoice->items as $item) {
                $newItem = $item->replicate();
                $newItem->invoice_id = $newInvoice->id;
                $newItem->save();
            }

            return $newInvoice->load(['client', 'items']);
        });
    }

    /**
     * Get invoice statistics for user
     */
    public function getInvoiceStats(int $userId): array
    {
        return $this->invoiceRepository->getStatsForUser($userId);
    }

    /**
     * Get monthly revenue data
     */
    public function getMonthlyRevenue(int $userId, int $months = 12): array
    {
        $data = $this->invoiceRepository->getMonthlyRevenueForUser($userId, $months);
        
        return $data->map(function ($item) {
            return [
                'month' => sprintf('%04d-%02d', $item->year, $item->month),
                'revenue' => $item->revenue,
                'count' => $item->count,
            ];
        })->toArray();
    }

    /**
     * Check if user can create invoice based on plan limits
     */
    public function canCreateInvoice(int $userId): bool
    {
        $user = User::find($userId);
        return PlanChecker::canCreateInvoice($user);
    }

    /**
     * Get current month usage for user
     */
    public function getCurrentMonthUsage(int $userId): array
    {
        $count = $this->invoiceRepository->getCurrentMonthCountForUser($userId);
        $limit = 3; // Free plan limit

        return [
            'used' => $count,
            'limit' => $limit,
            'remaining' => max(0, $limit - $count),
            'percentage' => $limit > 0 ? ($count / $limit) * 100 : 0,
        ];
    }

    /**
     * Get invoices for user with filters
     */
    public function getInvoicesForUser(int $userId, $request = null)
    {
        return $this->invoiceRepository->getForUser($userId, $request);
    }

    /**
     * Create invoice items
     */
    private function createInvoiceItems(Invoice $invoice, array $items): void
    {
        foreach ($items as $item) {
            InvoiceItem::create([
                'invoice_id' => $invoice->id,
                'description' => $item['description'],
                'quantity' => $item['quantity'],
                'rate' => $item['rate'],
                'amount' => $item['quantity'] * $item['rate'],
            ]);
        }
    }

    /**
     * Create TDS record
     */
    private function createTdsRecord(Invoice $invoice, Client $client, array $calculations): void
    {
        TdsRecord::create([
            'user_id' => $invoice->user_id,
            'client_id' => $client->id,
            'invoice_id' => $invoice->id,
            'financial_year' => TdsRecord::getCurrentFinancialYear(),
            'invoice_amount' => $calculations['subtotal'],
            'tds_percentage' => $calculations['tds_percentage'],
            'tds_amount' => $calculations['tds_amount'],
            'net_received' => $calculations['net_amount'],
            'deduction_date' => $invoice->invoice_date,
        ]);
    }

    /**
     * Update TDS record
     */
    private function updateTdsRecord(Invoice $invoice, Client $client, array $calculations): void
    {
        // Delete existing TDS record
        $invoice->tdsRecord()->delete();

        // Create new TDS record if applicable
        if ($calculations['tds_amount'] > 0) {
            $this->createTdsRecord($invoice, $client, $calculations);
        }
    }

    /**
     * Send invoice to client via email
     */
    public function sendInvoiceToClient(Invoice $invoice): bool
    {
        try {
            // Ensure tokens are generated
            if (!$invoice->public_token) {
                $invoice->generatePublicToken();
            }
            if (!$invoice->payment_token) {
                $invoice->generatePaymentToken();
            }

            // Update invoice status to 'sent' if it's currently 'draft'
            if ($invoice->status === 'draft') {
                $invoice->update(['status' => 'sent']);
            }

            // Send email notification
            \Illuminate\Support\Facades\Mail::to($invoice->client->email)
                ->send(new \App\Mail\InvoiceCreatedMail($invoice));

            \Illuminate\Support\Facades\Log::info('Invoice email sent', [
                'invoice_id' => $invoice->id,
                'invoice_number' => $invoice->invoice_number,
                'client_email' => $invoice->client->email,
                'public_url' => $invoice->public_url,
                'payment_url' => $invoice->payment_url,
            ]);

            return true;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Failed to send invoice email', [
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send payment reminder to client
     */
    public function sendPaymentReminder(Invoice $invoice): bool
    {
        try {
            // Only send reminders for unpaid invoices
            if ($invoice->status === 'paid') {
                return false;
            }

            // Ensure payment token is generated
            if (!$invoice->payment_token) {
                $invoice->generatePaymentToken();
            }

            // Send payment reminder email
            \Illuminate\Support\Facades\Mail::to($invoice->client->email)
                ->send(new \App\Mail\PaymentReminderMail($invoice));

            \Illuminate\Support\Facades\Log::info('Payment reminder sent', [
                'invoice_id' => $invoice->id,
                'invoice_number' => $invoice->invoice_number,
                'client_email' => $invoice->client->email,
                'payment_url' => $invoice->payment_url,
                'days_overdue' => $invoice->due_date->diffInDays(now(), false),
            ]);

            return true;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Failed to send payment reminder', [
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get payment summary for invoice
     */
    public function getPaymentSummary(Invoice $invoice): array
    {
        $completedPayments = $invoice->completedPayments;
        $totalPaid = $completedPayments->sum('amount');
        $remainingAmount = max(0, $invoice->total_amount - $totalPaid);

        return [
            'total_amount' => $invoice->total_amount,
            'total_paid' => $totalPaid,
            'remaining_amount' => $remainingAmount,
            'is_fully_paid' => $remainingAmount <= 0.01,
            'payment_count' => $completedPayments->count(),
            'last_payment_date' => $completedPayments->max('paid_at'),
            'payments' => $completedPayments->map(function ($payment) {
                return [
                    'id' => $payment->id,
                    'amount' => $payment->amount,
                    'gateway' => $payment->gateway,
                    'paid_at' => $payment->paid_at,
                    'formatted_amount' => $payment->formatted_amount,
                ];
            }),
        ];
    }
}
