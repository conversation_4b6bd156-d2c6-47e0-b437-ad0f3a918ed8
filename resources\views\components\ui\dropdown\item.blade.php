@props([
    'href' => null,
    'icon' => null,
    'disabled' => false,
    'danger' => false
])

@php
    $baseClasses = 'flex items-center px-4 py-3 text-sm transition-colors duration-200';
    
    if ($disabled) {
        $classes = $baseClasses . ' text-secondary-400 cursor-not-allowed';
    } elseif ($danger) {
        $classes = $baseClasses . ' text-danger-600 hover:bg-danger-50 hover:text-danger-700';
    } else {
        $classes = $baseClasses . ' text-secondary-700 hover:bg-secondary-50 hover:text-secondary-900';
    }
@endphp

@if($href && !$disabled)
    <a href="{{ $href }}" {{ $attributes->merge(['class' => $classes]) }}>
        @if($icon)
            <i class="{{ $icon }} mr-3 text-sm"></i>
        @endif
        {{ $slot }}
    </a>
@else
    <div {{ $attributes->merge(['class' => $classes]) }}>
        @if($icon)
            <i class="{{ $icon }} mr-3 text-sm"></i>
        @endif
        {{ $slot }}
    </div>
@endif
