<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TdsRecord extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'client_id',
        'invoice_id',
        'financial_year',
        'invoice_amount',
        'tds_percentage',
        'tds_amount',
        'net_received',
        'deduction_date',
        'tds_certificate_number',
        'is_auto_calculated',
        'calculation_method',
        'automation_metadata',
        'suggested_tds_rate',
        'rate_justification',
        'compliance_status',
        'compliance_checks',
        'compliance_verified_at',
        'verified_by',
        'certificate_status',
        'certificate_requested_at',
        'certificate_received_at',
        'certificate_file_path',
        'ai_insights',
        'confidence_score',
    ];

    protected function casts(): array
    {
        return [
            'invoice_amount' => 'decimal:2',
            'tds_percentage' => 'decimal:2',
            'tds_amount' => 'decimal:2',
            'net_received' => 'decimal:2',
            'suggested_tds_rate' => 'decimal:2',
            'confidence_score' => 'decimal:2',
            'deduction_date' => 'date',
            'compliance_verified_at' => 'datetime',
            'certificate_requested_at' => 'datetime',
            'certificate_received_at' => 'datetime',
            'is_auto_calculated' => 'boolean',
            'automation_metadata' => 'array',
            'compliance_checks' => 'array',
            'ai_insights' => 'array',
        ];
    }

    /**
     * Get the user that owns the TDS record.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the client for the TDS record.
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Get the invoice for the TDS record.
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }



    /**
     * Scope a query to filter by financial year.
     */
    public function scopeForFinancialYear($query, $year)
    {
        return $query->where('financial_year', $year);
    }

    /**
     * Get current financial year.
     */
    public static function getCurrentFinancialYear(): string
    {
        $currentDate = now();
        $year = $currentDate->year;

        if ($currentDate->month >= 4) {
            return $year . '-' . ($year + 1);
        } else {
            return ($year - 1) . '-' . $year;
        }
    }

    /**
     * Get the user who verified compliance.
     */
    public function verifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    /**
     * Scope to get auto-calculated records.
     */
    public function scopeAutoCalculated($query)
    {
        return $query->where('is_auto_calculated', true);
    }

    /**
     * Scope to get manually calculated records.
     */
    public function scopeManuallyCalculated($query)
    {
        return $query->where('is_auto_calculated', false);
    }

    /**
     * Scope to get compliant records.
     */
    public function scopeCompliant($query)
    {
        return $query->where('compliance_status', 'compliant');
    }

    /**
     * Scope to get non-compliant records.
     */
    public function scopeNonCompliant($query)
    {
        return $query->where('compliance_status', 'non_compliant');
    }

    /**
     * Scope to get records with missing certificates.
     */
    public function scopeMissingCertificates($query)
    {
        return $query->where('certificate_status', 'missing')
                    ->orWhere('certificate_status', 'pending');
    }

    /**
     * Scope to get records with received certificates.
     */
    public function scopeWithCertificates($query)
    {
        return $query->where('certificate_status', 'received');
    }

    /**
     * Mark as auto-calculated.
     */
    public function markAsAutoCalculated(string $method, array $metadata = []): void
    {
        $this->update([
            'is_auto_calculated' => true,
            'calculation_method' => $method,
            'automation_metadata' => $metadata,
        ]);
    }

    /**
     * Update compliance status.
     */
    public function updateComplianceStatus(string $status, array $checks = [], int $verifiedBy = null): void
    {
        $updateData = [
            'compliance_status' => $status,
            'compliance_checks' => $checks,
        ];

        if ($status === 'compliant' || $status === 'non_compliant') {
            $updateData['compliance_verified_at'] = now();
            $updateData['verified_by'] = $verifiedBy;
        }

        $this->update($updateData);
    }

    /**
     * Update certificate status.
     */
    public function updateCertificateStatus(string $status, string $certificateNumber = null, string $filePath = null): void
    {
        $updateData = ['certificate_status' => $status];

        if ($status === 'requested') {
            $updateData['certificate_requested_at'] = now();
        } elseif ($status === 'received') {
            $updateData['certificate_received_at'] = now();
            if ($certificateNumber) {
                $updateData['tds_certificate_number'] = $certificateNumber;
            }
            if ($filePath) {
                $updateData['certificate_file_path'] = $filePath;
            }
        }

        $this->update($updateData);
    }

    /**
     * Add AI insights.
     */
    public function addAIInsights(array $insights, float $confidenceScore = null): void
    {
        $updateData = ['ai_insights' => $insights];

        if ($confidenceScore !== null) {
            $updateData['confidence_score'] = $confidenceScore;
        }

        $this->update($updateData);
    }

    /**
     * Check if TDS rate is within acceptable range.
     */
    public function isRateAcceptable(): bool
    {
        $acceptableRates = [0, 1, 2, 5, 10, 20];
        return in_array($this->tds_percentage, $acceptableRates);
    }

    /**
     * Check if certificate is overdue.
     */
    public function isCertificateOverdue(): bool
    {
        if ($this->certificate_status === 'received') {
            return false;
        }

        // Certificate should be received within 30 days of deduction
        $overdueDate = $this->deduction_date->addDays(30);
        return now()->isAfter($overdueDate);
    }

    /**
     * Get compliance score.
     */
    public function getComplianceScore(): int
    {
        $score = 0;

        // Rate compliance (40 points)
        if ($this->isRateAcceptable()) {
            $score += 40;
        }

        // Certificate compliance (30 points)
        if ($this->certificate_status === 'received') {
            $score += 30;
        } elseif (!$this->isCertificateOverdue()) {
            $score += 15;
        }

        // Verification compliance (20 points)
        if ($this->compliance_status === 'compliant') {
            $score += 20;
        } elseif ($this->compliance_status === 'reviewed') {
            $score += 10;
        }

        // Automation compliance (10 points)
        if ($this->is_auto_calculated) {
            $score += 10;
        }

        return $score;
    }

    /**
     * Get status badge color.
     */
    public function getStatusBadgeColor(): string
    {
        return match ($this->compliance_status) {
            'compliant' => 'green',
            'non_compliant' => 'red',
            'reviewed' => 'blue',
            'pending' => 'yellow',
            default => 'gray',
        };
    }

    /**
     * Get certificate status badge color.
     */
    public function getCertificateStatusBadgeColor(): string
    {
        return match ($this->certificate_status) {
            'received' => 'green',
            'requested' => 'blue',
            'missing' => 'red',
            'pending' => 'yellow',
            default => 'gray',
        };
    }
}
