<?php

use App\Http\Controllers\Admin\AdminAuthController;
use App\Http\Controllers\Admin\AdminDashboardController;
use App\Http\Controllers\Admin\PlanController;
use App\Http\Controllers\Admin\UserManagementController;
use App\Http\Controllers\Admin\FileManagementController;
use App\Http\Controllers\Admin\AISettingsController;
use App\Http\Controllers\Admin\AutomationMonitoringController;
use App\Http\Controllers\Admin\SubscriptionManagementController;
use App\Http\Controllers\Admin\SiteSettingsController;
use App\Http\Controllers\Admin\ExpenseCategoryController;
use Illuminate\Support\Facades\Route;

// Admin Authentication Routes (Guest only)
Route::middleware('guest')->prefix('admin')->name('admin.')->group(function () {
    Route::get('login', [AdminAuthController::class, 'showLoginForm'])->name('login');
    Route::post('login', [AdminAuthController::class, 'login']);
});

// Admin Protected Routes
Route::middleware(['auth', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {
    // Admin Dashboard
    Route::get('/', [AdminDashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard', [AdminDashboardController::class, 'index']);
    Route::get('/dashboard/data', [AdminDashboardController::class, 'getData'])->name('dashboard.data');
    
    // User Management
    Route::resource('users', UserManagementController::class);

    // Plan Management
    Route::resource('plans', PlanController::class);
    Route::patch('plans/{plan}/toggle-status', [PlanController::class, 'toggleStatus'])->name('plans.toggle-status');

    // Subscription Management
    Route::resource('subscriptions', SubscriptionManagementController::class)->only(['index', 'show']);
    Route::patch('subscriptions/{subscription}/cancel', [SubscriptionManagementController::class, 'cancel'])->name('subscriptions.cancel');
    Route::patch('subscriptions/{subscription}/reactivate', [SubscriptionManagementController::class, 'reactivate'])->name('subscriptions.reactivate');
    Route::patch('subscriptions/{subscription}/change-plan', [SubscriptionManagementController::class, 'changePlan'])->name('subscriptions.change-plan');
    Route::get('subscriptions/{subscription}/payments', [SubscriptionManagementController::class, 'payments'])->name('subscriptions.payments');
    Route::get('subscriptions/export', [SubscriptionManagementController::class, 'export'])->name('subscriptions.export');

    // File Management
    Route::resource('files', FileManagementController::class);
    Route::get('files/{file}/download', [FileManagementController::class, 'download'])->name('files.download');
    Route::get('files/stats', [FileManagementController::class, 'stats'])->name('files.stats');

    // Expense Category Management
    Route::resource('expense-categories', ExpenseCategoryController::class);
    Route::patch('expense-categories/{expenseCategory}/toggle-status', [ExpenseCategoryController::class, 'toggleStatus'])->name('expense-categories.toggle-status');
    Route::post('expense-categories/update-sort-order', [ExpenseCategoryController::class, 'updateSortOrder'])->name('expense-categories.update-sort-order');

    // Site Settings
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/', [SiteSettingsController::class, 'index'])->name('index');
        Route::post('/update', [SiteSettingsController::class, 'update'])->name('update');
        Route::post('/reset', [SiteSettingsController::class, 'reset'])->name('reset');
        Route::get('/export', [SiteSettingsController::class, 'export'])->name('export');
        Route::post('/import', [SiteSettingsController::class, 'import'])->name('import');
        Route::post('/test-email', [SiteSettingsController::class, 'testEmail'])->name('test-email');
    });

    // Automation Monitoring
    Route::prefix('automation')->name('automation.')->group(function () {
        Route::get('/dashboard', [AutomationMonitoringController::class, 'dashboard'])->name('dashboard');
        Route::get('/queue-data', [AutomationMonitoringController::class, 'getQueueData'])->name('queue-data');
        Route::post('/retry-failed-jobs', [AutomationMonitoringController::class, 'retryFailedJobs'])->name('retry-failed-jobs');
        Route::post('/clear-failed-jobs', [AutomationMonitoringController::class, 'clearFailedJobs'])->name('clear-failed-jobs');
        Route::get('/logs', [AutomationMonitoringController::class, 'getLogs'])->name('logs');
    });

    // Admin Logout
    Route::post('logout', [AdminAuthController::class, 'logout'])->name('logout');
});

// AI Settings Routes
Route::middleware(['auth', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {
    // AI Settings
    Route::get('ai-settings', [AISettingsController::class, 'index'])->name('ai-settings');
    Route::post('ai-settings/update-provider', [AISettingsController::class, 'updateProvider'])->name('ai-settings.update-provider');
    Route::post('ai-settings/test-provider', [AISettingsController::class, 'testProvider'])->name('ai-settings.test-provider');
});
