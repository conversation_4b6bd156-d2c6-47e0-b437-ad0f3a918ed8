<?php

namespace App\Services;

use App\Models\SiteSetting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;

class SettingsService
{
    /**
     * Cache duration in seconds (1 hour)
     */
    private const CACHE_DURATION = 3600;

    /**
     * Get a setting value with fallback to environment
     */
    public static function get(string $key, $default = null)
    {
        return Cache::remember("setting_{$key}", self::CACHE_DURATION, function () use ($key, $default) {
            // Try to get from database first
            $setting = SiteSetting::where('key', $key)->first();
            
            if ($setting) {
                return $setting->getTypedValue();
            }
            
            // Fallback to environment variable
            $envKey = strtoupper(str_replace('.', '_', $key));
            $envValue = env($envKey);
            
            if ($envValue !== null) {
                return $envValue;
            }
            
            // Return default value
            return $default;
        });
    }

    /**
     * Set a setting value
     */
    public static function set(string $key, $value, array $attributes = []): SiteSetting
    {
        $setting = SiteSetting::updateOrCreate(
            ['key' => $key],
            array_merge($attributes, [
                'value' => is_array($value) || is_object($value) ? json_encode($value) : $value
            ])
        );

        // Clear cache
        Cache::forget("setting_{$key}");
        
        return $setting;
    }

    /**
     * Get all settings by category
     */
    public static function getByCategory(string $category): array
    {
        return SiteSetting::getByCategory($category);
    }

    /**
     * Get all public settings (for frontend use)
     */
    public static function getPublicSettings(): array
    {
        return SiteSetting::getPublicSettings();
    }

    /**
     * Get site configuration settings
     */
    public static function getSiteConfig(): array
    {
        return Cache::remember('site_config', self::CACHE_DURATION, function () {
            return [
                'name' => self::get('site.name', config('app.name')),
                'description' => self::get('site.description', 'Run Your Freelance Business Like a Pro'),
                'timezone' => self::get('site.timezone', config('app.timezone')),
                'locale' => self::get('site.locale', config('app.locale')),
            ];
        });
    }

    /**
     * Get branding configuration
     */
    public static function getBrandingConfig(): array
    {
        return Cache::remember('branding_config', self::CACHE_DURATION, function () {
            return [
                'primary_color' => self::get('brand.primary_color', '#10B981'),
                'secondary_color' => self::get('brand.secondary_color', '#059669'),
                'logo_url' => self::get('brand.logo_url', ''),
                'favicon_url' => self::get('brand.favicon_url', ''),
                'font_family' => self::get('brand.font_family', 'Inter'),
            ];
        });
    }

    /**
     * Get currency configuration
     */
    public static function getCurrencyConfig(): array
    {
        return Cache::remember('currency_config', self::CACHE_DURATION, function () {
            return [
                'code' => self::get('currency.code', 'USD'),
                'symbol' => self::get('currency.symbol', '$'),
                'position' => self::get('currency.position', 'before'), // before or after
                'decimal_places' => self::get('currency.decimal_places', 2),
                'thousands_separator' => self::get('currency.thousands_separator', ','),
                'decimal_separator' => self::get('currency.decimal_separator', '.'),
            ];
        });
    }

    /**
     * Get email configuration
     */
    public static function getEmailConfig(): array
    {
        return Cache::remember('email_config', self::CACHE_DURATION, function () {
            return [
                'from_name' => self::get('mail.from_name', config('mail.from.name')),
                'from_address' => self::get('mail.from_address', config('mail.from.address')),
                'reply_to' => self::get('mail.reply_to', ''),
                'footer_text' => self::get('mail.footer_text', ''),
                'signature' => self::get('mail.signature', ''),
            ];
        });
    }

    /**
     * Get payment gateway configuration
     */
    public static function getPaymentConfig(): array
    {
        return Cache::remember('payment_config', self::CACHE_DURATION, function () {
            return [
                'paypal_enabled' => self::get('payment.paypal_enabled', true),
                'razorpay_enabled' => self::get('payment.razorpay_enabled', false),
                'stripe_enabled' => self::get('payment.stripe_enabled', false),
                'default_gateway' => self::get('payment.default_gateway', 'paypal'),
                'currency_conversion' => self::get('payment.currency_conversion', false),
            ];
        });
    }

    /**
     * Get feature toggles
     */
    public static function getFeatureConfig(): array
    {
        return Cache::remember('feature_config', self::CACHE_DURATION, function () {
            return [
                'ai_features_enabled' => self::get('features.ai_enabled', true),
                'automation_enabled' => self::get('features.automation_enabled', true),
                'time_tracking_enabled' => self::get('features.time_tracking_enabled', true),
                'project_management_enabled' => self::get('features.project_management_enabled', true),
                'client_portal_enabled' => self::get('features.client_portal_enabled', false),
                'multi_currency_enabled' => self::get('features.multi_currency_enabled', false),
                'recurring_invoices_enabled' => self::get('features.recurring_invoices_enabled', true),
                'expense_tracking_enabled' => self::get('features.expense_tracking_enabled', true),
            ];
        });
    }

    /**
     * Get security configuration
     */
    public static function getSecurityConfig(): array
    {
        return Cache::remember('security_config', self::CACHE_DURATION, function () {
            return [
                'two_factor_enabled' => self::get('security.two_factor_enabled', false),
                'session_timeout' => self::get('security.session_timeout', 120),
                'password_expiry_days' => self::get('security.password_expiry_days', 90),
                'max_login_attempts' => self::get('security.max_login_attempts', 5),
                'lockout_duration' => self::get('security.lockout_duration', 15),
            ];
        });
    }

    /**
     * Format currency value
     */
    public static function formatCurrency(float $amount): string
    {
        $config = self::getCurrencyConfig();
        
        $formatted = number_format(
            $amount,
            $config['decimal_places'],
            $config['decimal_separator'],
            $config['thousands_separator']
        );
        
        return $config['position'] === 'before' 
            ? $config['symbol'] . $formatted
            : $formatted . $config['symbol'];
    }

    /**
     * Check if a feature is enabled
     */
    public static function isFeatureEnabled(string $feature): bool
    {
        $features = self::getFeatureConfig();
        return $features[$feature] ?? false;
    }

    /**
     * Clear all settings cache
     */
    public static function clearCache(): void
    {
        $cacheKeys = [
            'site_config',
            'branding_config',
            'currency_config',
            'email_config',
            'payment_config',
            'feature_config',
            'security_config',
        ];
        
        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }
        
        // Clear individual setting caches
        SiteSetting::clearCache();
    }

    /**
     * Refresh configuration in Laravel config
     */
    public static function refreshConfig(): void
    {
        $emailConfig = self::getEmailConfig();
        $currencyConfig = self::getCurrencyConfig();
        
        // Update mail configuration
        Config::set('mail.from.name', $emailConfig['from_name']);
        Config::set('mail.from.address', $emailConfig['from_address']);
        
        // Update currency configuration
        Config::set('services.currency.code', $currencyConfig['code']);
        Config::set('services.currency.symbol', $currencyConfig['symbol']);
    }

    /**
     * Get all settings for export
     */
    public static function exportSettings(string $category = null): array
    {
        $query = SiteSetting::query();
        
        if ($category) {
            $query->where('category', $category);
        }
        
        return $query->get()->map(function ($setting) {
            return [
                'key' => $setting->key,
                'value' => $setting->getTypedValue(),
                'type' => $setting->type,
                'category' => $setting->category,
                'label' => $setting->label,
                'description' => $setting->description,
                'is_public' => $setting->is_public,
                'is_required' => $setting->is_required,
                'sort_order' => $setting->sort_order,
            ];
        })->toArray();
    }
}
