<x-guest-layout>
    <div class="text-center mb-8">
        <h2 class="text-3xl font-bold text-secondary-900 mb-2">Welcome back</h2>
        <p class="text-secondary-600">Sign in to your account to continue</p>
    </div>

    <!-- Session Status -->
    @if(session('status'))
        <x-ui.alert type="success" class="mb-6" dismissible>
            {{ session('status') }}
        </x-ui.alert>
    @endif

    <form method="POST" action="{{ route('login') }}" class="space-y-6">
        @csrf

        <!-- Email Address -->
        <div>
            <x-ui.input
                type="email"
                name="email"
                label="Email Address"
                :value="old('email')"
                required
                autofocus
                autocomplete="username"
                icon="fas fa-envelope"
                :error="$errors->first('email')"
                placeholder="Enter your email address"
            />
        </div>

        <!-- Password -->
        <div>
            <x-ui.input
                type="password"
                name="password"
                label="Password"
                required
                autocomplete="current-password"
                icon="fas fa-lock"
                :error="$errors->first('password')"
                placeholder="Enter your password"
            />
        </div>

        <!-- Remember Me & Forgot Password -->
        <div class="flex items-center justify-between">
            <label for="remember_me" class="inline-flex items-center">
                <input id="remember_me" type="checkbox" class="rounded border-secondary-300 text-primary-600 shadow-sm focus:ring-primary-500 focus:ring-offset-0" name="remember">
                <span class="ml-2 text-sm text-secondary-600">Remember me</span>
            </label>

            @if (Route::has('password.request'))
                <a class="text-sm text-primary-600 hover:text-primary-700 font-medium focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-lg px-2 py-1" href="{{ route('password.request') }}">
                    Forgot password?
                </a>
            @endif
        </div>

        <!-- Login Button -->
        <div class="space-y-4">
            <x-ui.button
                type="submit"
                variant="primary"
                size="lg"
                class="w-full justify-center"
                icon="fas fa-sign-in-alt"
            >
                Sign In
            </x-ui.button>
        </div>
    </form>

    <!-- Divider -->
    <div class="mt-8 mb-6">
        <div class="relative">
            <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-secondary-200"></div>
            </div>
            <div class="relative flex justify-center text-sm">
                <span class="px-4 bg-white text-secondary-500">Don't have an account?</span>
            </div>
        </div>
    </div>

    <!-- Register Link -->
    <div class="text-center">
        <a href="{{ route('register') }}" class="btn btn-outline w-full justify-center">
            <i class="fas fa-user-plus mr-2"></i>
            Create Account
        </a>
    </div>
</x-guest-layout>
