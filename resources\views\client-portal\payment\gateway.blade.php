@extends('client-portal.layout')

@section('content')
<div class="space-y-6">
    <!-- Back to Invoice -->
    <div>
        <a href="{{ route('client-portal.invoice.view', ['invoice' => $invoice->id, 'token' => $invoice->public_token]) }}" 
           class="inline-flex items-center text-sm text-gray-500 hover:text-gray-700">
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Invoice
        </a>
    </div>

    <!-- Payment Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="text-center">
            <h2 class="text-2xl font-bold text-gray-900">Make Payment</h2>
            <p class="mt-2 text-gray-600">Invoice {{ $invoice->invoice_number }}</p>
            <div class="mt-4">
                <span class="text-3xl font-bold text-emerald-600">
                    {{ config('services.currency.symbol', '₹') }}{{ number_format($paymentSummary['remaining_amount'], 2) }}
                </span>
                <p class="text-sm text-gray-500 mt-1">Amount to pay</p>
            </div>
        </div>
    </div>

    <!-- Payment Amount Selection -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Payment Amount</h3>
        <div class="space-y-4">
            <div class="flex items-center space-x-4">
                <input type="radio" id="full_amount" name="payment_type" value="full" checked 
                       class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300">
                <label for="full_amount" class="flex-1 cursor-pointer">
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-medium text-gray-900">Pay Full Amount</span>
                        <span class="text-sm font-bold text-gray-900">
                            {{ config('services.currency.symbol', '₹') }}{{ number_format($paymentSummary['remaining_amount'], 2) }}
                        </span>
                    </div>
                    <p class="text-xs text-gray-500">Complete payment for this invoice</p>
                </label>
            </div>
            
            <div class="flex items-center space-x-4">
                <input type="radio" id="partial_amount" name="payment_type" value="partial" 
                       class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300">
                <label for="partial_amount" class="flex-1 cursor-pointer">
                    <span class="text-sm font-medium text-gray-900">Pay Partial Amount</span>
                    <p class="text-xs text-gray-500">Pay a custom amount</p>
                </label>
            </div>
            
            <div id="custom_amount_section" class="ml-8 hidden">
                <div class="flex items-center space-x-2">
                    <span class="text-gray-500">{{ config('services.currency.symbol', '₹') }}</span>
                    <input type="number" id="custom_amount" name="custom_amount" 
                           min="1" max="{{ $paymentSummary['remaining_amount'] }}" step="0.01"
                           class="block w-32 rounded-md border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500 sm:text-sm"
                           placeholder="0.00">
                </div>
                <p class="text-xs text-gray-500 mt-1">
                    Minimum: {{ config('services.currency.symbol', '₹') }}1.00, 
                    Maximum: {{ config('services.currency.symbol', '₹') }}{{ number_format($paymentSummary['remaining_amount'], 2) }}
                </p>
            </div>
        </div>
    </div>

    <!-- Payment Gateway Selection -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Select Payment Method</h3>
        
        @if($isInternational)
            <!-- International Version - PayPal Only -->
            <form id="payment-form" method="POST" action="{{ route('client-portal.payment.paypal', ['invoice' => $invoice->id, 'token' => $invoice->payment_token]) }}">
                @csrf
                <input type="hidden" name="amount" id="payment_amount" value="{{ $paymentSummary['remaining_amount'] }}">
                
                <div class="space-y-4">
                    <div class="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors cursor-pointer bg-blue-50">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-8 bg-blue-600 rounded flex items-center justify-center">
                                    <i class="fab fa-paypal text-white text-lg"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-900">PayPal</h4>
                                    <p class="text-sm text-gray-500">Pay securely with PayPal</p>
                                </div>
                            </div>
                            <div class="text-blue-600">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6">
                    <button type="submit" 
                            class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fab fa-paypal mr-2"></i>
                        Continue with PayPal
                    </button>
                </div>
            </form>
        @else
            <!-- Indian Version - PayPal and Razorpay -->
            <div class="space-y-4">
                <!-- PayPal Option -->
                <form method="POST" action="{{ route('client-portal.payment.paypal', ['invoice' => $invoice->id, 'token' => $invoice->payment_token]) }}" class="payment-form">
                    @csrf
                    <input type="hidden" name="amount" class="payment_amount" value="{{ $paymentSummary['remaining_amount'] }}">
                    
                    <div class="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-8 bg-blue-600 rounded flex items-center justify-center">
                                    <i class="fab fa-paypal text-white text-lg"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-900">PayPal</h4>
                                    <p class="text-sm text-gray-500">Pay with PayPal account or credit card</p>
                                </div>
                            </div>
                            <button type="submit" 
                                    class="px-4 py-2 border border-blue-600 text-blue-600 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                Select
                            </button>
                        </div>
                    </div>
                </form>

                <!-- Razorpay Option -->
                <form method="POST" action="{{ route('client-portal.payment.razorpay', ['invoice' => $invoice->id, 'token' => $invoice->payment_token]) }}" class="payment-form">
                    @csrf
                    <input type="hidden" name="amount" class="payment_amount" value="{{ $paymentSummary['remaining_amount'] }}">
                    
                    <div class="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-8 bg-blue-500 rounded flex items-center justify-center">
                                    <i class="fas fa-credit-card text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-900">Razorpay</h4>
                                    <p class="text-sm text-gray-500">UPI, Cards, Net Banking & Wallets</p>
                                </div>
                            </div>
                            <button type="submit" 
                                    class="px-4 py-2 border border-blue-600 text-blue-600 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                Select
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        @endif
    </div>

    <!-- Security Notice -->
    <div class="bg-gray-50 rounded-lg p-4">
        <div class="flex items-start space-x-3">
            <div class="flex-shrink-0">
                <i class="fas fa-shield-alt text-emerald-600 text-lg"></i>
            </div>
            <div>
                <h4 class="text-sm font-medium text-gray-900">Secure Payment</h4>
                <p class="text-sm text-gray-600 mt-1">
                    Your payment information is encrypted and secure. We do not store your payment details.
                </p>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const fullAmountRadio = document.getElementById('full_amount');
    const partialAmountRadio = document.getElementById('partial_amount');
    const customAmountSection = document.getElementById('custom_amount_section');
    const customAmountInput = document.getElementById('custom_amount');
    const paymentAmountInputs = document.querySelectorAll('.payment_amount, #payment_amount');
    
    const fullAmount = {{ $paymentSummary['remaining_amount'] }};
    
    // Handle payment type selection
    function updatePaymentType() {
        if (partialAmountRadio.checked) {
            customAmountSection.classList.remove('hidden');
            customAmountInput.focus();
        } else {
            customAmountSection.classList.add('hidden');
            updatePaymentAmount(fullAmount);
        }
    }
    
    // Update payment amount in all forms
    function updatePaymentAmount(amount) {
        paymentAmountInputs.forEach(input => {
            input.value = amount;
        });
    }
    
    // Event listeners
    fullAmountRadio.addEventListener('change', updatePaymentType);
    partialAmountRadio.addEventListener('change', updatePaymentType);
    
    customAmountInput.addEventListener('input', function() {
        const amount = parseFloat(this.value) || 0;
        if (amount > 0 && amount <= fullAmount) {
            updatePaymentAmount(amount);
        }
    });
    
    // Form validation
    document.querySelectorAll('.payment-form, #payment-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            const amount = parseFloat(form.querySelector('.payment_amount, #payment_amount').value);
            
            if (amount <= 0) {
                e.preventDefault();
                alert('Please enter a valid payment amount.');
                return;
            }
            
            if (amount > fullAmount) {
                e.preventDefault();
                alert('Payment amount cannot exceed the remaining invoice amount.');
                return;
            }
            
            if (amount < 1) {
                e.preventDefault();
                alert('Minimum payment amount is ₹1.00');
                return;
            }
        });
    });
});
</script>
@endpush
@endsection
