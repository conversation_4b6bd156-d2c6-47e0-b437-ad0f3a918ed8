<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    {{ $plan->name }}
                </h2>
                <p class="text-sm text-gray-600 mt-1">Plan Details and Subscribers</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('admin.plans.edit', $plan) }}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                    <i class="fas fa-edit mr-2"></i>
                    Edit Plan
                </a>
                <a href="{{ route('admin.plans.index') }}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Plans
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Plan Information -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Basic Info -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex justify-between items-start mb-6">
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">{{ $plan->name }}</h3>
                                <div class="flex items-center space-x-4">
                                    @if($plan->is_popular)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            <i class="fas fa-star mr-1"></i>
                                            Popular
                                        </span>
                                    @endif
                                    @if($plan->is_active)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-check-circle mr-1"></i>
                                            Active
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <i class="fas fa-times-circle mr-1"></i>
                                            Inactive
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-3xl font-bold text-gray-900">
                                    @if($plan->price == 0)
                                        Free
                                    @else
                                        ₹{{ number_format($plan->price) }}
                                    @endif
                                </div>
                                @if($plan->price > 0)
                                    <div class="text-sm text-gray-500">per {{ $plan->billing_cycle }}</div>
                                @endif
                            </div>
                        </div>

                        @if($plan->description)
                            <div class="mb-6">
                                <h4 class="text-sm font-medium text-gray-900 mb-2">Description</h4>
                                <p class="text-gray-700">{{ $plan->description }}</p>
                            </div>
                        @endif

                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">Billing Cycle:</span>
                                <span class="font-medium text-gray-900 ml-2">{{ ucfirst($plan->billing_cycle) }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">Sort Order:</span>
                                <span class="font-medium text-gray-900 ml-2">{{ $plan->sort_order }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">Created:</span>
                                <span class="font-medium text-gray-900 ml-2">{{ $plan->created_at->format('M j, Y') }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">Updated:</span>
                                <span class="font-medium text-gray-900 ml-2">{{ $plan->updated_at->format('M j, Y') }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Plan Features -->
                    @if($plan->planFeatures->count() > 0)
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Plan Features</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                @foreach($plan->planFeatures as $feature)
                                    <div class="flex items-start">
                                        <i class="fas fa-check text-green-500 text-sm mt-0.5 mr-3 flex-shrink-0"></i>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{{ $feature->feature_name }}</div>
                                            @if($feature->feature_value)
                                                <div class="text-sm text-gray-600">{{ $feature->feature_value }}</div>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Subscribers -->
                    @if($plan->userSubscriptions->count() > 0)
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                            <div class="p-6 border-b border-gray-200">
                                <h3 class="text-lg font-medium text-gray-900">Subscribers ({{ $plan->userSubscriptions->count() }})</h3>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Started</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expires</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        @foreach($plan->userSubscriptions as $subscription)
                                            <tr class="hover:bg-gray-50">
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="flex items-center">
                                                        <div>
                                                            <div class="text-sm font-medium text-gray-900">{{ $subscription->user->name }}</div>
                                                            <div class="text-sm text-gray-500">{{ $subscription->user->email }}</div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    @if($subscription->isActive())
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                            Active
                                                        </span>
                                                    @else
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                            {{ ucfirst($subscription->status) }}
                                                        </span>
                                                    @endif
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    {{ $subscription->starts_at ? $subscription->starts_at->format('M j, Y') : 'N/A' }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    {{ $subscription->ends_at ? $subscription->ends_at->format('M j, Y') : 'Never' }}
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Statistics -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Statistics</h3>
                        <div class="space-y-4">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Total Subscribers</span>
                                <span class="text-sm font-medium text-gray-900">{{ $plan->userSubscriptions->count() }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Active Subscribers</span>
                                <span class="text-sm font-medium text-gray-900">{{ $plan->userSubscriptions->filter(fn($sub) => $sub->isActive())->count() }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Monthly Revenue</span>
                                <span class="text-sm font-medium text-gray-900">
                                    ₹{{ number_format($plan->userSubscriptions->filter(fn($sub) => $sub->isActive())->count() * $plan->price) }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Actions</h3>
                        <div class="space-y-3">
                            <form action="{{ route('admin.plans.toggle-status', $plan) }}" method="POST">
                                @csrf
                                @method('PATCH')
                                <button type="submit" 
                                        class="w-full {{ $plan->is_active ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700' }} text-white px-4 py-2 rounded-lg transition-colors duration-200">
                                    <i class="fas fa-{{ $plan->is_active ? 'pause' : 'play' }} mr-2"></i>
                                    {{ $plan->is_active ? 'Deactivate Plan' : 'Activate Plan' }}
                                </button>
                            </form>
                            
                            @if($plan->userSubscriptions->count() == 0)
                                <form action="{{ route('admin.plans.destroy', $plan) }}" method="POST" 
                                      onsubmit="return confirm('Are you sure you want to delete this plan? This action cannot be undone.')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" 
                                            class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                                        <i class="fas fa-trash mr-2"></i>
                                        Delete Plan
                                    </button>
                                </form>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
