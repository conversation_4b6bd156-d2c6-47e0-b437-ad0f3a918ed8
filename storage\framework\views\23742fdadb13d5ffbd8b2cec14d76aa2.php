<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header Section -->
            <div class="text-center mb-16">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-3xl mb-6 shadow-glow-lg">
                    <i class="fas fa-crown text-2xl text-white"></i>
                </div>
                <h1 class="text-4xl lg:text-5xl font-bold text-secondary-900 mb-6 tracking-tight">
                    Choose the Perfect Plan for Your Business
                </h1>
                <p class="text-xl text-secondary-600 max-w-3xl mx-auto leading-relaxed">
                    Start with our free plan and upgrade as you grow. All plans include our core features with no setup fees.
                </p>

                <!-- Billing Toggle -->
                <div class="mt-8 flex items-center justify-center">
                    <span class="text-secondary-600 mr-3">Monthly</span>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" class="sr-only peer" id="billing-toggle">
                        <div class="w-11 h-6 bg-secondary-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                    </label>
                    <span class="text-secondary-600 ml-3">Yearly</span>
                    <span class="ml-2 px-2 py-1 bg-success-100 text-success-800 text-xs font-medium rounded-full">Save 20%</span>
                </div>
            </div>

            <!-- Plans Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
                <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="relative <?php echo e($plan->is_popular ? 'transform scale-105' : ''); ?>">
                    <?php if($plan->is_popular): ?>
                        <div class="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                            <span class="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg">
                                <i class="fas fa-star mr-1"></i>
                                Most Popular
                            </span>
                        </div>
                    <?php endif; ?>

                    <div class="card <?php echo e($plan->is_popular ? 'ring-2 ring-primary-500 shadow-glow-lg' : ''); ?> hover:shadow-card-lg transition-all duration-300 h-full">
                        <div class="card-body text-center">
                            <!-- Plan Icon -->
                            <div class="w-16 h-16 mx-auto mb-6 bg-gradient-to-br <?php echo e($plan->is_popular ? 'from-primary-500 to-primary-600' : 'from-secondary-100 to-secondary-200'); ?> rounded-2xl flex items-center justify-center">
                                <i class="fas <?php echo e($plan->name === 'Free' ? 'fa-gift' : ($plan->name === 'Pro' ? 'fa-rocket' : 'fa-crown')); ?> text-2xl <?php echo e($plan->is_popular ? 'text-white' : 'text-secondary-600'); ?>"></i>
                            </div>

                            <!-- Plan Name -->
                            <h3 class="text-2xl font-bold text-secondary-900 mb-2"><?php echo e($plan->name); ?></h3>
                            <p class="text-secondary-600 mb-8"><?php echo e($plan->description); ?></p>

                            <!-- Pricing -->
                            <div class="mb-8">
                                <?php if($plan->price == 0): ?>
                                    <div class="text-4xl font-bold text-secondary-900">Free</div>
                                    <div class="text-secondary-500">Forever</div>
                                <?php else: ?>
                                    <div class="flex items-baseline justify-center">
                                        <span class="text-secondary-500 text-lg">₹</span>
                                        <span class="text-5xl font-bold text-secondary-900"><?php echo e($plan->price); ?></span>
                                        <span class="text-secondary-500 text-lg ml-1">/<?php echo e($plan->billing_cycle); ?></span>
                                    </div>
                                    <?php if($plan->billing_cycle === 'month'): ?>
                                        <div class="text-sm text-secondary-500 mt-1">Billed monthly</div>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>

                            <!-- Features List -->
                            <div class="mb-8">
                                <h4 class="text-sm font-semibold text-secondary-700 uppercase tracking-wider mb-4">What's included</h4>
                                <ul class="space-y-3 text-left">
                                    <?php $__currentLoopData = $plan->planFeatures; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="flex items-start space-x-3">
                                        <div class="flex-shrink-0 w-5 h-5 bg-success-100 rounded-full flex items-center justify-center mt-0.5">
                                            <i class="fas fa-check text-success-600 text-xs"></i>
                                        </div>
                                        <span class="text-secondary-600"><?php echo e($feature->feature_name); ?></span>
                                    </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>

                            <!-- Action Button -->
                            <?php if(auth()->user()->current_plan_id == $plan->id): ?>
                                <?php if (isset($component)) { $__componentOriginala8bb031a483a05f647cb99ed3a469847 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala8bb031a483a05f647cb99ed3a469847 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.ui.button','data' => ['variant' => 'secondary','size' => 'lg','class' => 'w-full justify-center','disabled' => true,'icon' => 'fas fa-check']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('ui.button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'secondary','size' => 'lg','class' => 'w-full justify-center','disabled' => true,'icon' => 'fas fa-check']); ?>
                                    Current Plan
                                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala8bb031a483a05f647cb99ed3a469847)): ?>
<?php $attributes = $__attributesOriginala8bb031a483a05f647cb99ed3a469847; ?>
<?php unset($__attributesOriginala8bb031a483a05f647cb99ed3a469847); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala8bb031a483a05f647cb99ed3a469847)): ?>
<?php $component = $__componentOriginala8bb031a483a05f647cb99ed3a469847; ?>
<?php unset($__componentOriginala8bb031a483a05f647cb99ed3a469847); ?>
<?php endif; ?>
                            <?php else: ?>
                                <form action="<?php echo e(route('subscriptions.subscribe', $plan)); ?>" method="POST">
                                    <?php echo csrf_field(); ?>
                                    <?php if (isset($component)) { $__componentOriginala8bb031a483a05f647cb99ed3a469847 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala8bb031a483a05f647cb99ed3a469847 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.ui.button','data' => ['type' => 'submit','variant' => ''.e($plan->is_popular ? 'primary' : 'outline').'','size' => 'lg','class' => 'w-full justify-center '.e($plan->is_popular ? 'shadow-glow hover:shadow-glow-lg transform hover:scale-105' : '').'','icon' => 'fas fa-'.e($plan->price == 0 ? 'gift' : 'rocket').'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('ui.button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'submit','variant' => ''.e($plan->is_popular ? 'primary' : 'outline').'','size' => 'lg','class' => 'w-full justify-center '.e($plan->is_popular ? 'shadow-glow hover:shadow-glow-lg transform hover:scale-105' : '').'','icon' => 'fas fa-'.e($plan->price == 0 ? 'gift' : 'rocket').'']); ?>
                                        <?php echo e($plan->price == 0 ? 'Get Started Free' : 'Choose ' . $plan->name); ?>

                                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala8bb031a483a05f647cb99ed3a469847)): ?>
<?php $attributes = $__attributesOriginala8bb031a483a05f647cb99ed3a469847; ?>
<?php unset($__attributesOriginala8bb031a483a05f647cb99ed3a469847); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala8bb031a483a05f647cb99ed3a469847)): ?>
<?php $component = $__componentOriginala8bb031a483a05f647cb99ed3a469847; ?>
<?php unset($__componentOriginala8bb031a483a05f647cb99ed3a469847); ?>
<?php endif; ?>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- FAQ Section -->
            <div class="mt-20">
                <div class="text-center mb-12">
                    <h3 class="text-3xl font-bold text-secondary-900 mb-4">Frequently Asked Questions</h3>
                    <p class="text-secondary-600">Everything you need to know about our plans and pricing</p>
                </div>

                <div class="max-w-4xl mx-auto">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div class="card">
                            <div class="card-body">
                                <div class="flex items-start space-x-4">
                                    <div class="flex-shrink-0 w-10 h-10 bg-primary-100 rounded-xl flex items-center justify-center">
                                        <i class="fas fa-sync-alt text-primary-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-secondary-900 mb-2">Can I change my plan anytime?</h4>
                                        <p class="text-secondary-600">Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-body">
                                <div class="flex items-start space-x-4">
                                    <div class="flex-shrink-0 w-10 h-10 bg-success-100 rounded-xl flex items-center justify-center">
                                        <i class="fas fa-credit-card text-success-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-secondary-900 mb-2">What payment methods do you accept?</h4>
                                        <p class="text-secondary-600">We accept all major credit cards, PayPal, and Razorpay for your convenience.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-body">
                                <div class="flex items-start space-x-4">
                                    <div class="flex-shrink-0 w-10 h-10 bg-accent-100 rounded-xl flex items-center justify-center">
                                        <i class="fas fa-gift text-accent-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-secondary-900 mb-2">Is there a free trial?</h4>
                                        <p class="text-secondary-600">Yes, all new users get a 14-day free trial to explore all features with no credit card required.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-body">
                                <div class="flex items-start space-x-4">
                                    <div class="flex-shrink-0 w-10 h-10 bg-warning-100 rounded-xl flex items-center justify-center">
                                        <i class="fas fa-shield-alt text-warning-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-secondary-900 mb-2">Is my data secure?</h4>
                                        <p class="text-secondary-600">Absolutely! We use enterprise-grade security and encryption to protect your business data.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Trust Indicators -->
            <div class="mt-16 text-center">
                <div class="card max-w-4xl mx-auto">
                    <div class="card-body">
                        <div class="flex flex-col md:flex-row items-center justify-center space-y-6 md:space-y-0 md:space-x-12">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-success-100 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-lock text-success-600 text-xl"></i>
                                </div>
                                <div class="text-left">
                                    <div class="font-semibold text-secondary-900">Secure & Encrypted</div>
                                    <div class="text-sm text-secondary-600">Bank-level security</div>
                                </div>
                            </div>

                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-headset text-primary-600 text-xl"></i>
                                </div>
                                <div class="text-left">
                                    <div class="font-semibold text-secondary-900">24/7 Support</div>
                                    <div class="text-sm text-secondary-600">Always here to help</div>
                                </div>
                            </div>

                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-accent-100 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-undo text-accent-600 text-xl"></i>
                                </div>
                                <div class="text-left">
                                    <div class="font-semibold text-secondary-900">30-Day Guarantee</div>
                                    <div class="text-sm text-secondary-600">Money back promise</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </div>
</div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\freeligo\resources\views/subscriptions/plans.blade.php ENDPATH**/ ?>