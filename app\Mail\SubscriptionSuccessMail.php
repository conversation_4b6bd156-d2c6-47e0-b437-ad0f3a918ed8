<?php

namespace App\Mail;

use App\Models\User;
use App\Models\Subscription;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class SubscriptionSuccessMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public User $user;
    public Subscription $subscription;
    public array $paymentDetails;

    /**
     * Create a new message instance.
     */
    public function __construct(User $user, Subscription $subscription, array $paymentDetails = [])
    {
        $this->user = $user;
        $this->subscription = $subscription;
        $this->paymentDetails = $paymentDetails;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: "🎉 Payment Successful! Welcome to " . ($this->subscription->plan->name ?? 'Pro') . " Plan",
            to: [$this->user->email],
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.subscription-success',
            with: [
                'user' => $this->user,
                'subscription' => $this->subscription,
                'plan' => $this->subscription->plan,
                'paymentDetails' => $this->paymentDetails,
                'header_title' => 'Payment Successful!',
                'header_subtitle' => 'Welcome to your new plan - let\'s achieve great things together',
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
