@props([
    'user' => null,
    'dismissible' => true
])

@php
    $user = $user ?: auth()->user();
    
    // Define onboarding tasks
    $tasks = [
        [
            'id' => 'profile_complete',
            'title' => 'Complete Your Profile',
            'description' => 'Add your business information and profile details',
            'icon' => 'fas fa-user-circle',
            'completed' => $user->profile_completed ?? false,
            'action_url' => route('profile.edit'),
            'action_text' => 'Complete Profile'
        ],
        [
            'id' => 'first_client',
            'title' => 'Add Your First Client',
            'description' => 'Start building your client database',
            'icon' => 'fas fa-users',
            'completed' => $user->clients()->count() > 0,
            'action_url' => route('clients.create'),
            'action_text' => 'Add Client'
        ],
        [
            'id' => 'first_invoice',
            'title' => 'Create Your First Invoice',
            'description' => 'Send professional invoices to get paid faster',
            'icon' => 'fas fa-file-invoice-dollar',
            'completed' => $user->invoices()->count() > 0,
            'action_url' => route('invoices.create'),
            'action_text' => 'Create Invoice'
        ],
        [
            'id' => 'payment_setup',
            'title' => 'Set Up Payment Methods',
            'description' => 'Configure how you want to receive payments',
            'icon' => 'fas fa-credit-card',
            'completed' => $user->payment_methods_configured ?? false,
            'action_url' => route('profile.edit') . '#payment-methods',
            'action_text' => 'Setup Payments'
        ],
        [
            'id' => 'business_branding',
            'title' => 'Customize Your Branding',
            'description' => 'Add your logo and brand colors to invoices',
            'icon' => 'fas fa-palette',
            'completed' => $user->branding_configured ?? false,
            'action_url' => route('profile.edit') . '#branding',
            'action_text' => 'Add Branding'
        ]
    ];
    
    $completedTasks = collect($tasks)->where('completed', true)->count();
    $totalTasks = count($tasks);
    $progressPercentage = $totalTasks > 0 ? ($completedTasks / $totalTasks) * 100 : 0;
    $isCompleted = $completedTasks === $totalTasks;
@endphp

<div x-data="{ 
    showChecklist: {{ $isCompleted ? 'false' : 'true' }}, 
    isMinimized: false,
    completedTasks: {{ $completedTasks }},
    totalTasks: {{ $totalTasks }}
}" 
     x-show="showChecklist"
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0 transform translate-y-4"
     x-transition:enter-end="opacity-100 transform translate-y-0"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100 transform translate-y-0"
     x-transition:leave-end="opacity-0 transform translate-y-4"
     class="fixed bottom-6 right-6 z-40 w-96 max-w-[calc(100vw-2rem)]"
     style="display: none;">
    
    <div class="card shadow-card-lg border-2 border-primary-200 bg-gradient-to-br from-white to-primary-50">
        <!-- Header -->
        <div class="card-header bg-gradient-to-r from-primary-500 to-primary-600 text-white">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                        <i class="fas fa-rocket text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-bold text-lg">Getting Started</h3>
                        <p class="text-primary-100 text-sm">{{ $completedTasks }}/{{ $totalTasks }} completed</p>
                    </div>
                </div>
                
                <div class="flex items-center space-x-2">
                    <button @click="isMinimized = !isMinimized" 
                            class="text-white hover:text-primary-200 transition-colors duration-200">
                        <i class="fas" :class="isMinimized ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                    </button>
                    
                    @if($dismissible)
                        <button @click="showChecklist = false" 
                                class="text-white hover:text-primary-200 transition-colors duration-200">
                            <i class="fas fa-times"></i>
                        </button>
                    @endif
                </div>
            </div>
            
            <!-- Progress Bar -->
            <div class="mt-4">
                <div class="w-full bg-primary-400 bg-opacity-30 rounded-full h-2">
                    <div class="bg-white h-2 rounded-full transition-all duration-500" 
                         style="width: {{ $progressPercentage }}%"></div>
                </div>
            </div>
        </div>
        
        <!-- Content -->
        <div x-show="!isMinimized" 
             x-transition:enter="transition ease-out duration-200"
             x-transition:enter-start="opacity-0 max-h-0"
             x-transition:enter-end="opacity-100 max-h-96"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100 max-h-96"
             x-transition:leave-end="opacity-0 max-h-0"
             class="card-body overflow-hidden">
            
            @if($isCompleted)
                <!-- Completion Message -->
                <div class="text-center py-6">
                    <div class="w-16 h-16 bg-gradient-to-br from-success-500 to-success-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-check text-2xl text-white"></i>
                    </div>
                    <h4 class="text-xl font-bold text-secondary-900 mb-2">🎉 All Set!</h4>
                    <p class="text-secondary-600 mb-4">You've completed all onboarding tasks. You're ready to grow your business!</p>
                    <x-ui.button href="{{ route('dashboard') }}" variant="primary" size="sm">
                        Go to Dashboard
                    </x-ui.button>
                </div>
            @else
                <!-- Task List -->
                <div class="space-y-4">
                    @foreach($tasks as $task)
                        <div class="flex items-start space-x-4 p-3 rounded-xl {{ $task['completed'] ? 'bg-success-50 border border-success-200' : 'bg-secondary-50 border border-secondary-200' }} transition-all duration-200">
                            <!-- Task Icon -->
                            <div class="flex-shrink-0 w-10 h-10 rounded-xl flex items-center justify-center {{ $task['completed'] ? 'bg-success-500 text-white' : 'bg-secondary-200 text-secondary-600' }}">
                                @if($task['completed'])
                                    <i class="fas fa-check"></i>
                                @else
                                    <i class="{{ $task['icon'] }}"></i>
                                @endif
                            </div>
                            
                            <!-- Task Content -->
                            <div class="flex-1 min-w-0">
                                <h4 class="font-semibold text-secondary-900 {{ $task['completed'] ? 'line-through' : '' }}">
                                    {{ $task['title'] }}
                                </h4>
                                <p class="text-sm text-secondary-600 mt-1">{{ $task['description'] }}</p>
                                
                                @if(!$task['completed'])
                                    <div class="mt-3">
                                        <x-ui.button 
                                            href="{{ $task['action_url'] }}" 
                                            variant="primary" 
                                            size="sm"
                                            class="text-xs"
                                        >
                                            {{ $task['action_text'] }}
                                        </x-ui.button>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
                
                <!-- Help Section -->
                <div class="mt-6 p-4 bg-gradient-to-r from-accent-50 to-primary-50 rounded-xl border border-accent-200">
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-accent-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-lightbulb text-accent-600"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-secondary-900 text-sm">Need Help?</h4>
                            <p class="text-xs text-secondary-600 mt-1">Check out our guides or contact support for assistance.</p>
                            <div class="flex items-center space-x-3 mt-2">
                                <a href="#" class="text-xs text-accent-600 hover:text-accent-700 font-medium">View Guides</a>
                                <a href="#" class="text-xs text-accent-600 hover:text-accent-700 font-medium">Contact Support</a>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
