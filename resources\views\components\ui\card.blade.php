@props([
    'title' => null,
    'subtitle' => null,
    'headerActions' => null,
    'padding' => true,
    'shadow' => 'sm'
])

@php
    $shadowClasses = [
        'none' => '',
        'sm' => 'shadow-sm',
        'md' => 'shadow-md',
        'lg' => 'shadow-lg',
        'soft' => 'shadow-soft'
    ];
    
    $cardClasses = 'card ' . $shadowClasses[$shadow];
@endphp

<div {{ $attributes->merge(['class' => $cardClasses]) }}>
    @if($title || $subtitle || $headerActions)
        <div class="card-header">
            <div class="flex items-center justify-between">
                <div>
                    @if($title)
                        <h3 class="text-lg font-semibold text-secondary-900">{{ $title }}</h3>
                    @endif
                    @if($subtitle)
                        <p class="text-sm text-secondary-600 mt-1">{{ $subtitle }}</p>
                    @endif
                </div>
                @if($headerActions)
                    <div class="flex items-center space-x-2">
                        {{ $headerActions }}
                    </div>
                @endif
            </div>
        </div>
    @endif

    <div class="{{ $padding ? 'card-body' : '' }}">
        {{ $slot }}
    </div>
</div>
