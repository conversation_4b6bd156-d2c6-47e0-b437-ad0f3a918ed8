import './bootstrap';

import Alpine from 'alpinejs';
import Toastify from 'toastify-js';
import Swal from 'sweetalert2';

// Make libraries globally available
window.Alpine = Alpine;
window.Toastify = Toastify;
window.Swal = Swal;

// Global toast notification function
window.showToast = function(message, type = 'success') {
    const colors = {
        success: 'linear-gradient(to right, #22c55e, #16a34a)',
        error: 'linear-gradient(to right, #ef4444, #dc2626)',
        warning: 'linear-gradient(to right, #f59e0b, #d97706)',
        info: 'linear-gradient(to right, #0ea5e9, #0284c7)'
    };

    Toastify({
        text: message,
        duration: 3000,
        gravity: "top",
        position: "right",
        style: {
            background: colors[type] || colors.success,
        },
        stopOnFocus: true,
    }).showToast();
};

// Global confirmation dialog function
window.confirmAction = function(title, text, confirmButtonText = 'Yes, do it!') {
    return Swal.fire({
        title: title,
        text: text,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ef4444',
        cancelButtonColor: '#6b7280',
        confirmButtonText: confirmButtonText,
        cancelButtonText: 'Cancel'
    });
};

// Global success dialog function
window.showSuccess = function(title, text) {
    return Swal.fire({
        title: title,
        text: text,
        icon: 'success',
        confirmButtonColor: '#22c55e'
    });
};

// Global error dialog function
window.showError = function(title, text) {
    return Swal.fire({
        title: title,
        text: text,
        icon: 'error',
        confirmButtonColor: '#ef4444'
    });
};

// Professional micro-interactions
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth scroll behavior
    document.documentElement.style.scrollBehavior = 'smooth';

    // Add loading states to buttons
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitButton = this.querySelector('button[type="submit"]');
            if (submitButton && !submitButton.disabled && !submitButton.classList.contains('no-loading')) {
                // Add loading state
                const originalText = submitButton.innerHTML;
                const loadingIcon = '<i class="fas fa-spinner fa-spin mr-2"></i>';

                submitButton.innerHTML = loadingIcon + 'Processing...';
                submitButton.disabled = true;

                // Reset after timeout in case of errors
                setTimeout(() => {
                    submitButton.innerHTML = originalText;
                    submitButton.disabled = false;
                }, 10000);
            }
        });
    });

    // Add hover effects to cards
    document.querySelectorAll('.card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Add ripple effect to buttons
    document.querySelectorAll('.btn').forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // Add fade-in animation to elements as they come into view
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in-up');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    document.querySelectorAll('.card, .stats-card, .nav-link').forEach(el => {
        observer.observe(el);
    });
});

Alpine.start();
