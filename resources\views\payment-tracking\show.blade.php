<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    Payment Tracking - {{ $invoice->invoice_number }}
                </h2>
                <p class="text-sm text-gray-600 mt-1">{{ $invoice->client->name }}</p>
            </div>
            <div class="flex space-x-2">
                <a href="{{ route('payment-tracking.index') }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    <i class="fas fa-arrow-left mr-1"></i>
                    Back to Tracking
                </a>
                <a href="{{ route('invoices.show', $invoice) }}" 
                   class="bg-emerald-500 hover:bg-emerald-700 text-white font-bold py-2 px-4 rounded">
                    <i class="fas fa-file-invoice mr-1"></i>
                    View Invoice
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            
            <!-- Payment Summary -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-gray-900">
                            {{ config('services.currency.symbol', '₹') }}{{ number_format($paymentSummary['total_amount'], 2) }}
                        </div>
                        <p class="text-sm text-gray-500 mt-1">Total Amount</p>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-600">
                            {{ config('services.currency.symbol', '₹') }}{{ number_format($paymentSummary['total_paid'], 2) }}
                        </div>
                        <p class="text-sm text-gray-500 mt-1">Amount Paid</p>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600">
                            {{ config('services.currency.symbol', '₹') }}{{ number_format($paymentSummary['remaining_amount'], 2) }}
                        </div>
                        <p class="text-sm text-gray-500 mt-1">Remaining</p>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-purple-600">
                            {{ $paymentSummary['payment_count'] }}
                        </div>
                        <p class="text-sm text-gray-500 mt-1">Payments Made</p>
                    </div>
                </div>
            </div>

            <!-- Payment Analytics -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Payment Analytics</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-emerald-600">
                                {{ number_format($paymentAnalytics['success_rate'], 1) }}%
                            </div>
                            <p class="text-sm text-gray-500">Success Rate</p>
                            <p class="text-xs text-gray-400 mt-1">
                                {{ $paymentAnalytics['successful_payments'] }} of {{ $paymentAnalytics['total_attempts'] }} attempts
                            </p>
                        </div>

                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600">
                                {{ config('services.currency.symbol', '₹') }}{{ number_format($paymentAnalytics['average_payment_amount'], 2) }}
                            </div>
                            <p class="text-sm text-gray-500">Average Payment</p>
                        </div>

                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600">
                                {{ number_format($paymentAnalytics['total_processing_time'], 0) }}m
                            </div>
                            <p class="text-sm text-gray-500">Avg Processing Time</p>
                        </div>
                    </div>

                    @if(count($paymentAnalytics['payment_methods_used']) > 0)
                        <div class="mt-6">
                            <h4 class="text-sm font-medium text-gray-700 mb-2">Payment Methods Used</h4>
                            <div class="flex flex-wrap gap-2">
                                @foreach($paymentAnalytics['payment_methods_used'] as $method)
                                    <span class="inline-flex px-3 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">
                                        {{ ucfirst($method) }}
                                    </span>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Invoice Stats -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Invoice Activity</h3>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold {{ $invoiceStats['viewed_by_client'] ? 'text-green-600' : 'text-gray-400' }}">
                                <i class="fas {{ $invoiceStats['viewed_by_client'] ? 'fa-eye' : 'fa-eye-slash' }}"></i>
                            </div>
                            <p class="text-sm text-gray-500 mt-1">
                                {{ $invoiceStats['viewed_by_client'] ? 'Viewed by Client' : 'Not Viewed' }}
                            </p>
                            @if($invoiceStats['client_viewed_at'])
                                <p class="text-xs text-gray-400">{{ $invoiceStats['client_viewed_at']->format('M d, Y g:i A') }}</p>
                            @endif
                        </div>

                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600">
                                {{ $invoiceStats['days_since_issued'] }}
                            </div>
                            <p class="text-sm text-gray-500 mt-1">Days Since Issued</p>
                        </div>

                        <div class="text-center">
                            <div class="text-2xl font-bold {{ $invoiceStats['is_overdue'] ? 'text-red-600' : 'text-green-600' }}">
                                {{ abs($invoiceStats['days_until_due']) }}
                            </div>
                            <p class="text-sm text-gray-500 mt-1">
                                {{ $invoiceStats['is_overdue'] ? 'Days Overdue' : 'Days Until Due' }}
                            </p>
                        </div>

                        <div class="text-center">
                            <div class="text-2xl font-bold text-yellow-600">
                                {{ $invoiceStats['payment_attempts_today'] }}
                            </div>
                            <p class="text-sm text-gray-500 mt-1">Payment Attempts Today</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment History -->
            @if($paymentSummary['payment_count'] > 0)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Payment History</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment ID</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gateway</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($invoice->invoicePayments as $payment)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                {{ $payment->payment_id }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $payment->formatted_amount }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                                    {{ ucfirst($payment->gateway) }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                    @if($payment->status === 'completed') bg-green-100 text-green-800
                                                    @elseif($payment->status === 'failed') bg-red-100 text-red-800
                                                    @elseif($payment->status === 'pending') bg-yellow-100 text-yellow-800
                                                    @else bg-gray-100 text-gray-800 @endif">
                                                    {{ ucfirst($payment->status) }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                @if($payment->paid_at)
                                                    {{ $payment->paid_at->format('M d, Y g:i A') }}
                                                @else
                                                    {{ $payment->created_at->format('M d, Y g:i A') }}
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Client Portal Links -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Client Portal Access</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Invoice View Link</label>
                            <div class="flex">
                                <input type="text" readonly value="{{ $invoice->public_url }}" 
                                       class="flex-1 rounded-l-md border-gray-300 bg-gray-50 text-sm">
                                <button onclick="copyToClipboard('{{ $invoice->public_url }}')" 
                                        class="px-3 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>

                        @if($invoice->status !== 'paid')
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Payment Link</label>
                                <div class="flex">
                                    <input type="text" readonly value="{{ $invoice->payment_url }}" 
                                           class="flex-1 rounded-l-md border-gray-300 bg-gray-50 text-sm">
                                    <button onclick="copyToClipboard('{{ $invoice->payment_url }}')" 
                                            class="px-3 py-2 bg-emerald-600 text-white rounded-r-md hover:bg-emerald-700">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Notification -->
    <div id="toast" class="fixed top-4 right-4 bg-green-600 text-white px-6 py-3 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300 z-50">
        <div class="flex items-center">
            <i class="fas fa-check-circle mr-2"></i>
            <span id="toast-message">Link copied to clipboard!</span>
        </div>
    </div>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                showToast('Link copied to clipboard!');
            }).catch(function() {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showToast('Link copied to clipboard!');
            });
        }

        function showToast(message) {
            const toast = document.getElementById('toast');
            const toastMessage = document.getElementById('toast-message');
            toastMessage.textContent = message;
            toast.classList.remove('translate-x-full');
            setTimeout(() => {
                toast.classList.add('translate-x-full');
            }, 3000);
        }

        // Auto-refresh payment data every 30 seconds
        setInterval(function() {
            location.reload();
        }, 30000);
    </script>
</x-app-layout>
