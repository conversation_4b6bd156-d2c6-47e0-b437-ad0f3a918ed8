@props([
    'feature' => '',
    'title' => '',
    'description' => '',
    'requiredPlan' => 'Pro',
    'showUpgrade' => true,
    'icon' => 'fas fa-lock'
])

@php
use App\Services\PlanChecker;

$user = auth()->user();
$hasAccess = false;

// Check feature access based on feature type
switch($feature) {
    case 'tds_reports':
        $hasAccess = PlanChecker::canAccessTdsReports($user);
        break;
    case 'custom_branding':
        $hasAccess = PlanChecker::canUseCustomBranding($user);
        break;
    case 'whatsapp_shortcuts':
        $hasAccess = PlanChecker::canUseWhatsAppShortcuts($user);
        break;
    case 'multi_client_dashboard':
        $hasAccess = PlanChecker::canAccessMultiClientDashboard($user);
        break;
    case 'portfolio_generator':
        $hasAccess = PlanChecker::canUsePortfolioGenerator($user);
        break;
    case 'ai_assistant':
        $hasAccess = PlanChecker::canUseAiAssistant($user);
        break;
    case 'expense_creation':
        $hasAccess = PlanChecker::canCreateExpense($user);
        break;
    case 'unlimited_expenses':
        $hasAccess = PlanChecker::hasUnlimitedExpenses($user);
        break;
    default:
        $hasAccess = true;
}
@endphp

@if($hasAccess)
    <!-- User has access - render the content -->
    {{ $slot }}
@else
    <!-- User doesn't have access - show upgrade prompt -->
    <div class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl border-2 border-dashed border-gray-300 p-6 sm:p-8 text-center {{ $attributes->get('class') }}">
        <div class="max-w-md mx-auto">
            <!-- Icon -->
            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="{{ $icon }} text-white text-xl"></i>
            </div>
            
            <!-- Title -->
            <h3 class="text-lg sm:text-xl font-semibold text-gray-900 mb-2">
                {{ $title ?: 'Premium Feature' }}
            </h3>
            
            <!-- Description -->
            <p class="text-gray-600 mb-4 text-sm sm:text-base">
                {{ $description ?: 'This feature is available on the ' . $requiredPlan . ' plan and above.' }}
            </p>
            
            <!-- Required Plan Badge -->
            <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mb-4">
                <i class="fas fa-crown mr-1"></i>
                {{ $requiredPlan }} Plan Required
            </div>
            
            <!-- Upgrade Button -->
            @if($showUpgrade)
                <div class="space-y-2">
                    <x-ui.button 
                        href="{{ route('subscriptions.plans') }}" 
                        variant="primary" 
                        icon="fas fa-arrow-up"
                        class="w-full sm:w-auto">
                        Upgrade to {{ $requiredPlan }}
                    </x-ui.button>
                    
                    <p class="text-xs text-gray-500">
                        <a href="{{ route('subscriptions.plans') }}" class="text-blue-600 hover:text-blue-700 underline">
                            View all plans and features
                        </a>
                    </p>
                </div>
            @endif
        </div>
    </div>
@endif
