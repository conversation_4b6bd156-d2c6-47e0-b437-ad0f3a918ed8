<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LeadNote extends Model
{
    use HasFactory;

    protected $fillable = [
        'lead_id',
        'user_id',
        'content',
        'is_private',
        'is_important',
        'type',
        'tags',
        'reminder_at',
    ];

    protected function casts(): array
    {
        return [
            'is_private' => 'boolean',
            'is_important' => 'boolean',
            'tags' => 'array',
            'reminder_at' => 'datetime',
        ];
    }

    /**
     * Get the lead that owns the note.
     */
    public function lead(): BelongsTo
    {
        return $this->belongsTo(Lead::class);
    }

    /**
     * Get the user that created the note.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope a query to only include important notes.
     */
    public function scopeImportant($query)
    {
        return $query->where('is_important', true);
    }

    /**
     * Scope a query to only include public notes.
     */
    public function scopePublic($query)
    {
        return $query->where('is_private', false);
    }

    /**
     * Scope a query to only include private notes.
     */
    public function scopePrivate($query)
    {
        return $query->where('is_private', true);
    }

    /**
     * Scope a query by note type.
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope a query for notes with reminders.
     */
    public function scopeWithReminders($query)
    {
        return $query->whereNotNull('reminder_at');
    }

    /**
     * Scope a query for overdue reminders.
     */
    public function scopeOverdueReminders($query)
    {
        return $query->whereNotNull('reminder_at')
                    ->where('reminder_at', '<=', now());
    }

    /**
     * Get the note type icon.
     */
    public function getTypeIconAttribute(): string
    {
        return match($this->type) {
            'meeting' => 'fas fa-calendar',
            'call' => 'fas fa-phone',
            'email' => 'fas fa-envelope',
            'reminder' => 'fas fa-bell',
            'general' => 'fas fa-sticky-note',
            default => 'fas fa-sticky-note',
        };
    }

    /**
     * Get the note type color.
     */
    public function getTypeColorAttribute(): string
    {
        return match($this->type) {
            'meeting' => '#8B5CF6',
            'call' => '#10B981',
            'email' => '#3B82F6',
            'reminder' => '#F59E0B',
            'general' => '#6B7280',
            default => '#6B7280',
        };
    }

    /**
     * Check if note has an overdue reminder.
     */
    public function hasOverdueReminder(): bool
    {
        return $this->reminder_at && $this->reminder_at->isPast();
    }

    /**
     * Get formatted content with limited length.
     */
    public function getExcerptAttribute(int $length = 100): string
    {
        return strlen($this->content) > $length 
            ? substr($this->content, 0, $length) . '...'
            : $this->content;
    }

    /**
     * Add tag to note.
     */
    public function addTag(string $tag): void
    {
        $tags = $this->tags ?? [];
        if (!in_array($tag, $tags)) {
            $tags[] = $tag;
            $this->update(['tags' => $tags]);
        }
    }

    /**
     * Remove tag from note.
     */
    public function removeTag(string $tag): void
    {
        $tags = $this->tags ?? [];
        $tags = array_filter($tags, fn($t) => $t !== $tag);
        $this->update(['tags' => array_values($tags)]);
    }

    /**
     * Check if note has specific tag.
     */
    public function hasTag(string $tag): bool
    {
        return in_array($tag, $this->tags ?? []);
    }
}
