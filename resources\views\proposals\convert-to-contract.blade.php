<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Convert Proposal to Contract') }}
            </h2>
            <a href="{{ route('proposals.show', $proposal) }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                <i class="fas fa-arrow-left mr-2"></i>Back to Proposal
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <!-- Proposal Summary -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Proposal Summary</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <dl class="space-y-3">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Title</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $proposal->title }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Proposal Number</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $proposal->proposal_number }}</dd>
                                </div>
                                @if($proposal->client)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Client</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $proposal->client->name }}</dd>
                                    </div>
                                @endif
                            </dl>
                        </div>
                        <div>
                            <dl class="space-y-3">
                                @if($proposal->total_amount)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Amount</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $proposal->formatted_amount }}</dd>
                                    </div>
                                @endif
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                                    <dd class="mt-1">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                            {{ ucfirst($proposal->status) }}
                                        </span>
                                    </dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contract Creation Form -->
            <form method="POST" action="{{ route('proposals.convert-to-contract', $proposal) }}">
                @csrf
                
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-6">Contract Details</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Contract Title -->
                            <div class="md:col-span-2">
                                <label for="title" class="block text-sm font-medium text-gray-700">Contract Title *</label>
                                <input type="text" name="title" id="title" value="{{ old('title', $proposal->title . ' - Service Agreement') }}" required
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('title')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Client -->
                            <div>
                                <label for="client_id" class="block text-sm font-medium text-gray-700">Client</label>
                                <select name="client_id" id="client_id" 
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="">Select a client (optional)</option>
                                    @foreach($clients as $client)
                                        <option value="{{ $client->id }}" 
                                                {{ old('client_id', $proposal->client_id) == $client->id ? 'selected' : '' }}>
                                            {{ $client->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('client_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Contract Type -->
                            <div>
                                <label for="contract_type" class="block text-sm font-medium text-gray-700">Contract Type</label>
                                <select name="contract_type" id="contract_type" 
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="service_agreement" {{ old('contract_type', 'service_agreement') === 'service_agreement' ? 'selected' : '' }}>Service Agreement</option>
                                    <option value="project_contract" {{ old('contract_type') === 'project_contract' ? 'selected' : '' }}>Project Contract</option>
                                    <option value="retainer_agreement" {{ old('contract_type') === 'retainer_agreement' ? 'selected' : '' }}>Retainer Agreement</option>
                                    <option value="nda" {{ old('contract_type') === 'nda' ? 'selected' : '' }}>Non-Disclosure Agreement</option>
                                    <option value="other" {{ old('contract_type') === 'other' ? 'selected' : '' }}>Other</option>
                                </select>
                                @error('contract_type')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Start Date -->
                            <div>
                                <label for="start_date" class="block text-sm font-medium text-gray-700">Start Date</label>
                                <input type="date" name="start_date" id="start_date" 
                                       value="{{ old('start_date', now()->format('Y-m-d')) }}"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('start_date')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- End Date -->
                            <div>
                                <label for="end_date" class="block text-sm font-medium text-gray-700">End Date</label>
                                <input type="date" name="end_date" id="end_date" 
                                       value="{{ old('end_date') }}"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('end_date')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Contract Value -->
                            <div>
                                <label for="contract_value" class="block text-sm font-medium text-gray-700">Contract Value</label>
                                <div class="mt-1 relative rounded-md shadow-sm">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm">$</span>
                                    </div>
                                    <input type="number" name="contract_value" id="contract_value" step="0.01" min="0" 
                                           value="{{ old('contract_value', $proposal->total_amount) }}"
                                           class="pl-7 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                </div>
                                @error('contract_value')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Payment Terms -->
                            <div>
                                <label for="payment_terms" class="block text-sm font-medium text-gray-700">Payment Terms</label>
                                <select name="payment_terms" id="payment_terms" 
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="net_30" {{ old('payment_terms', 'net_30') === 'net_30' ? 'selected' : '' }}>Net 30</option>
                                    <option value="net_15" {{ old('payment_terms') === 'net_15' ? 'selected' : '' }}>Net 15</option>
                                    <option value="net_7" {{ old('payment_terms') === 'net_7' ? 'selected' : '' }}>Net 7</option>
                                    <option value="due_on_receipt" {{ old('payment_terms') === 'due_on_receipt' ? 'selected' : '' }}>Due on Receipt</option>
                                    <option value="50_50" {{ old('payment_terms') === '50_50' ? 'selected' : '' }}>50% Upfront, 50% on Completion</option>
                                    <option value="milestone_based" {{ old('payment_terms') === 'milestone_based' ? 'selected' : '' }}>Milestone Based</option>
                                </select>
                                @error('payment_terms')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Description -->
                            <div class="md:col-span-2">
                                <label for="description" class="block text-sm font-medium text-gray-700">Contract Description</label>
                                <textarea name="description" id="description" rows="4" 
                                          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('description', $proposal->description) }}</textarea>
                                @error('description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Terms and Conditions -->
                            <div class="md:col-span-2">
                                <label for="terms_and_conditions" class="block text-sm font-medium text-gray-700">Terms and Conditions</label>
                                <textarea name="terms_and_conditions" id="terms_and_conditions" rows="8" 
                                          placeholder="Enter the terms and conditions for this contract..."
                                          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('terms_and_conditions', $proposal->content) }}</textarea>
                                @error('terms_and_conditions')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Notes -->
                            <div class="md:col-span-2">
                                <label for="notes" class="block text-sm font-medium text-gray-700">Internal Notes</label>
                                <textarea name="notes" id="notes" rows="3" 
                                          placeholder="Internal notes for this contract..."
                                          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('notes', "Generated from proposal: {$proposal->proposal_number}") }}</textarea>
                                @error('notes')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Conversion Options -->
                        <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                            <h4 class="text-sm font-medium text-blue-900 mb-3">Conversion Options</h4>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" name="mark_proposal_accepted" value="1" checked
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-blue-700">Mark proposal as accepted</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="copy_proposal_content" value="1" checked
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-blue-700">Copy proposal content to contract terms</span>
                                </label>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="mt-6 flex justify-end space-x-3">
                            <a href="{{ route('proposals.show', $proposal) }}" 
                               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" 
                                    class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                                <i class="fas fa-file-contract mr-2"></i>Convert to Contract
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</x-app-layout>
