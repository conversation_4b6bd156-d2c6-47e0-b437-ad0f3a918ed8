<?php

namespace App\Policies;

use App\Models\ExpenseCategory;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class ExpenseCategoryPolicy
{
    /**
     * Determine whether the user can view any expense categories.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasRole('admin') && $user->can('view expense categories');
    }

    /**
     * Determine whether the user can view the expense category.
     */
    public function view(User $user, ExpenseCategory $expenseCategory): bool
    {
        return $user->hasRole('admin') && $user->can('view expense categories');
    }

    /**
     * Determine whether the user can create expense categories.
     */
    public function create(User $user): bool
    {
        return $user->hasRole('admin') && $user->can('create expense categories');
    }

    /**
     * Determine whether the user can update the expense category.
     */
    public function update(User $user, ExpenseCategory $expenseCategory): bool
    {
        return $user->hasRole('admin') && $user->can('edit expense categories');
    }

    /**
     * Determine whether the user can delete the expense category.
     */
    public function delete(User $user, ExpenseCategory $expenseCategory): bool
    {
        return $user->hasRole('admin') && $user->can('delete expense categories');
    }

    /**
     * Determine whether the user can restore the expense category.
     */
    public function restore(User $user, ExpenseCategory $expenseCategory): bool
    {
        return $user->hasRole('admin');
    }

    /**
     * Determine whether the user can permanently delete the expense category.
     */
    public function forceDelete(User $user, ExpenseCategory $expenseCategory): bool
    {
        return $user->hasRole('admin');
    }
}
