@extends('emails.layouts.app')

@section('content')
    <!-- Invoice Header -->
    @if($isReminder)
        <div style="text-align: center; margin-bottom: 32px;">
            <div style="font-size: 48px; margin-bottom: 16px;">⏰</div>
            <h2 style="color: #f59e0b; margin-bottom: 8px;">Payment Reminder</h2>
            <p class="lead" style="color: #f59e0b;">
                Hi {{ $client->name }}, this is a friendly reminder about your pending invoice payment.
            </p>
        </div>
    @else
        <div style="text-align: center; margin-bottom: 32px;">
            <div style="font-size: 48px; margin-bottom: 16px;">📄</div>
            <h2 style="color: #166534; margin-bottom: 8px;">New Invoice</h2>
            <p class="lead" style="color: #15803d;">
                Hi {{ $client->name }}, please find your invoice attached. Thank you for your business!
            </p>
        </div>
    @endif
    
    <!-- Invoice Details -->
    <div class="{{ $isReminder ? 'warning-box' : 'success-box' }}">
        <h3 style="color: {{ $isReminder ? '#92400e' : '#166534' }}; margin: 0 0 16px 0;">📋 Invoice Details</h3>
        <table style="width: 100%; border-collapse: collapse;">
            <tr style="border-bottom: 1px solid {{ $isReminder ? '#fde68a' : '#bbf7d0' }};">
                <td style="padding: 12px 0; color: {{ $isReminder ? '#b45309' : '#15803d' }}; font-weight: 600;">Invoice Number:</td>
                <td style="padding: 12px 0; color: {{ $isReminder ? '#92400e' : '#166534' }}; font-weight: 700; text-align: right; font-family: monospace;">#{{ $invoice->invoice_number }}</td>
            </tr>
            <tr style="border-bottom: 1px solid {{ $isReminder ? '#fde68a' : '#bbf7d0' }};">
                <td style="padding: 12px 0; color: {{ $isReminder ? '#b45309' : '#15803d' }}; font-weight: 600;">Invoice Date:</td>
                <td style="padding: 12px 0; color: {{ $isReminder ? '#92400e' : '#166534' }}; font-weight: 700; text-align: right;">{{ $invoice->invoice_date->format('M d, Y') }}</td>
            </tr>
            <tr style="border-bottom: 1px solid {{ $isReminder ? '#fde68a' : '#bbf7d0' }};">
                <td style="padding: 12px 0; color: {{ $isReminder ? '#b45309' : '#15803d' }}; font-weight: 600;">Due Date:</td>
                <td style="padding: 12px 0; color: {{ $isReminder ? '#92400e' : '#166534' }}; font-weight: 700; text-align: right;">{{ $invoice->due_date->format('M d, Y') }}</td>
            </tr>
            <tr style="border-bottom: 1px solid {{ $isReminder ? '#fde68a' : '#bbf7d0' }};">
                <td style="padding: 12px 0; color: {{ $isReminder ? '#b45309' : '#15803d' }}; font-weight: 600;">Amount Due:</td>
                <td style="padding: 12px 0; color: {{ $isReminder ? '#92400e' : '#166534' }}; font-weight: 700; text-align: right; font-size: 20px;">₹{{ number_format($invoice->total_amount, 2) }}</td>
            </tr>
            <tr>
                <td style="padding: 12px 0; color: {{ $isReminder ? '#b45309' : '#15803d' }}; font-weight: 600;">Status:</td>
                <td style="padding: 12px 0; text-align: right;">
                    <span style="background-color: {{ $invoice->status === 'paid' ? '#dcfce7' : ($invoice->status === 'overdue' ? '#fee2e2' : '#fef3c7') }}; 
                                 color: {{ $invoice->status === 'paid' ? '#166534' : ($invoice->status === 'overdue' ? '#dc2626' : '#92400e') }}; 
                                 padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase;">
                        {{ ucfirst($invoice->status) }}
                    </span>
                </td>
            </tr>
        </table>
        
        @if($isReminder && $invoice->due_date->isPast())
            <div style="margin-top: 16px; padding: 12px; background-color: rgba(255, 255, 255, 0.7); border-radius: 8px; border-left: 4px solid #dc2626;">
                <p style="color: #dc2626; font-size: 14px; margin: 0; font-weight: 600;">
                    ⚠️ This invoice is {{ $invoice->due_date->diffForHumans() }} and is now overdue.
                </p>
            </div>
        @endif
    </div>
    
    <!-- Invoice Items Summary -->
    @if($invoice->items && count($invoice->items) > 0)
        <h3>📝 Invoice Items</h3>
        <div style="background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; overflow: hidden; margin: 20px 0;">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background-color: #f1f5f9;">
                        <th style="padding: 16px; text-align: left; color: #374151; font-weight: 600; border-bottom: 1px solid #e5e7eb;">Description</th>
                        <th style="padding: 16px; text-align: center; color: #374151; font-weight: 600; border-bottom: 1px solid #e5e7eb;">Qty</th>
                        <th style="padding: 16px; text-align: right; color: #374151; font-weight: 600; border-bottom: 1px solid #e5e7eb;">Rate</th>
                        <th style="padding: 16px; text-align: right; color: #374151; font-weight: 600; border-bottom: 1px solid #e5e7eb;">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($invoice->items as $item)
                        <tr style="border-bottom: 1px solid #f3f4f6;">
                            <td style="padding: 16px; color: #374151;">{{ $item['description'] ?? $item['name'] }}</td>
                            <td style="padding: 16px; text-align: center; color: #6b7280;">{{ $item['quantity'] ?? 1 }}</td>
                            <td style="padding: 16px; text-align: right; color: #6b7280;">₹{{ number_format($item['rate'] ?? $item['price'], 2) }}</td>
                            <td style="padding: 16px; text-align: right; color: #374151; font-weight: 600;">₹{{ number_format(($item['quantity'] ?? 1) * ($item['rate'] ?? $item['price']), 2) }}</td>
                        </tr>
                    @endforeach
                </tbody>
                <tfoot>
                    <tr style="background-color: #f9fafb; border-top: 2px solid #e5e7eb;">
                        <td colspan="3" style="padding: 16px; text-align: right; color: #374151; font-weight: 600;">Total Amount:</td>
                        <td style="padding: 16px; text-align: right; color: #166534; font-weight: 700; font-size: 18px;">₹{{ number_format($invoice->total_amount, 2) }}</td>
                    </tr>
                </tfoot>
            </table>
        </div>
    @endif
    
    <!-- Payment Instructions -->
    <div class="info-box">
        <h3>💳 How to Pay</h3>
        <p>Making payment is quick and secure. Choose from the options below:</p>
        
        <div style="text-align: center; margin: 24px 0;">
            <a href="{{ route('invoices.pay', $invoice) }}" class="btn btn-primary" style="font-size: 18px; padding: 16px 32px;">
                💳 Pay Now - ₹{{ number_format($invoice->total_amount, 2) }}
            </a>
        </div>
        
        <div style="display: flex; flex-wrap: wrap; gap: 16px; margin: 20px 0;">
            <div style="flex: 1; min-width: 200px; text-align: center; background-color: rgba(16, 185, 129, 0.1); padding: 16px; border-radius: 8px;">
                <div style="color: #10b981; font-size: 24px; margin-bottom: 8px;">🔒</div>
                <h4 style="color: #166534; margin: 0 0 4px 0; font-size: 14px;">Secure Payment</h4>
                <p style="color: #15803d; margin: 0; font-size: 12px;">Bank-level encryption</p>
            </div>
            <div style="flex: 1; min-width: 200px; text-align: center; background-color: rgba(59, 130, 246, 0.1); padding: 16px; border-radius: 8px;">
                <div style="color: #3b82f6; font-size: 24px; margin-bottom: 8px;">⚡</div>
                <h4 style="color: #1e40af; margin: 0 0 4px 0; font-size: 14px;">Instant Processing</h4>
                <p style="color: #2563eb; margin: 0; font-size: 12px;">Immediate confirmation</p>
            </div>
            <div style="flex: 1; min-width: 200px; text-align: center; background-color: rgba(168, 85, 247, 0.1); padding: 16px; border-radius: 8px;">
                <div style="color: #a855f7; font-size: 24px; margin-bottom: 8px;">💳</div>
                <h4 style="color: #7c3aed; margin: 0 0 4px 0; font-size: 14px;">Multiple Options</h4>
                <p style="color: #8b5cf6; margin: 0; font-size: 12px;">Cards, UPI, Net Banking</p>
            </div>
        </div>
        
        <p style="color: #64748b; font-size: 14px; text-align: center; margin-top: 16px;">
            <strong>Payment Link:</strong> <a href="{{ route('invoices.pay', $invoice) }}" style="color: #10b981; text-decoration: none;">{{ route('invoices.pay', $invoice) }}</a>
        </p>
    </div>
    
    <!-- Business Information -->
    <div class="highlight-box">
        <h3>🏢 From: {{ $business_name }}</h3>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="padding: 8px 0; color: #64748b; width: 30%;">Contact Person:</td>
                <td style="padding: 8px 0; color: #1e293b; font-weight: 600;">{{ $sender->name }}</td>
            </tr>
            <tr>
                <td style="padding: 8px 0; color: #64748b;">Email:</td>
                <td style="padding: 8px 0; color: #1e293b; font-weight: 600;">
                    <a href="mailto:{{ $sender->email }}" style="color: #10b981; text-decoration: none;">{{ $sender->email }}</a>
                </td>
            </tr>
            @if($sender->phone)
            <tr>
                <td style="padding: 8px 0; color: #64748b;">Phone:</td>
                <td style="padding: 8px 0; color: #1e293b; font-weight: 600;">{{ $sender->phone }}</td>
            </tr>
            @endif
            @if($sender->business_address)
            <tr>
                <td style="padding: 8px 0; color: #64748b;">Address:</td>
                <td style="padding: 8px 0; color: #1e293b; font-weight: 600;">{{ $sender->business_address }}</td>
            </tr>
            @endif
        </table>
    </div>
    
    @if($invoice->notes)
        <!-- Invoice Notes -->
        <div style="background-color: #f0f9ff; border: 1px solid #bae6fd; border-radius: 12px; padding: 20px; margin: 24px 0;">
            <h3 style="color: #1e40af; margin: 0 0 12px 0;">📝 Additional Notes</h3>
            <p style="color: #2563eb; margin: 0; font-style: italic;">{{ $invoice->notes }}</p>
        </div>
    @endif
    
    <!-- Questions Section -->
    <h3>❓ Questions About This Invoice?</h3>
    <p>If you have any questions about this invoice or need assistance with payment, please don't hesitate to reach out:</p>
    
    <div style="text-align: center; margin: 24px 0;">
        <a href="mailto:{{ $sender->email }}" class="btn btn-outline">
            📧 Contact {{ $sender->name }}
        </a>
        @if($sender->phone)
        <a href="tel:{{ $sender->phone }}" class="btn btn-secondary">
            📞 Call Us
        </a>
        @endif
    </div>
    
    <!-- Payment Terms -->
    @if(!$isReminder)
        <div style="background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px; margin: 24px 0;">
            <h3 style="color: #1e293b; margin: 0 0 12px 0;">📋 Payment Terms</h3>
            <ul style="color: #64748b; margin: 0; padding-left: 20px;">
                <li>Payment is due within {{ $invoice->due_date->diffInDays($invoice->invoice_date) }} days of invoice date</li>
                <li>Late payments may incur additional charges</li>
                <li>All payments are processed securely</li>
                <li>You will receive a confirmation email once payment is received</li>
            </ul>
        </div>
    @endif
    
    <!-- Thank You Message -->
    <div style="text-align: center; margin: 40px 0;">
        @if($isReminder)
            <h3 style="color: #1e293b; margin-bottom: 16px;">Thank You for Your Prompt Attention! 🙏</h3>
            <p style="color: #64748b; font-size: 16px;">
                We appreciate your business and look forward to your payment.
            </p>
        @else
            <h3 style="color: #1e293b; margin-bottom: 16px;">Thank You for Your Business! 🙏</h3>
            <p style="color: #64748b; font-size: 16px;">
                We appreciate the opportunity to work with you and look forward to continuing our partnership.
            </p>
        @endif
    </div>
    
    <!-- Footer Note -->
    <div style="border-top: 1px solid #e2e8f0; padding-top: 20px; margin-top: 32px;">
        <p style="color: #64748b; font-size: 14px; text-align: center; margin: 0;">
            This invoice was generated by {{ config('app.name') }} on behalf of {{ $business_name }}.
            <br>
            For technical support, visit our <a href="#" style="color: #10b981; text-decoration: none;">Help Center</a>.
        </p>
    </div>
@endsection
