<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            $table->string('public_token')->unique()->nullable()->after('pdf_path');
            $table->string('payment_token')->unique()->nullable()->after('public_token');
            $table->timestamp('client_viewed_at')->nullable()->after('payment_token');
            $table->json('payment_attempts')->nullable()->after('client_viewed_at');

            // Add indexes for performance
            $table->index('public_token');
            $table->index('payment_token');
            $table->index('client_viewed_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            $table->dropIndex(['public_token']);
            $table->dropIndex(['payment_token']);
            $table->dropIndex(['client_viewed_at']);
            $table->dropColumn(['public_token', 'payment_token', 'client_viewed_at', 'payment_attempts']);
        });
    }
};
