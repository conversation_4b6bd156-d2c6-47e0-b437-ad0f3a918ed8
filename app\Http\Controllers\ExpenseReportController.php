<?php

namespace App\Http\Controllers;

use App\Models\Expense;
use App\Services\ExpenseService;
use App\Services\ExpenseReportService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class ExpenseReportController extends Controller
{
    protected ExpenseReportService $reportService;
    protected ExpenseService $expenseService;

    public function __construct(ExpenseReportService $reportService, ExpenseService $expenseService)
    {
        $this->reportService = $reportService;
        $this->expenseService = $expenseService;
    }

    /**
     * Display expense reports dashboard
     */
    public function index(Request $request)
    {
        $this->authorize('viewReports', Expense::class);

        $userId = Auth::id();
        $filters = $this->getFilters($request);

        // Get report data
        $reportData = $this->reportService->generateReport($userId, $filters);

        // Get filter options
        $categories = $this->expenseService->getActiveCategories();
        $years = $this->getAvailableYears($userId);

        return view('expenses.reports.index', compact('reportData', 'categories', 'years', 'filters'));
    }

    /**
     * Export expense report
     */
    public function export(Request $request)
    {
        $this->authorize('exportReports', Expense::class);

        $userId = Auth::id();
        $filters = $this->getFilters($request);
        $format = $request->get('format', 'pdf');

        return $this->reportService->exportReport($userId, $filters, $format);
    }

    /**
     * Get tax summary report
     */
    public function taxSummary(Request $request)
    {
        $this->authorize('viewReports', Expense::class);

        $userId = Auth::id();
        $year = $request->get('year', now()->year);

        $taxData = $this->reportService->getTaxSummary($userId, $year);

        return view('expenses.reports.tax-summary', compact('taxData', 'year'));
    }

    /**
     * Export tax summary
     */
    public function exportTaxSummary(Request $request)
    {
        $this->authorize('exportReports', Expense::class);

        $userId = Auth::id();
        $year = $request->get('year', now()->year);
        $format = $request->get('format', 'pdf');

        return $this->reportService->exportTaxSummary($userId, $year, $format);
    }

    /**
     * Get monthly expense comparison
     */
    public function monthlyComparison(Request $request)
    {
        $this->authorize('viewReports', Expense::class);

        $userId = Auth::id();
        $year = $request->get('year', now()->year);

        $comparisonData = $this->reportService->getMonthlyComparison($userId, $year);

        return view('expenses.reports.monthly-comparison', compact('comparisonData', 'year'));
    }

    /**
     * Get category analysis
     */
    public function categoryAnalysis(Request $request)
    {
        $this->authorize('viewReports', Expense::class);

        $userId = Auth::id();
        $period = $request->get('period', 'year');
        
        $analysisData = $this->reportService->getCategoryAnalysis($userId, $period);
        
        return view('expenses.reports.category-analysis', compact('analysisData', 'period'));
    }

    /**
     * Get filters from request
     */
    private function getFilters(Request $request): array
    {
        return [
            'start_date' => $request->get('start_date', now()->startOfMonth()->format('Y-m-d')),
            'end_date' => $request->get('end_date', now()->endOfMonth()->format('Y-m-d')),
            'category_id' => $request->get('category_id'),
            'status' => $request->get('status'),
            'is_billable' => $request->get('is_billable'),
            'is_tax_deductible' => $request->get('is_tax_deductible'),
            'client_id' => $request->get('client_id'),
        ];
    }

    /**
     * Get available years for filtering
     */
    private function getAvailableYears(int $userId): array
    {
        return $this->expenseService->getAvailableYears($userId);
    }
}
