<?php

namespace App\Mail;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class SecurityNotificationMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public User $user;
    public string $eventType;
    public array $eventData;

    /**
     * Create a new message instance.
     */
    public function __construct(User $user, string $eventType, array $eventData = [])
    {
        $this->user = $user;
        $this->eventType = $eventType;
        $this->eventData = $eventData;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $subjects = [
            'password_changed' => 'Password Changed Successfully',
            'email_changed' => 'Email Address Updated',
            'login_from_new_device' => 'New Device Login Detected',
            'suspicious_activity' => 'Suspicious Activity Alert',
            'account_locked' => 'Account Security Lock',
            'two_factor_enabled' => 'Two-Factor Authentication Enabled',
            'two_factor_disabled' => 'Two-Factor Authentication Disabled',
        ];

        return new Envelope(
            subject: ($subjects[$this->eventType] ?? 'Security Notification') . ' - ' . config('app.name'),
            to: [$this->user->email],
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.security-notification',
            with: [
                'user' => $this->user,
                'eventType' => $this->eventType,
                'eventData' => $this->eventData,
                'header_title' => $this->getHeaderTitle(),
                'header_subtitle' => $this->getHeaderSubtitle(),
            ],
        );
    }

    private function getHeaderTitle(): string
    {
        return match($this->eventType) {
            'password_changed' => 'Password Changed',
            'email_changed' => 'Email Updated',
            'login_from_new_device' => 'New Device Login',
            'suspicious_activity' => 'Security Alert',
            'account_locked' => 'Account Locked',
            'two_factor_enabled' => '2FA Enabled',
            'two_factor_disabled' => '2FA Disabled',
            default => 'Security Notification'
        };
    }

    private function getHeaderSubtitle(): string
    {
        return match($this->eventType) {
            'password_changed' => 'Your password has been successfully updated',
            'email_changed' => 'Your email address has been changed',
            'login_from_new_device' => 'We detected a login from a new device',
            'suspicious_activity' => 'Unusual activity detected on your account',
            'account_locked' => 'Your account has been temporarily locked',
            'two_factor_enabled' => 'Two-factor authentication is now active',
            'two_factor_disabled' => 'Two-factor authentication has been disabled',
            default => 'Important security update for your account'
        };
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
