<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreProposalRequest;
use App\Http\Requests\UpdateProposalRequest;
use App\Models\Proposal;
use App\Services\ProposalService;
use App\Services\ClientService;
use App\Services\ProposalTemplateService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProposalController extends Controller
{
    protected ProposalService $proposalService;
    protected ClientService $clientService;
    protected ProposalTemplateService $templateService;

    public function __construct(
        ProposalService $proposalService,
        ClientService $clientService,
        ProposalTemplateService $templateService
    ) {
        $this->proposalService = $proposalService;
        $this->clientService = $clientService;
        $this->templateService = $templateService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $proposals = $this->proposalService->getProposalsForUser(Auth::id(), $request);
        $clients = $this->clientService->getClientsForSelect(Auth::id());
        $stats = $this->proposalService->getProposalStats(Auth::id());

        // Check if user is at proposal limit
        $isAtLimit = !$this->proposalService->canCreateProposal(Auth::id());

        return view('proposals.index', compact('proposals', 'clients', 'stats', 'isAtLimit'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        // Check if user can create proposals
        if (!$this->proposalService->canCreateProposal(Auth::id())) {
            return redirect()->route('proposals.upgrade')
                ->with('error', 'You have reached your proposal limit. Upgrade your plan for more proposals.');
        }

        $clients = $this->clientService->getClientsForSelect(Auth::id());
        $templates = $this->templateService->getTemplatesForUser(Auth::id());
        
        $selectedTemplate = null;
        if ($request->filled('template_id')) {
            $selectedTemplate = $templates->where('id', $request->template_id)->first();
        }

        return view('proposals.create', compact('clients', 'templates', 'selectedTemplate'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreProposalRequest $request)
    {
        // Check if user can create proposals
        if (!$this->proposalService->canCreateProposal(Auth::id())) {
            return redirect()->route('proposals.upgrade')
                ->with('error', 'You have reached your proposal limit. Upgrade your plan for more proposals.');
        }

        $validated = $request->validated();
        $proposal = $this->proposalService->createProposal($validated);

        return redirect()->route('proposals.show', $proposal)
                        ->with('success', 'Proposal created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Proposal $proposal)
    {
        $this->authorize('view', $proposal);

        $proposal->load(['client', 'proposalTemplate', 'projects', 'contracts']);

        return view('proposals.show', compact('proposal'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Proposal $proposal)
    {
        $this->authorize('update', $proposal);

        if (!$proposal->canBeEdited()) {
            return redirect()->route('proposals.show', $proposal)
                ->with('error', 'This proposal cannot be edited in its current state.');
        }

        $clients = $this->clientService->getClientsForSelect(Auth::id());
        $templates = $this->templateService->getTemplatesForUser(Auth::id());

        return view('proposals.edit', compact('proposal', 'clients', 'templates'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateProposalRequest $request, Proposal $proposal)
    {
        $this->authorize('update', $proposal);

        if (!$proposal->canBeEdited()) {
            return redirect()->route('proposals.show', $proposal)
                ->with('error', 'This proposal cannot be edited in its current state.');
        }

        $validated = $request->validated();
        $this->proposalService->updateProposal($proposal, $validated);

        return redirect()->route('proposals.show', $proposal)
                        ->with('success', 'Proposal updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Proposal $proposal)
    {
        $this->authorize('delete', $proposal);

        $result = $this->proposalService->deleteProposal($proposal);

        if (!$result['success']) {
            return redirect()->route('proposals.index')
                            ->with('error', $result['message']);
        }

        return redirect()->route('proposals.index')
                        ->with('success', 'Proposal deleted successfully.');
    }

    /**
     * Send proposal to client.
     */
    public function send(Proposal $proposal)
    {
        $this->authorize('update', $proposal);

        $result = $this->proposalService->sendProposal($proposal);

        if (!$result['success']) {
            return redirect()->route('proposals.show', $proposal)
                            ->with('error', $result['message']);
        }

        return redirect()->route('proposals.show', $proposal)
                        ->with('success', 'Proposal sent successfully.');
    }

    /**
     * Accept proposal (client action).
     */
    public function accept(Request $request, Proposal $proposal)
    {
        $result = $this->proposalService->acceptProposal($proposal, $request->all());

        if (!$result['success']) {
            return redirect()->route('proposals.show', $proposal)
                            ->with('error', $result['message']);
        }

        return redirect()->route('proposals.show', $proposal)
                        ->with('success', 'Proposal accepted successfully.');
    }

    /**
     * Reject proposal (client action).
     */
    public function reject(Request $request, Proposal $proposal)
    {
        $result = $this->proposalService->rejectProposal($proposal, $request->all());

        if (!$result['success']) {
            return redirect()->route('proposals.show', $proposal)
                            ->with('error', $result['message']);
        }

        return redirect()->route('proposals.show', $proposal)
                        ->with('success', 'Proposal rejected.');
    }

    /**
     * Create revision of proposal.
     */
    public function createRevision(Request $request, Proposal $proposal)
    {
        $this->authorize('update', $proposal);

        $revision = $this->proposalService->createRevision($proposal, $request->all());

        return redirect()->route('proposals.edit', $revision)
                        ->with('success', 'Proposal revision created successfully.');
    }

    /**
     * Show convert to project form.
     */
    public function showConvertToProject(Proposal $proposal)
    {
        $this->authorize('update', $proposal);

        if (!$proposal->canBeConverted()) {
            return redirect()->route('proposals.show', $proposal)
                ->with('error', 'This proposal cannot be converted to a project.');
        }

        $clients = auth()->user()->clients()->orderBy('name')->get();

        return view('proposals.convert-to-project', compact('proposal', 'clients'));
    }

    /**
     * Convert proposal to project.
     */
    public function convertToProject(Request $request, Proposal $proposal)
    {
        $this->authorize('update', $proposal);

        $result = $this->proposalService->convertToProject($proposal, $request->all());

        if (!$result['success']) {
            return redirect()->route('proposals.show', $proposal)
                            ->with('error', $result['message']);
        }

        return redirect()->route('projects.show', $result['project'])
                        ->with('success', 'Proposal converted to project successfully.');
    }

    /**
     * Show convert to contract form.
     */
    public function showConvertToContract(Proposal $proposal)
    {
        $this->authorize('update', $proposal);

        if (!$proposal->canBeConverted()) {
            return redirect()->route('proposals.show', $proposal)
                ->with('error', 'This proposal cannot be converted to a contract.');
        }

        $clients = auth()->user()->clients()->orderBy('name')->get();

        return view('proposals.convert-to-contract', compact('proposal', 'clients'));
    }

    /**
     * Convert proposal to contract.
     */
    public function convertToContract(Request $request, Proposal $proposal)
    {
        $this->authorize('update', $proposal);

        $result = $this->proposalService->convertToContract($proposal, $request->all());

        if (!$result['success']) {
            return redirect()->route('proposals.show', $proposal)
                            ->with('error', $result['message']);
        }

        return redirect()->route('contracts.show', $result['contract'])
                        ->with('success', 'Proposal converted to contract successfully.');
    }

    /**
     * Mark proposal as viewed (public endpoint for client).
     */
    public function markViewed(Proposal $proposal)
    {
        $this->proposalService->markAsViewed($proposal);
        
        return response()->json(['success' => true]);
    }

    /**
     * Get proposal data for AJAX requests.
     */
    public function getData(Proposal $proposal)
    {
        $this->authorize('view', $proposal);

        return response()->json([
            'proposal' => $proposal->load(['client', 'proposalTemplate']),
            'stats' => $this->proposalService->getProposalStats(Auth::id())
        ]);
    }

    /**
     * Public view for clients to view proposals.
     */
    public function publicView(Proposal $proposal, $token)
    {
        if ($proposal->public_token !== $token) {
            abort(404);
        }

        // Mark as viewed if not already
        if ($proposal->status === 'sent') {
            $proposal->update(['status' => 'viewed']);
        }

        return view('proposals.public', compact('proposal'));
    }

    /**
     * Public accept proposal.
     */
    public function publicAccept(Request $request, Proposal $proposal, $token)
    {
        if ($proposal->public_token !== $token) {
            abort(404);
        }

        $proposal->update([
            'status' => 'accepted',
            'accepted_at' => now(),
        ]);

        return redirect()->route('proposals.public', [$proposal, $token])
            ->with('success', 'Proposal accepted successfully!');
    }

    /**
     * Public reject proposal.
     */
    public function publicReject(Request $request, Proposal $proposal, $token)
    {
        if ($proposal->public_token !== $token) {
            abort(404);
        }

        $proposal->update([
            'status' => 'rejected',
            'rejected_at' => now(),
        ]);

        return redirect()->route('proposals.public', [$proposal, $token])
            ->with('info', 'Proposal has been declined.');
    }
}
