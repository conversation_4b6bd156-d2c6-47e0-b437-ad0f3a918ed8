<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class Expense extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'category_id',
        'client_id',
        'project_id',
        'expense_number',
        'amount',
        'description',
        'notes',
        'expense_date',
        'tax_amount',
        'tax_percentage',
        'is_billable',
        'is_billed',
        'receipt_path',
        'status',
        'payment_method',
        'vendor_name',
        'reference_number',
        'approved_at',
        'approved_by',
    ];

    protected function casts(): array
    {
        return [
            'expense_date' => 'date',
            'amount' => 'float',
            'tax_amount' => 'float',
            'tax_percentage' => 'float',
            'is_billable' => 'boolean',
            'is_billed' => 'boolean',
            'approved_at' => 'datetime',
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($expense) {
            if (empty($expense->expense_number)) {
                $expense->expense_number = static::generateExpenseNumber();
            }
        });
    }

    /**
     * Generate a unique expense number.
     */
    public static function generateExpenseNumber(): string
    {
        $prefix = 'EXP';
        $year = now()->format('Y');
        $month = now()->format('m');

        // Get the last expense number for this month
        $lastExpense = static::whereRaw("expense_number LIKE '{$prefix}-{$year}{$month}-%'")
            ->orderBy('expense_number', 'desc')
            ->first();

        if ($lastExpense) {
            $lastNumber = (int) substr($lastExpense->expense_number, -4);
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }

        return "{$prefix}-{$year}{$month}-{$newNumber}";
    }

    /**
     * Get the user that owns the expense.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the category for the expense.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(ExpenseCategory::class, 'category_id');
    }

    /**
     * Get the client for the expense.
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Get the project for the expense.
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the user who approved the expense.
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Scope a query to only include expenses for a specific user.
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope a query to only include billable expenses.
     */
    public function scopeBillable($query)
    {
        return $query->where('is_billable', true);
    }

    /**
     * Scope a query to only include unbilled expenses.
     */
    public function scopeUnbilled($query)
    {
        return $query->where('is_billable', true)->where('is_billed', false);
    }

    /**
     * Scope a query to filter by status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to filter by date range.
     */
    public function scopeDateRange($query, string $startDate, string $endDate)
    {
        return $query->whereBetween('expense_date', [$startDate, $endDate]);
    }

    /**
     * Scope for optimized listing queries.
     */
    public function scopeForListing($query)
    {
        return $query->select([
            'id', 'user_id', 'category_id', 'client_id', 'project_id',
            'expense_number', 'amount', 'description', 'expense_date',
            'status', 'is_billable', 'is_billed', 'created_at'
        ])->with([
            'category:id,name,icon,color',
            'client:id,name,company_name',
            'project:id,name'
        ]);
    }

    /**
     * Scope for dashboard queries.
     */
    public function scopeForDashboard($query)
    {
        return $query->select([
            'id', 'amount', 'expense_date', 'status', 'category_id', 'created_at'
        ])->with('category:id,name,icon');
    }

    /**
     * Get the total amount including tax.
     */
    public function getTotalAmountAttribute(): float
    {
        return $this->amount + $this->tax_amount;
    }

    /**
     * Get formatted amount with currency.
     */
    public function getFormattedAmountAttribute(): string
    {
        return '$' . number_format($this->amount, 2);
    }

    /**
     * Get formatted total amount with currency.
     */
    public function getFormattedTotalAmountAttribute(): string
    {
        return '$' . number_format($this->total_amount, 2);
    }

    /**
     * Check if expense can be edited.
     */
    public function canBeEdited(): bool
    {
        return in_array($this->status, ['draft', 'rejected']);
    }

    /**
     * Check if expense can be deleted.
     */
    public function canBeDeleted(): bool
    {
        return in_array($this->status, ['draft', 'rejected']);
    }

    /**
     * Check if expense can be approved.
     */
    public function canBeApproved(): bool
    {
        return $this->status === 'submitted';
    }
}
