<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('proposals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('client_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('proposal_template_id')->nullable()->constrained()->onDelete('set null');
            $table->string('title');
            $table->text('description')->nullable();
            $table->longText('content');
            $table->json('sections')->nullable(); // For structured proposal sections
            $table->enum('status', ['draft', 'sent', 'viewed', 'accepted', 'rejected', 'expired'])->default('draft');
            $table->decimal('total_amount', 12, 2)->nullable();
            $table->string('currency', 3)->default('USD');
            $table->date('valid_until')->nullable();
            $table->date('sent_date')->nullable();
            $table->date('viewed_date')->nullable();
            $table->date('responded_date')->nullable();
            $table->text('client_notes')->nullable();
            $table->text('internal_notes')->nullable();
            $table->json('custom_fields')->nullable();
            $table->string('pdf_path')->nullable();
            $table->string('proposal_number')->unique();
            $table->integer('version')->default(1);
            $table->foreignId('parent_proposal_id')->nullable()->constrained('proposals')->onDelete('cascade');
            $table->boolean('is_template')->default(false);
            $table->timestamps();

            // Indexes for performance
            $table->index(['user_id', 'status']);
            $table->index(['client_id', 'status']);
            $table->index('sent_date');
            $table->index('valid_until');
            $table->index('proposal_number');
            $table->index(['status', 'sent_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('proposals');
    }
};
