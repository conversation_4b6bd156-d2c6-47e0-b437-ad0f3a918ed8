@extends('emails.layouts.app')

@section('content')
    <!-- Payment Issue Message -->
    <div style="text-align: center; margin-bottom: 32px;">
        <div style="font-size: 64px; margin-bottom: 16px;">⚠️</div>
        <h2 style="color: #dc2626; margin-bottom: 8px;">Payment Issue</h2>
        <p class="lead" style="color: #ef4444;">
            Hi {{ $user->name }}, we encountered an issue processing your payment for the {{ $planName }} plan. Don't worry - no charges were made to your account.
        </p>
    </div>
    
    <!-- Payment Details -->
    <div class="warning-box">
        <h3 style="color: #92400e; margin: 0 0 16px 0;">📄 Payment Attempt Details</h3>
        <table style="width: 100%; border-collapse: collapse;">
            <tr style="border-bottom: 1px solid #fde68a;">
                <td style="padding: 12px 0; color: #b45309; font-weight: 600;">Plan:</td>
                <td style="padding: 12px 0; color: #92400e; font-weight: 700; text-align: right;">{{ $planName }}</td>
            </tr>
            <tr style="border-bottom: 1px solid #fde68a;">
                <td style="padding: 12px 0; color: #b45309; font-weight: 600;">Amount:</td>
                <td style="padding: 12px 0; color: #92400e; font-weight: 700; text-align: right;">₹{{ $paymentDetails['amount'] ?? '199.00' }}</td>
            </tr>
            <tr style="border-bottom: 1px solid #fde68a;">
                <td style="padding: 12px 0; color: #b45309; font-weight: 600;">Attempt Date:</td>
                <td style="padding: 12px 0; color: #92400e; font-weight: 700; text-align: right;">{{ now()->format('M d, Y \a\t g:i A') }}</td>
            </tr>
            <tr>
                <td style="padding: 12px 0; color: #b45309; font-weight: 600;">Status:</td>
                <td style="padding: 12px 0; color: #dc2626; font-weight: 700; text-align: right;">{{ $paymentDetails['status'] ?? 'Failed' }}</td>
            </tr>
        </table>
        
        <div style="margin-top: 16px; padding: 12px; background-color: rgba(255, 255, 255, 0.7); border-radius: 8px;">
            <p style="color: #b45309; font-size: 14px; margin: 0; text-align: center;">
                <strong>Error:</strong> {{ $paymentDetails['error_message'] ?? 'Payment could not be processed. Please check your payment method and try again.' }}
            </p>
        </div>
    </div>
    
    <!-- Common Reasons -->
    <h3>🔍 Common Reasons for Payment Issues</h3>
    <p>Here are the most common reasons why payments fail and how to fix them:</p>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 16px; margin: 24px 0;">
        <div style="background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%); padding: 20px; border-radius: 12px; border: 1px solid #fecaca;">
            <div style="color: #dc2626; font-size: 24px; margin-bottom: 8px;">💳</div>
            <h4 style="margin: 0 0 8px 0; color: #dc2626; font-size: 16px;">Insufficient Funds</h4>
            <p style="margin: 0; color: #ef4444; font-size: 14px;">Your card may not have enough balance. Check with your bank or try a different card.</p>
        </div>
        
        <div style="background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%); padding: 20px; border-radius: 12px; border: 1px solid #fde68a;">
            <div style="color: #f59e0b; font-size: 24px; margin-bottom: 8px;">📅</div>
            <h4 style="margin: 0 0 8px 0; color: #f59e0b; font-size: 16px;">Expired Card</h4>
            <p style="margin: 0; color: #f59e0b; font-size: 14px;">Check your card's expiration date and update your payment method if needed.</p>
        </div>
        
        <div style="background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%); padding: 20px; border-radius: 12px; border: 1px solid #bae6fd;">
            <div style="color: #3b82f6; font-size: 24px; margin-bottom: 8px;">🛡️</div>
            <h4 style="margin: 0 0 8px 0; color: #3b82f6; font-size: 16px;">Security Block</h4>
            <p style="margin: 0; color: #3b82f6; font-size: 14px;">Your bank may have blocked the transaction for security. Contact them to authorize the payment.</p>
        </div>
        
        <div style="background: linear-gradient(135deg, #fdf4ff 0%, #fae8ff 100%); padding: 20px; border-radius: 12px; border: 1px solid #f5d0fe;">
            <div style="color: #a855f7; font-size: 24px; margin-bottom: 8px;">ℹ️</div>
            <h4 style="margin: 0 0 8px 0; color: #a855f7; font-size: 16px;">Incorrect Details</h4>
            <p style="margin: 0; color: #a855f7; font-size: 14px;">Double-check your card number, CVV, and billing address for any typos.</p>
        </div>
    </div>
    
    <!-- Quick Solutions -->
    <div class="info-box">
        <h3>✅ Quick Solutions</h3>
        <p>Here's what you can do right now to resolve this issue:</p>
        
        <div style="display: flex; flex-wrap: wrap; gap: 16px; margin: 20px 0;">
            <div style="flex: 1; min-width: 250px;">
                <h4 style="color: #1e293b; margin: 0 0 8px 0; font-size: 16px;">1. Try Again</h4>
                <p style="color: #64748b; margin: 0 0 12px 0; font-size: 14px;">Sometimes payment issues are temporary. Try processing the payment again.</p>
                <a href="{{ route('subscriptions.plans') }}" class="btn btn-primary" style="font-size: 14px; padding: 12px 20px;">
                    Retry Payment
                </a>
            </div>
            
            <div style="flex: 1; min-width: 250px;">
                <h4 style="color: #1e293b; margin: 0 0 8px 0; font-size: 16px;">2. Different Payment Method</h4>
                <p style="color: #64748b; margin: 0 0 12px 0; font-size: 14px;">Use a different credit card, debit card, or try PayPal if available.</p>
                <a href="{{ route('subscriptions.plans') }}" class="btn btn-outline" style="font-size: 14px; padding: 12px 20px;">
                    Change Method
                </a>
            </div>
        </div>
    </div>
    
    <!-- Alternative Options -->
    <h3>🔄 Alternative Options</h3>
    <p>If you continue to experience issues, here are some alternatives:</p>
    
    <div style="background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); border: 1px solid #bbf7d0; border-radius: 12px; padding: 24px; margin: 24px 0;">
        <div style="display: flex; align-items: center; margin-bottom: 16px;">
            <div style="color: #166534; font-size: 32px; margin-right: 16px;">🎁</div>
            <div>
                <h4 style="color: #166534; margin: 0 0 4px 0; font-size: 18px;">Continue with Free Plan</h4>
                <p style="color: #15803d; margin: 0; font-size: 14px;">You can always upgrade later when you're ready</p>
            </div>
        </div>
        
        <div style="display: flex; flex-wrap: wrap; gap: 12px;">
            <a href="{{ route('dashboard') }}" class="btn btn-secondary" style="background-color: #166534; color: white;">
                Continue Free
            </a>
            <a href="#" class="btn btn-outline" style="border-color: #166534; color: #166534;">
                Contact Support
            </a>
        </div>
    </div>
    
    <!-- Payment Security -->
    <div style="background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); border: 1px solid #e2e8f0; border-radius: 12px; padding: 24px; margin: 24px 0;">
        <h3 style="color: #1e293b; margin: 0 0 16px 0;">🔒 Your Security is Our Priority</h3>
        <div style="display: flex; flex-wrap: wrap; gap: 20px;">
            <div style="flex: 1; min-width: 200px; text-align: center;">
                <div style="color: #10b981; font-size: 32px; margin-bottom: 8px;">🛡️</div>
                <h4 style="color: #1e293b; margin: 0 0 4px 0; font-size: 14px;">Bank-Level Security</h4>
                <p style="color: #64748b; margin: 0; font-size: 12px;">256-bit SSL encryption</p>
            </div>
            <div style="flex: 1; min-width: 200px; text-align: center;">
                <div style="color: #10b981; font-size: 32px; margin-bottom: 8px;">🔐</div>
                <h4 style="color: #1e293b; margin: 0 0 4px 0; font-size: 14px;">PCI Compliant</h4>
                <p style="color: #64748b; margin: 0; font-size: 12px;">Industry standard protection</p>
            </div>
            <div style="flex: 1; min-width: 200px; text-align: center;">
                <div style="color: #10b981; font-size: 32px; margin-bottom: 8px;">💳</div>
                <h4 style="color: #1e293b; margin: 0 0 4px 0; font-size: 14px;">No Data Stored</h4>
                <p style="color: #64748b; margin: 0; font-size: 12px;">We never store card details</p>
            </div>
        </div>
    </div>
    
    <!-- Support Section -->
    <div style="text-align: center; background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; padding: 32px; margin: 32px 0;">
        <div style="font-size: 48px; margin-bottom: 16px;">🎧</div>
        <h3 style="color: #1e293b; margin: 0 0 12px 0;">Need Personal Assistance?</h3>
        <p style="color: #64748b; margin: 0 0 24px 0;">
            Our support team is here to help you resolve any payment issues. We typically respond within 2 hours during business hours.
        </p>
        
        <div style="display: flex; flex-wrap: wrap; gap: 12px; justify-content: center; margin-bottom: 20px;">
            <a href="mailto:support@{{ config('app.domain', 'freeligo.com') }}" class="btn btn-primary">
                📧 Email Support
            </a>
            <a href="#" class="btn btn-outline">
                💬 Live Chat
            </a>
        </div>
        
        <p style="color: #64748b; font-size: 14px; margin: 0;">
            <strong>Support Hours:</strong> Monday - Friday, 9:00 AM - 6:00 PM IST
        </p>
    </div>
    
    <!-- Reassurance Message -->
    <div style="text-align: center; margin: 40px 0;">
        <h3 style="color: #1e293b; margin-bottom: 16px;">Don't Worry - We've Got You Covered! 💪</h3>
        <p style="color: #64748b; font-size: 16px; margin-bottom: 20px;">
            Payment issues happen to everyone. We're here to help you get back on track quickly and securely.
        </p>
        <p style="color: #64748b; font-size: 14px;">
            Thank you for choosing {{ config('app.name') }}. We appreciate your patience!
        </p>
    </div>
    
    <!-- Footer Note -->
    <div style="border-top: 1px solid #e2e8f0; padding-top: 20px; margin-top: 32px;">
        <p style="color: #64748b; font-size: 14px; text-align: center; margin: 0;">
            If you didn't attempt to make this payment, please 
            <a href="mailto:security@{{ config('app.domain', 'freeligo.com') }}" style="color: #dc2626; text-decoration: none;">contact our security team</a> immediately.
        </p>
    </div>
@endsection
