<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\FollowUp;
use App\Models\Client;
use App\Services\AIServiceFactory;
use App\Services\NotificationService;
use App\Helpers\PlanChecker;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class SmartPaymentReminderService
{
    protected $aiService;
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->aiService = AIServiceFactory::create();
        $this->notificationService = $notificationService;
    }

    /**
     * Process smart payment reminders for all overdue invoices.
     */
    public function processSmartReminders(): array
    {
        $results = [
            'reminders_created' => 0,
            'multi_channel_sent' => 0,
            'ai_optimized' => 0,
            'failed' => 0,
            'errors' => []
        ];

        $overdueInvoices = $this->getInvoicesNeedingReminders();

        foreach ($overdueInvoices as $invoice) {
            try {
                $reminderStrategy = $this->determineOptimalReminderStrategy($invoice);
                $followUp = $this->createEnhancedSmartReminder($invoice, $reminderStrategy);

                $results['reminders_created']++;

                if (count($reminderStrategy['channels']) > 1) {
                    $results['multi_channel_sent']++;
                }

                if ($reminderStrategy['ai_optimized']) {
                    $results['ai_optimized']++;
                }

                // Update client behavior analytics
                $this->updateClientBehaviorAnalytics($invoice->client, $followUp);

            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = [
                    'invoice_id' => $invoice->id,
                    'error' => $e->getMessage()
                ];
                Log::error('Failed to create smart reminder', [
                    'invoice_id' => $invoice->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $results;
    }

    /**
     * Get invoices that need reminders.
     */
    protected function getInvoicesNeedingReminders()
    {
        return Invoice::with(['client', 'followUps'])
            ->where('status', '!=', 'paid')
            ->where('due_date', '<', now())
            ->whereDoesntHave('followUps', function ($query) {
                $query->where('created_at', '>', now()->subDays(3))
                      ->where('status', '!=', 'failed');
            })
            ->get();
    }

    /**
     * Determine the appropriate reminder type based on invoice history.
     */
    protected function determineReminderType(Invoice $invoice): string
    {
        $daysOverdue = now()->diffInDays($invoice->due_date);
        $previousReminders = $invoice->followUps()->count();
        $clientPaymentHistory = $this->analyzeClientPaymentHistory($invoice->client);

        // AI-powered reminder type determination
        if (PlanChecker::canUseAiAssistant()) {
            return $this->getAIReminderType($invoice, $daysOverdue, $previousReminders, $clientPaymentHistory);
        }

        // Fallback logic
        if ($daysOverdue <= 7 && $previousReminders === 0) {
            return 'friendly';
        } elseif ($daysOverdue <= 30 && $previousReminders <= 2) {
            return 'reminder';
        } else {
            return 'legal_notice';
        }
    }

    /**
     * Get AI-powered reminder type recommendation.
     */
    protected function getAIReminderType(Invoice $invoice, int $daysOverdue, int $previousReminders, array $paymentHistory): string
    {
        try {
            $context = [
                'days_overdue' => $daysOverdue,
                'previous_reminders' => $previousReminders,
                'client_payment_reliability' => $paymentHistory['reliability_score'],
                'average_payment_delay' => $paymentHistory['avg_payment_delay'],
                'invoice_amount' => $invoice->total_amount,
                'client_relationship_duration' => $paymentHistory['relationship_months'],
            ];

            $result = $this->aiService->generateCompletion(
                $this->buildReminderTypePrompt($context),
                ['max_tokens' => 50, 'temperature' => 0.3]
            );

            if ($result['success']) {
                $recommendation = strtolower(trim($result['content']));
                if (in_array($recommendation, ['friendly', 'reminder', 'legal_notice'])) {
                    return $recommendation;
                }
            }
        } catch (\Exception $e) {
            Log::warning('AI reminder type determination failed', [
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage()
            ]);
        }

        // Fallback to rule-based logic
        return $daysOverdue <= 7 ? 'friendly' : ($daysOverdue <= 30 ? 'reminder' : 'legal_notice');
    }

    /**
     * Create a smart reminder with AI-generated content.
     */
    protected function createSmartReminder(Invoice $invoice, string $reminderType): FollowUp
    {
        $message = $this->generateReminderMessage($invoice, $reminderType);
        $method = $this->determineOptimalMethod($invoice->client);
        $scheduledAt = $this->calculateOptimalSendTime($invoice->client);

        return FollowUp::create([
            'user_id' => $invoice->user_id,
            'invoice_id' => $invoice->id,
            'type' => $reminderType,
            'message' => $message,
            'method' => $method,
            'status' => 'pending',
            'scheduled_at' => $scheduledAt,
        ]);
    }

    /**
     * Generate AI-powered reminder message.
     */
    protected function generateReminderMessage(Invoice $invoice, string $reminderType): string
    {
        if (PlanChecker::canUseAiAssistant()) {
            try {
                $context = [
                    'client_name' => $invoice->client?->name ?? 'Client',
                    'invoice_number' => $invoice->invoice_number,
                    'amount' => $invoice->total_amount,
                    'days_overdue' => now()->diffInDays($invoice->due_date),
                    'type' => $reminderType,
                    'relationship' => 'professional',
                ];

                $result = $this->aiService->generateFollowUpMessage($context);
                
                if ($result['success']) {
                    return $result['content'];
                }
            } catch (\Exception $e) {
                Log::warning('AI message generation failed', [
                    'invoice_id' => $invoice->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Fallback templates
        return $this->getFallbackMessage($invoice, $reminderType);
    }

    /**
     * Analyze client payment history for smart decisions.
     */
    protected function analyzeClientPaymentHistory($client): array
    {
        $paidInvoices = $client->invoices()->where('status', 'paid')->get();
        
        if ($paidInvoices->isEmpty()) {
            return [
                'reliability_score' => 50,
                'avg_payment_delay' => 0,
                'relationship_months' => 0,
            ];
        }

        $paymentDelays = $paidInvoices->map(function ($invoice) {
            return $invoice->paid_date ? 
                Carbon::parse($invoice->paid_date)->diffInDays($invoice->due_date) : 0;
        });

        return [
            'reliability_score' => $this->calculateReliabilityScore($paymentDelays),
            'avg_payment_delay' => $paymentDelays->avg(),
            'relationship_months' => now()->diffInMonths($client->created_at),
        ];
    }

    /**
     * Calculate client reliability score.
     */
    protected function calculateReliabilityScore($paymentDelays): int
    {
        $onTimePayments = $paymentDelays->filter(fn($delay) => $delay <= 3)->count();
        $totalPayments = $paymentDelays->count();
        
        return $totalPayments > 0 ? (int) (($onTimePayments / $totalPayments) * 100) : 50;
    }

    /**
     * Determine optimal communication method using AI and client behavior.
     */
    protected function determineOptimalMethod($client): string
    {
        // Analyze client communication preferences
        $communicationHistory = $this->analyzeClientCommunicationHistory($client);

        // Use AI to determine best method if available
        if (PlanChecker::canUseAiAssistant()) {
            $optimalMethod = $this->getAIOptimalMethod($client, $communicationHistory);
            if ($optimalMethod) {
                return $optimalMethod;
            }
        }

        // Fallback logic based on response rates
        if ($communicationHistory['whatsapp_response_rate'] > $communicationHistory['email_response_rate'] && $client->phone) {
            return 'whatsapp';
        } elseif ($communicationHistory['sms_response_rate'] > $communicationHistory['email_response_rate'] && $client->phone) {
            return 'sms';
        }

        return 'email'; // Default fallback
    }

    /**
     * Calculate optimal send time based on client timezone and behavior.
     */
    protected function calculateOptimalSendTime($client): Carbon
    {
        // Send during business hours (9 AM - 5 PM)
        $sendTime = now()->addHours(2);
        
        if ($sendTime->hour < 9) {
            $sendTime->setTime(9, 0);
        } elseif ($sendTime->hour > 17) {
            $sendTime->addDay()->setTime(9, 0);
        }

        return $sendTime;
    }

    /**
     * Build prompt for AI reminder type determination.
     */
    protected function buildReminderTypePrompt(array $context): string
    {
        return "Based on the following invoice data, recommend the most appropriate reminder type (friendly, reminder, or legal_notice):\n" .
               "Days overdue: {$context['days_overdue']}\n" .
               "Previous reminders: {$context['previous_reminders']}\n" .
               "Client reliability score: {$context['client_payment_reliability']}%\n" .
               "Average payment delay: {$context['average_payment_delay']} days\n" .
               "Invoice amount: ₹{$context['invoice_amount']}\n" .
               "Respond with only one word: friendly, reminder, or legal_notice";
    }

    /**
     * Get fallback message templates.
     */
    protected function getFallbackMessage(Invoice $invoice, string $reminderType): string
    {
        $templates = [
            'friendly' => "Hi {$invoice->client->name}, just a friendly reminder that invoice {$invoice->invoice_number} for ₹{$invoice->total_amount} was due on {$invoice->due_date->format('M d, Y')}. Please let us know if you have any questions!",
            'reminder' => "Dear {$invoice->client->name}, this is a reminder that invoice {$invoice->invoice_number} for ₹{$invoice->total_amount} is now overdue. Please arrange payment at your earliest convenience.",
            'legal_notice' => "Dear {$invoice->client->name}, invoice {$invoice->invoice_number} for ₹{$invoice->total_amount} remains unpaid despite previous reminders. Please settle this immediately to avoid further action.",
        ];

        return $templates[$reminderType] ?? $templates['reminder'];
    }

    /**
     * Analyze client communication history for optimal method selection.
     */
    protected function analyzeClientCommunicationHistory($client): array
    {
        $followUps = $client->invoices()
            ->with('followUps')
            ->get()
            ->pluck('followUps')
            ->flatten();

        $emailFollowUps = $followUps->where('method', 'email');
        $whatsappFollowUps = $followUps->where('method', 'whatsapp');
        $smsFollowUps = $followUps->where('method', 'sms');

        return [
            'email_response_rate' => $this->calculateResponseRate($emailFollowUps),
            'whatsapp_response_rate' => $this->calculateResponseRate($whatsappFollowUps),
            'sms_response_rate' => $this->calculateResponseRate($smsFollowUps),
            'total_followups' => $followUps->count(),
            'preferred_time' => $this->getPreferredContactTime($followUps)
        ];
    }

    /**
     * Calculate response rate for a communication method.
     */
    protected function calculateResponseRate($followUps): float
    {
        if ($followUps->isEmpty()) {
            return 0.0;
        }

        $responded = $followUps->filter(function ($followUp) {
            // Check if payment was made within 7 days of reminder
            return $followUp->invoice->status === 'paid' &&
                   $followUp->invoice->paid_date &&
                   $followUp->sent_at &&
                   Carbon::parse($followUp->invoice->paid_date)->diffInDays($followUp->sent_at) <= 7;
        })->count();

        return ($responded / $followUps->count()) * 100;
    }

    /**
     * Get AI-powered optimal communication method.
     */
    protected function getAIOptimalMethod($client, array $communicationHistory): ?string
    {
        try {
            $context = [
                'client_name' => $client->name,
                'email_response_rate' => $communicationHistory['email_response_rate'],
                'whatsapp_response_rate' => $communicationHistory['whatsapp_response_rate'],
                'sms_response_rate' => $communicationHistory['sms_response_rate'],
                'total_interactions' => $communicationHistory['total_followups'],
                'has_phone' => !empty($client->phone),
                'has_email' => !empty($client->email)
            ];

            $prompt = $this->buildOptimalMethodPrompt($context);
            $result = $this->aiService->generateCompletion($prompt, [
                'max_tokens' => 20,
                'temperature' => 0.2
            ]);

            if ($result['success']) {
                $method = strtolower(trim($result['content']));
                if (in_array($method, ['email', 'whatsapp', 'sms'])) {
                    return $method;
                }
            }
        } catch (\Exception $e) {
            Log::warning('AI optimal method determination failed', [
                'client_id' => $client->id,
                'error' => $e->getMessage()
            ]);
        }

        return null;
    }

    /**
     * Build prompt for AI optimal method determination.
     */
    protected function buildOptimalMethodPrompt(array $context): string
    {
        return "Based on the following client communication data, recommend the most effective contact method (email, whatsapp, or sms):\n" .
               "Email response rate: {$context['email_response_rate']}%\n" .
               "WhatsApp response rate: {$context['whatsapp_response_rate']}%\n" .
               "SMS response rate: {$context['sms_response_rate']}%\n" .
               "Total interactions: {$context['total_interactions']}\n" .
               "Has phone: " . ($context['has_phone'] ? 'Yes' : 'No') . "\n" .
               "Has email: " . ($context['has_email'] ? 'Yes' : 'No') . "\n" .
               "Respond with only one word: email, whatsapp, or sms";
    }

    /**
     * Get preferred contact time based on historical data.
     */
    protected function getPreferredContactTime($followUps): int
    {
        if ($followUps->isEmpty()) {
            return 10; // Default to 10 AM
        }

        $successfulFollowUps = $followUps->filter(function ($followUp) {
            return $followUp->invoice->status === 'paid' &&
                   $followUp->invoice->paid_date &&
                   $followUp->sent_at &&
                   Carbon::parse($followUp->invoice->paid_date)->diffInDays($followUp->sent_at) <= 3;
        });

        if ($successfulFollowUps->isEmpty()) {
            return 10; // Default to 10 AM
        }

        $hours = $successfulFollowUps->map(function ($followUp) {
            return Carbon::parse($followUp->sent_at)->hour;
        });

        return (int) $hours->avg();
    }

    /**
     * Create escalation workflow for overdue invoices.
     */
    public function createEscalationWorkflow(Invoice $invoice): array
    {
        $escalationSteps = [
            ['days' => 1, 'type' => 'gentle', 'method' => 'email'],
            ['days' => 3, 'type' => 'reminder', 'method' => 'whatsapp'],
            ['days' => 7, 'type' => 'urgent', 'method' => 'email'],
            ['days' => 14, 'type' => 'final_notice', 'method' => 'sms'],
            ['days' => 21, 'type' => 'legal_notice', 'method' => 'email']
        ];

        $createdReminders = [];

        foreach ($escalationSteps as $step) {
            $scheduledAt = $invoice->due_date->addDays($step['days']);

            // Skip if date has already passed
            if ($scheduledAt->isPast()) {
                continue;
            }

            $followUp = FollowUp::create([
                'user_id' => $invoice->user_id,
                'invoice_id' => $invoice->id,
                'type' => $step['type'],
                'method' => $step['method'],
                'status' => 'scheduled',
                'scheduled_at' => $scheduledAt,
                'message' => $this->generateReminderMessage($invoice, $step['type'])
            ]);

            $createdReminders[] = $followUp;
        }

        return $createdReminders;
    }

    /**
     * Process AI-powered reminder optimization.
     */
    public function optimizeReminderTiming(): array
    {
        $results = [
            'optimized' => 0,
            'failed' => 0,
            'errors' => []
        ];

        $pendingReminders = FollowUp::where('status', 'scheduled')
            ->where('scheduled_at', '>', now())
            ->get();

        foreach ($pendingReminders as $reminder) {
            try {
                $optimalTime = $this->calculateOptimalSendTime($reminder->invoice->client);
                $currentScheduled = Carbon::parse($reminder->scheduled_at);

                // Update time while keeping the same date
                $optimizedTime = $currentScheduled->setTime($optimalTime->hour, $optimalTime->minute);

                $reminder->update(['scheduled_at' => $optimizedTime]);
                $results['optimized']++;

            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = [
                    'reminder_id' => $reminder->id,
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * Determine optimal reminder strategy with AI insights.
     */
    protected function determineOptimalReminderStrategy(Invoice $invoice): array
    {
        $clientHistory = $this->analyzeClientPaymentHistory($invoice->client);
        $daysOverdue = now()->diffInDays($invoice->due_date);
        $previousReminders = $invoice->followUps()->count();

        $strategy = [
            'type' => $this->determineReminderType($invoice),
            'channels' => $this->determineOptimalChannels($invoice->client, $clientHistory),
            'timing' => $this->calculateOptimalSendTime($invoice->client),
            'personalization_level' => $this->determinePersonalizationLevel($clientHistory),
            'ai_optimized' => false,
            'urgency_level' => $this->calculateUrgencyLevel($daysOverdue, $previousReminders, $invoice->total_amount),
        ];

        // AI optimization if available
        if (PlanChecker::canUseAiAssistant()) {
            $aiStrategy = $this->getAIOptimizedStrategy($invoice, $clientHistory, $strategy);
            if ($aiStrategy) {
                $strategy = array_merge($strategy, $aiStrategy);
                $strategy['ai_optimized'] = true;
            }
        }

        return $strategy;
    }

    /**
     * Create enhanced smart reminder with multi-channel support.
     */
    protected function createEnhancedSmartReminder(Invoice $invoice, array $strategy): FollowUp
    {
        $message = $this->generatePersonalizedMessage($invoice, $strategy);

        $followUp = FollowUp::create([
            'user_id' => $invoice->user_id,
            'invoice_id' => $invoice->id,
            'type' => $strategy['type'],
            'message' => $message,
            'method' => $strategy['channels'][0], // Primary channel
            'delivery_channels' => $strategy['channels'],
            'status' => 'pending',
            'scheduled_at' => $strategy['timing'],
            'optimal_send_time' => $strategy['timing'],
            'auto_generated' => true,
            'template_used' => $strategy['type'] . '_' . $strategy['personalization_level'],
            'personalization_data' => [
                'urgency_level' => $strategy['urgency_level'],
                'personalization_level' => $strategy['personalization_level'],
                'ai_optimized' => $strategy['ai_optimized'],
            ],
        ]);

        // Schedule for multiple channels if specified
        if (count($strategy['channels']) > 1) {
            $this->scheduleMultiChannelDelivery($followUp, $strategy['channels']);
        }

        return $followUp;
    }

    /**
     * Determine optimal communication channels.
     */
    protected function determineOptimalChannels(Client $client, array $clientHistory): array
    {
        $channels = ['email']; // Default channel

        // Analyze previous follow-up effectiveness
        $effectiveChannels = $this->getEffectiveChannelsForClient($client);

        if (!empty($effectiveChannels)) {
            $channels = $effectiveChannels;
        } else {
            // Use client preferences or defaults
            if ($client->preferred_contact_method) {
                $channels = [$client->preferred_contact_method];
            }

            // Add WhatsApp for high-value invoices or poor email response
            if ($clientHistory['reliability_score'] < 60 || $clientHistory['avg_payment_delay'] > 14) {
                if (!in_array('whatsapp', $channels) && $client->whatsapp_number) {
                    $channels[] = 'whatsapp';
                }
            }
        }

        return $channels;
    }

    /**
     * Get effective channels based on client's response history.
     */
    protected function getEffectiveChannelsForClient(Client $client): array
    {
        $followUps = $client->invoices()
            ->with('followUps')
            ->get()
            ->pluck('followUps')
            ->flatten()
            ->where('effectiveness_score', '>=', 60);

        if ($followUps->isEmpty()) {
            return [];
        }

        $channelEffectiveness = [];
        foreach ($followUps as $followUp) {
            $channels = $followUp->delivery_channels ?? [$followUp->method];
            foreach ($channels as $channel) {
                if (!isset($channelEffectiveness[$channel])) {
                    $channelEffectiveness[$channel] = [];
                }
                $channelEffectiveness[$channel][] = $followUp->effectiveness_score;
            }
        }

        // Calculate average effectiveness per channel
        $avgEffectiveness = [];
        foreach ($channelEffectiveness as $channel => $scores) {
            $avgEffectiveness[$channel] = array_sum($scores) / count($scores);
        }

        // Sort by effectiveness and return top channels
        arsort($avgEffectiveness);
        return array_keys(array_slice($avgEffectiveness, 0, 2, true));
    }

    /**
     * Determine personalization level based on client relationship.
     */
    protected function determinePersonalizationLevel(array $clientHistory): string
    {
        $relationshipMonths = $clientHistory['relationship_months'] ?? 0;
        $reliabilityScore = $clientHistory['reliability_score'] ?? 50;

        if ($relationshipMonths >= 12 && $reliabilityScore >= 80) {
            return 'high'; // Personal, friendly tone
        } elseif ($relationshipMonths >= 6 && $reliabilityScore >= 60) {
            return 'medium'; // Professional but warm
        } else {
            return 'low'; // Formal, professional
        }
    }

    /**
     * Calculate urgency level for the reminder.
     */
    protected function calculateUrgencyLevel(int $daysOverdue, int $previousReminders, float $invoiceAmount): string
    {
        $urgencyScore = 0;

        // Days overdue factor
        if ($daysOverdue <= 7) $urgencyScore += 1;
        elseif ($daysOverdue <= 30) $urgencyScore += 2;
        else $urgencyScore += 3;

        // Previous reminders factor
        $urgencyScore += min($previousReminders, 3);

        // Invoice amount factor
        if ($invoiceAmount >= 50000) $urgencyScore += 2;
        elseif ($invoiceAmount >= 10000) $urgencyScore += 1;

        if ($urgencyScore <= 2) return 'low';
        elseif ($urgencyScore <= 5) return 'medium';
        else return 'high';
    }

    /**
     * Generate personalized message based on strategy.
     */
    protected function generatePersonalizedMessage(Invoice $invoice, array $strategy): string
    {
        if ($strategy['ai_optimized'] && PlanChecker::canUseAiAssistant()) {
            return $this->generateAIPersonalizedMessage($invoice, $strategy);
        }

        return $this->generateTemplateBasedMessage($invoice, $strategy);
    }

    /**
     * Generate AI-powered personalized message.
     */
    protected function generateAIPersonalizedMessage(Invoice $invoice, array $strategy): string
    {
        try {
            $context = [
                'client_name' => $invoice->client->name,
                'invoice_number' => $invoice->invoice_number,
                'amount' => $invoice->total_amount,
                'days_overdue' => now()->diffInDays($invoice->due_date),
                'reminder_type' => $strategy['type'],
                'personalization_level' => $strategy['personalization_level'],
                'urgency_level' => $strategy['urgency_level'],
                'relationship_duration' => now()->diffInMonths($invoice->client->created_at),
                'client_reliability' => $this->analyzeClientPaymentHistory($invoice->client)['reliability_score'],
                'previous_reminders' => $invoice->followUps()->count(),
            ];

            $result = $this->aiService->generatePersonalizedFollowUpMessage($context);

            if ($result['success']) {
                return $result['content'];
            }
        } catch (\Exception $e) {
            Log::warning('AI personalized message generation failed', [
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage()
            ]);
        }

        return $this->generateTemplateBasedMessage($invoice, $strategy);
    }

    /**
     * Generate template-based message with personalization.
     */
    protected function generateTemplateBasedMessage(Invoice $invoice, array $strategy): string
    {
        $templates = $this->getPersonalizedTemplates($strategy['personalization_level'], $strategy['urgency_level']);
        $template = $templates[$strategy['type']] ?? $templates['reminder'];

        $replacements = [
            '{client_name}' => $invoice->client->name,
            '{invoice_number}' => $invoice->invoice_number,
            '{amount}' => '₹' . number_format($invoice->total_amount, 2),
            '{due_date}' => $invoice->due_date->format('d M Y'),
            '{days_overdue}' => now()->diffInDays($invoice->due_date),
            '{business_name}' => $invoice->user->business_name ?? $invoice->user->name,
        ];

        return str_replace(array_keys($replacements), array_values($replacements), $template);
    }

    /**
     * Get personalized message templates.
     */
    protected function getPersonalizedTemplates(string $personalizationLevel, string $urgencyLevel): array
    {
        $templates = [
            'high' => [
                'friendly' => "Hi {client_name}! Hope you're doing well. Just a quick reminder that invoice #{invoice_number} for {amount} is due. Thanks for your continued partnership!",
                'reminder' => "Hi {client_name}, I wanted to personally reach out about invoice #{invoice_number} for {amount} which was due on {due_date}. Could you please let me know the status?",
                'urgent' => "Hi {client_name}, I hope everything is okay. Invoice #{invoice_number} for {amount} is now {days_overdue} days overdue. Could we please arrange payment soon?",
            ],
            'medium' => [
                'friendly' => "Dear {client_name}, this is a friendly reminder that invoice #{invoice_number} for {amount} is due on {due_date}. Thank you for your business!",
                'reminder' => "Dear {client_name}, invoice #{invoice_number} for {amount} was due on {due_date}. Please process payment at your earliest convenience.",
                'urgent' => "Dear {client_name}, invoice #{invoice_number} for {amount} is now {days_overdue} days overdue. Please arrange payment to avoid any service disruption.",
            ],
            'low' => [
                'friendly' => "Dear {client_name}, please note that invoice #{invoice_number} for {amount} is due on {due_date}.",
                'reminder' => "Dear {client_name}, this is a reminder that invoice #{invoice_number} for {amount} was due on {due_date}. Please process payment promptly.",
                'urgent' => "Dear {client_name}, invoice #{invoice_number} for {amount} is overdue by {days_overdue} days. Immediate payment is required.",
            ],
        ];

        return $templates[$personalizationLevel] ?? $templates['medium'];
    }

    /**
     * Update client behavior analytics.
     */
    protected function updateClientBehaviorAnalytics(Client $client, FollowUp $followUp): void
    {
        $cacheKey = "client_behavior_analytics_{$client->id}";

        $analytics = Cache::remember($cacheKey, 3600, function () use ($client) {
            return $this->calculateClientBehaviorAnalytics($client);
        });

        // Update analytics with new follow-up data
        $analytics['total_reminders_sent'] = ($analytics['total_reminders_sent'] ?? 0) + 1;
        $analytics['last_reminder_sent'] = now()->toISOString();
        $analytics['reminder_frequency'] = $this->calculateReminderFrequency($client);

        Cache::put($cacheKey, $analytics, 3600);
    }

    /**
     * Calculate comprehensive client behavior analytics.
     */
    protected function calculateClientBehaviorAnalytics(Client $client): array
    {
        $invoices = $client->invoices()->with('followUps')->get();
        $followUps = $invoices->pluck('followUps')->flatten();

        return [
            'total_invoices' => $invoices->count(),
            'paid_invoices' => $invoices->where('status', 'paid')->count(),
            'overdue_invoices' => $invoices->where('status', 'overdue')->count(),
            'total_reminders_sent' => $followUps->count(),
            'effective_reminders' => $followUps->where('effectiveness_score', '>=', 60)->count(),
            'average_response_time' => $this->calculateAverageResponseTime($followUps),
            'preferred_channels' => $this->getEffectiveChannelsForClient($client),
            'payment_patterns' => $this->analyzePaymentPatterns($invoices),
            'communication_preferences' => $this->analyzeCommunicationPreferences($followUps),
        ];
    }

    /**
     * Schedule multi-channel delivery for follow-up.
     */
    protected function scheduleMultiChannelDelivery(FollowUp $followUp, array $channels): void
    {
        $baseTime = $followUp->scheduled_at;

        foreach ($channels as $index => $channel) {
            if ($index === 0) continue; // Skip primary channel

            // Schedule additional channels with delays
            $delay = $index * 30; // 30 minutes between channels
            $scheduledTime = $baseTime->copy()->addMinutes($delay);

            FollowUp::create([
                'user_id' => $followUp->user_id,
                'invoice_id' => $followUp->invoice_id,
                'type' => $followUp->type,
                'message' => $followUp->message,
                'method' => $channel,
                'delivery_channels' => [$channel],
                'status' => 'pending',
                'scheduled_at' => $scheduledTime,
                'auto_generated' => true,
                'template_used' => $followUp->template_used,
                'personalization_data' => $followUp->personalization_data,
            ]);
        }
    }

    /**
     * Get AI-optimized strategy.
     */
    protected function getAIOptimizedStrategy(Invoice $invoice, array $clientHistory, array $baseStrategy): ?array
    {
        try {
            $context = [
                'invoice_amount' => $invoice->total_amount,
                'days_overdue' => now()->diffInDays($invoice->due_date),
                'client_reliability' => $clientHistory['reliability_score'],
                'previous_reminders' => $invoice->followUps()->count(),
                'relationship_duration' => $clientHistory['relationship_months'],
                'base_strategy' => $baseStrategy,
            ];

            $result = $this->aiService->optimizeReminderStrategy($context);

            if ($result['success']) {
                return $result['optimized_strategy'];
            }
        } catch (\Exception $e) {
            Log::warning('AI strategy optimization failed', [
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage()
            ]);
        }

        return null;
    }

    /**
     * Dispatch queue-based processing for all scheduled reminders.
     */
    public function dispatchQueueProcessing(array $options = []): void
    {
        \App\Jobs\ProcessSmartRemindersJob::dispatch(null, $options)
            ->onQueue($options['queue'] ?? 'default');
    }

    /**
     * Dispatch queue-based processing for specific reminder.
     */
    public function dispatchSingleProcessing(int $reminderId, array $options = []): void
    {
        \App\Jobs\ProcessSmartRemindersJob::dispatch($reminderId, $options)
            ->onQueue($options['queue'] ?? 'high');
    }
}
