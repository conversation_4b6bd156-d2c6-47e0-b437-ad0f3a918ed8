@props([
    'type' => 'invoices',
    'current' => 0,
    'limit' => null,
    'showUpgrade' => true
])

@php
$isUnlimited = $limit === null;
$percentage = $isUnlimited ? 0 : ($limit > 0 ? min(($current / $limit) * 100, 100) : 0);
$isNearLimit = $percentage >= 80;
$isAtLimit = $percentage >= 100;

$typeLabels = [
    'invoices' => 'Invoices',
    'contracts' => 'Contracts',
    'clients' => 'Clients',
    'expenses' => 'Expenses'
];

$typeLabel = $typeLabels[$type] ?? ucfirst($type);

$progressColor = $isAtLimit ? 'bg-red-500' : ($isNearLimit ? 'bg-yellow-500' : 'bg-blue-500');
$textColor = $isAtLimit ? 'text-red-600' : ($isNearLimit ? 'text-yellow-600' : 'text-blue-600');
@endphp

<div class="bg-white rounded-lg border border-gray-200 p-4 {{ $attributes->get('class') }}">
    <div class="flex items-center justify-between mb-2">
        <h3 class="text-sm font-medium text-gray-700">{{ $typeLabel }} Usage</h3>
        @if($isUnlimited)
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <i class="fas fa-infinity mr-1"></i>
                Unlimited
            </span>
        @else
            <span class="text-sm {{ $textColor }} font-medium">
                {{ $current }}/{{ $limit }}
            </span>
        @endif
    </div>

    @if(!$isUnlimited)
        <!-- Progress Bar -->
        <div class="w-full bg-gray-200 rounded-full h-2 mb-3">
            <div class="{{ $progressColor }} h-2 rounded-full transition-all duration-300" 
                 style="width: {{ $percentage }}%"></div>
        </div>

        <!-- Status Message -->
        @if($isAtLimit)
            <div class="flex items-center text-sm text-red-600 mb-2">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                <span>You've reached your {{ strtolower($typeLabel) }} limit</span>
            </div>
        @elseif($isNearLimit)
            <div class="flex items-center text-sm text-yellow-600 mb-2">
                <i class="fas fa-exclamation-circle mr-2"></i>
                <span>{{ $limit - $current }} {{ strtolower($typeLabel) }} remaining</span>
            </div>
        @else
            <div class="flex items-center text-sm text-gray-600 mb-2">
                <i class="fas fa-check-circle mr-2"></i>
                <span>{{ $limit - $current }} {{ strtolower($typeLabel) }} remaining</span>
            </div>
        @endif

        <!-- Upgrade Button -->
        @if($showUpgrade && ($isAtLimit || $isNearLimit))
            <x-ui.button 
                href="{{ route('subscriptions.plans') }}" 
                variant="primary" 
                size="sm" 
                icon="fas fa-arrow-up"
                class="w-full">
                Upgrade Plan
            </x-ui.button>
        @endif
    @endif
</div>
