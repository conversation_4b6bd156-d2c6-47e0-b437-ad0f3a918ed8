<x-app-layout>
    <x-slot name="title">Good {{ now()->format('H') < 12 ? 'morning' : (now()->format('H') < 17 ? 'afternoon' : 'evening') }}, {{ Auth::user()->name }}!</x-slot>
    <x-slot name="subtitle">Here's what's happening with your business today.</x-slot>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
        <!-- Total Revenue Card -->
        <div class="card group hover:shadow-glow transition-all duration-300">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <p class="text-xs font-semibold text-secondary-500 uppercase tracking-wider mb-2">Total Revenue</p>
                        <p class="text-3xl font-bold text-secondary-900 mb-1">₹{{ number_format($total_invoice_amount, 0) }}</p>
                        @if(isset($revenue_growth))
                            <div class="flex items-center">
                                <span class="text-xs {{ $revenue_growth >= 0 ? 'text-success-600' : 'text-danger-600' }} font-medium">
                                    <i class="fas fa-{{ $revenue_growth >= 0 ? 'arrow-up' : 'arrow-down' }} mr-1"></i>
                                    {{ abs($revenue_growth) }}%
                                </span>
                                <span class="text-xs text-secondary-500 ml-1">vs last month</span>
                            </div>
                        @endif
                    </div>
                    <div class="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl shadow-soft group-hover:shadow-glow-lg transition-all duration-300">
                        <i class="fas fa-chart-line text-white text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Invoices Card -->
        <div class="card group hover:shadow-glow transition-all duration-300">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <p class="text-xs font-semibold text-secondary-500 uppercase tracking-wider mb-2">Total Invoices</p>
                        <p class="text-3xl font-bold text-secondary-900 mb-1">{{ $total_invoices }}</p>
                        @if(isset($invoice_growth))
                            <div class="flex items-center">
                                <span class="text-xs {{ $invoice_growth >= 0 ? 'text-success-600' : 'text-danger-600' }} font-medium">
                                    <i class="fas fa-{{ $invoice_growth >= 0 ? 'arrow-up' : 'arrow-down' }} mr-1"></i>
                                    {{ abs($invoice_growth) }}%
                                </span>
                                <span class="text-xs text-secondary-500 ml-1">vs last month</span>
                            </div>
                        @endif
                    </div>
                    <div class="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-accent-500 to-accent-600 rounded-2xl shadow-soft group-hover:shadow-glow-lg transition-all duration-300">
                        <i class="fas fa-file-invoice text-white text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Clients Card -->
        <div class="card group hover:shadow-glow transition-all duration-300">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <p class="text-xs font-semibold text-secondary-500 uppercase tracking-wider mb-2">Total Clients</p>
                        <p class="text-3xl font-bold text-secondary-900 mb-1">{{ $total_clients }}</p>
                        <div class="flex items-center">
                            <span class="text-xs text-secondary-500">Active relationships</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-success-500 to-success-600 rounded-2xl shadow-soft group-hover:shadow-glow-lg transition-all duration-300">
                        <i class="fas fa-users text-white text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Amount Card -->
        <div class="card group hover:shadow-glow transition-all duration-300">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <p class="text-xs font-semibold text-secondary-500 uppercase tracking-wider mb-2">Pending Amount</p>
                        <p class="text-3xl font-bold text-secondary-900 mb-1">₹{{ number_format($pending_invoice_amount, 0) }}</p>
                        <div class="flex items-center">
                            <span class="text-xs text-warning-600">{{ $pending_invoices ?? 0 }} invoices pending</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-warning-500 to-warning-600 rounded-2xl shadow-soft group-hover:shadow-glow-lg transition-all duration-300">
                        <i class="fas fa-clock text-white text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue Overview and Invoice Status -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Revenue Chart -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Revenue Overview</h3>
                <p class="text-sm text-gray-600">Monthly revenue for the current year</p>
            </div>
            <div class="p-6">
                <canvas id="revenueChart" height="300"></canvas>
            </div>
        </div>

        <!-- Invoice Status -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Invoice Status</h3>
            </div>
            <div class="p-6 space-y-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                        <span class="text-sm text-gray-600">Paid</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900">{{ $paid_invoices }}</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-orange-500 rounded-full mr-3"></div>
                        <span class="text-sm text-gray-600">Pending</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900">{{ $pending_invoices }}</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                        <span class="text-sm text-gray-600">Overdue</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900">{{ $overdue_invoices }}</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-gray-400 rounded-full mr-3"></div>
                        <span class="text-sm text-gray-600">Draft</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900">{{ $draft_invoices }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Recent Invoices -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Recent Invoices</h3>
                    <a href="{{ route('invoices.index') }}" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                        View All
                    </a>
                </div>
            </div>
            <div class="p-6">
                @if($recent_invoices->count() > 0)
                    <div class="space-y-4">
                        @foreach($recent_invoices as $invoice)
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-900">{{ $invoice->invoice_number }}</p>
                                    <p class="text-sm text-gray-600">{{ $invoice->client->name }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="font-medium text-gray-900">₹{{ number_format($invoice->total_amount, 0) }}</p>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                        @if($invoice->status === 'paid') bg-green-100 text-green-800
                                        @elseif($invoice->status === 'pending') bg-yellow-100 text-yellow-800
                                        @elseif($invoice->status === 'overdue') bg-red-100 text-red-800
                                        @else bg-gray-100 text-gray-800 @endif">
                                        {{ ucfirst($invoice->status) }}
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-file-invoice text-gray-400 text-3xl mb-3"></i>
                        <p class="text-gray-500">No invoices yet</p>
                        <a href="{{ route('invoices.create') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors duration-200 mt-3">
                            Create Your First Invoice
                        </a>
                    </div>
                @endif
            </div>
        </div>

        <!-- Recent Clients -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Recent Clients</h3>
                    <a href="{{ route('clients.index') }}" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                        View All
                    </a>
                </div>
            </div>
            <div class="p-6">
                @if($recent_clients->count() > 0)
                    <div class="space-y-4">
                        @foreach($recent_clients as $client)
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-900">{{ $client->name }}</p>
                                    <p class="text-sm text-gray-600">{{ $client->email }}</p>
                                </div>
                                <div class="text-right">
                                    <a href="{{ route('clients.show', $client) }}" class="text-blue-600 hover:text-blue-700 text-sm">
                                        View
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-users text-gray-400 text-3xl mb-3"></i>
                        <p class="text-gray-500">No clients yet</p>
                        <a href="{{ route('clients.create') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors duration-200 mt-3">
                            Add Your First Client
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Chart.js Script -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Revenue Chart
        const ctx = document.getElementById('revenueChart').getContext('2d');
        const revenueChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: @json(array_keys($monthly_revenue)),
                datasets: [{
                    label: 'Revenue',
                    data: @json(array_values($monthly_revenue)),
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '₹' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    </script>
</x-app-layout>
