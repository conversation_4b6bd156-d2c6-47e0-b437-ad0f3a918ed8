<?php

namespace App\Services;

use App\Services\OpenAIService;
use App\Services\GroqService;
use InvalidArgumentException;

class AIServiceFactory
{
    /**
     * Create AI service instance based on configuration
     */
    public static function create(): OpenAIService|GroqService
    {
        $provider = config('ai.default', 'groq');

        try {
            return match($provider) {
                'openai' => new OpenAIService(),
                'groq' => new GroqService(),
                default => throw new InvalidArgumentException("Unsupported AI provider: {$provider}")
            };
        } catch (\Exception $e) {
            \Log::error('AI service creation failed', [
                'provider' => $provider,
                'error' => $e->getMessage()
            ]);

            // Fallback to Groq service
            return new GroqService();
        }
    }

    /**
     * Get available providers
     */
    public static function getAvailableProviders(): array
    {
        return [
            'openai' => [
                'name' => 'OpenAI GPT-4',
                'description' => 'High-quality AI responses with advanced reasoning',
                'speed' => 'Medium',
                'cost' => 'Higher',
            ],
            'groq' => [
                'name' => 'Groq Llama',
                'description' => 'Ultra-fast AI responses with good quality',
                'speed' => 'Very Fast',
                'cost' => 'Lower',
            ],
        ];
    }

    /**
     * Check if provider is configured
     */
    public static function isProviderConfigured(string $provider): bool
    {
        return match($provider) {
            'openai' => !empty(config('services.openai.api_key')),
            'groq' => !empty(config('services.groq.api_key')),
            default => false
        };
    }

    /**
     * Get current provider
     */
    public static function getCurrentProvider(): string
    {
        return config('services.ai.provider', 'openai');
    }

    /**
     * Test provider connection
     */
    public static function testProvider(string $provider): array
    {
        try {
            $service = match($provider) {
                'openai' => new OpenAIService(),
                'groq' => new GroqService(),
                default => throw new InvalidArgumentException("Unsupported provider: {$provider}")
            };

            return $service->testConnection();
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
