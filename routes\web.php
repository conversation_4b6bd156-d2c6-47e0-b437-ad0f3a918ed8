<?php

use App\Http\Controllers\AiAssistantController;
use App\Http\Controllers\AutomationController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\ContractController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ExpenseController;
use App\Http\Controllers\ExpenseReportController;
use App\Http\Controllers\FileController;
use App\Http\Controllers\FollowUpController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\LeadController;
use App\Http\Controllers\MultiClientDashboardController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\PortfolioGeneratorController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\ProposalController;
use App\Http\Controllers\ProposalTemplateController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\TaskController;
use App\Http\Controllers\TdsController;
use App\Http\Controllers\TimeEntryController;
use Illuminate\Support\Facades\Route;

// Public Pages
Route::get('/', [App\Http\Controllers\HomeController::class, 'index'])->name('home');
Route::get('/about', [App\Http\Controllers\HomeController::class, 'about'])->name('about');
Route::get('/features', [App\Http\Controllers\HomeController::class, 'features'])->name('features');
Route::get('/pricing', [App\Http\Controllers\HomeController::class, 'pricing'])->name('pricing');
Route::get('/contact', [App\Http\Controllers\HomeController::class, 'contact'])->name('contact');
Route::post('/contact', [App\Http\Controllers\HomeController::class, 'submitContact'])
    ->middleware('rate.limit:contact')
    ->name('contact.submit');
Route::get('/privacy', [App\Http\Controllers\HomeController::class, 'privacy'])->name('privacy');
Route::get('/terms', [App\Http\Controllers\HomeController::class, 'terms'])->name('terms');
Route::get('/refund', [App\Http\Controllers\HomeController::class, 'refund'])->name('refund');

// Dashboard
Route::get('/dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::get('/dashboard/data', [DashboardController::class, 'getData'])
    ->middleware(['auth'])
    ->name('dashboard.data');

// Business Registration and Onboarding
Route::middleware('auth')->group(function () {
    Route::get('/business/select-type', [App\Http\Controllers\BusinessRegistrationController::class, 'selectType'])
        ->name('business.select-type');
    Route::get('/business/register', [App\Http\Controllers\BusinessRegistrationController::class, 'create'])
        ->name('business.register');
    Route::post('/business/register', [App\Http\Controllers\BusinessRegistrationController::class, 'store'])
        ->name('business.store');

    // Business Onboarding
    Route::prefix('business/{business}/onboarding')->group(function () {
        Route::get('/welcome', [App\Http\Controllers\BusinessOnboardingController::class, 'welcome'])
            ->name('business.onboarding.welcome');
        Route::get('/setup', [App\Http\Controllers\BusinessOnboardingController::class, 'setup'])
            ->name('business.onboarding.setup');
        Route::post('/setup', [App\Http\Controllers\BusinessOnboardingController::class, 'updateSetup'])
            ->name('business.onboarding.setup.update');
        Route::get('/features', [App\Http\Controllers\BusinessOnboardingController::class, 'features'])
            ->name('business.onboarding.features');
        Route::post('/features', [App\Http\Controllers\BusinessOnboardingController::class, 'updateFeatures'])
            ->name('business.onboarding.features.update');
        Route::get('/complete', [App\Http\Controllers\BusinessOnboardingController::class, 'complete'])
            ->name('business.onboarding.complete');
        Route::post('/finish', [App\Http\Controllers\BusinessOnboardingController::class, 'finish'])
            ->name('business.onboarding.finish');
    });
});

// Profile routes
Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Upgrade routes
    Route::get('/upgrade/required', function () {
        return view('upgrade.required');
    })->name('upgrade.required');

    Route::get('/upgrade', function () {
        return redirect()->route('subscriptions.plans');
    })->name('upgrade');

    // Advanced Features (Business Plan)
    // AI Assistant
    Route::get('/ai-assistant', [AiAssistantController::class, 'index'])->name('ai-assistant.index');
    Route::post('/ai-assistant/generate', [AiAssistantController::class, 'generate'])->name('ai-assistant.generate');
    Route::post('/ai-assistant/analyze', [AiAssistantController::class, 'analyze'])->name('ai-assistant.analyze');
    Route::post('/ai-assistant/invoice-description', [AiAssistantController::class, 'generateInvoiceDescription'])->name('ai-assistant.invoice-description');
    Route::post('/ai-assistant/business-insights', [AiAssistantController::class, 'generateBusinessInsights'])->name('ai-assistant.business-insights');

    // AI Proposal Assistant
    Route::post('/ai-assistant/generate-proposal', [AiAssistantController::class, 'generateProposal'])->name('ai-assistant.generate-proposal');
    Route::post('/ai-assistant/improve-proposal', [AiAssistantController::class, 'improveProposal'])->name('ai-assistant.improve-proposal');

    // Multi-Client Dashboard
    Route::get('/multi-client-dashboard', [MultiClientDashboardController::class, 'index'])->name('multi-client-dashboard.index');
    Route::post('/multi-client-dashboard/compare', [MultiClientDashboardController::class, 'compare'])->name('multi-client-dashboard.compare');

    // Portfolio Generator
    Route::get('/portfolio-generator', [PortfolioGeneratorController::class, 'index'])->name('portfolio-generator.index');
    Route::post('/portfolio-generator/generate', [PortfolioGeneratorController::class, 'generate'])->name('portfolio-generator.generate');
    Route::get('/portfolio-generator/preview', [PortfolioGeneratorController::class, 'preview'])->name('portfolio-generator.preview');
    Route::get('/portfolio-generator/download', [PortfolioGeneratorController::class, 'download'])->name('portfolio-generator.download');
});

// Client Management
Route::middleware('auth')->group(function () {
    Route::resource('clients', ClientController::class);
});

// Lead Management
Route::middleware('auth')->group(function () {
    Route::resource('leads', LeadController::class);

    // Pipeline view
    Route::get('leads-pipeline', [LeadController::class, 'pipeline'])->name('leads.pipeline');

    // Lead actions
    Route::post('leads/{lead}/convert', [LeadController::class, 'convert'])->name('leads.convert');
    Route::post('leads/{lead}/mark-as-lost', [LeadController::class, 'markAsLost'])->name('leads.mark-as-lost');
    Route::patch('leads/{lead}/move-to-stage', [LeadController::class, 'moveToStage'])->name('leads.move-to-stage');

    // Lead activities and notes
    Route::post('leads/{lead}/activities', [LeadController::class, 'addActivity'])->name('leads.add-activity');
    Route::post('leads/{lead}/notes', [LeadController::class, 'addNote'])->name('leads.add-note');

    // Analytics and export
    Route::get('leads-analytics', [LeadController::class, 'analytics'])->name('leads.analytics');
    Route::get('leads-export', [LeadController::class, 'export'])->name('leads.export');
});

// Invoice Management
Route::middleware('auth')->group(function () {
    Route::resource('invoices', InvoiceController::class);
    Route::get('invoices/{invoice}/download', [InvoiceController::class, 'download'])->name('invoices.download');
    Route::patch('invoices/{invoice}/mark-paid', [InvoiceController::class, 'markAsPaid'])->name('invoices.mark-paid');

    // Client portal integration
    Route::post('invoices/{invoice}/send-to-client', [InvoiceController::class, 'sendToClient'])->name('invoices.send-to-client');
    Route::post('invoices/{invoice}/send-payment-reminder', [InvoiceController::class, 'sendPaymentReminder'])->name('invoices.send-payment-reminder');
    Route::get('invoices/{invoice}/payment-summary', [InvoiceController::class, 'getPaymentSummary'])->name('invoices.payment-summary');
    Route::post('invoices/{invoice}/regenerate-tokens', [InvoiceController::class, 'regenerateTokens'])->name('invoices.regenerate-tokens');
});

// Payment Tracking
Route::middleware('auth')->group(function () {
    Route::get('payment-tracking', [App\Http\Controllers\PaymentTrackingController::class, 'index'])->name('payment-tracking.index');
    Route::get('payment-tracking/{invoice}', [App\Http\Controllers\PaymentTrackingController::class, 'show'])->name('payment-tracking.show');
    Route::get('payment-tracking/export/data', [App\Http\Controllers\PaymentTrackingController::class, 'export'])->name('payment-tracking.export');
});

// Contract Management
Route::middleware('auth')->group(function () {
    Route::resource('contracts', ContractController::class);
    Route::get('contracts/{contract}/download', [ContractController::class, 'download'])->name('contracts.download');
    Route::patch('contracts/{contract}/send', [ContractController::class, 'send'])->name('contracts.send');
    Route::patch('contracts/{contract}/mark-signed', [ContractController::class, 'markAsSigned'])->name('contracts.mark-signed');
    Route::get('contract-templates/{template}', [ContractController::class, 'getTemplate'])->name('contract-templates.show');
    Route::get('clients/{client}/details', [ContractController::class, 'getClientDetails'])->name('clients.details');
});

// Proposal Management (Freelancers only)
Route::middleware(['auth', 'role:freelancer'])->group(function () {
    Route::resource('proposals', ProposalController::class);
    Route::post('proposals/{proposal}/send', [ProposalController::class, 'send'])->name('proposals.send');
    Route::post('proposals/{proposal}/accept', [ProposalController::class, 'accept'])->name('proposals.accept');
    Route::post('proposals/{proposal}/reject', [ProposalController::class, 'reject'])->name('proposals.reject');
    Route::post('proposals/{proposal}/create-revision', [ProposalController::class, 'createRevision'])->name('proposals.create-revision');

    // Conversion workflows
    Route::get('proposals/{proposal}/convert-to-project', [ProposalController::class, 'showConvertToProject'])->name('proposals.show-convert-to-project');
    Route::post('proposals/{proposal}/convert-to-project', [ProposalController::class, 'convertToProject'])->name('proposals.convert-to-project');
    Route::get('proposals/{proposal}/convert-to-contract', [ProposalController::class, 'showConvertToContract'])->name('proposals.show-convert-to-contract');
    Route::post('proposals/{proposal}/convert-to-contract', [ProposalController::class, 'convertToContract'])->name('proposals.convert-to-contract');

    Route::get('proposals/{proposal}/data', [ProposalController::class, 'getData'])->name('proposals.data');

    // Proposal upgrade page
    Route::get('proposals/upgrade', function() {
        return view('proposals.upgrade');
    })->name('proposals.upgrade');

    // Proposal Templates
    Route::resource('proposal-templates', ProposalTemplateController::class);
    Route::get('proposal-templates/{proposalTemplate}/content', [ProposalTemplateController::class, 'getContent'])->name('proposal-templates.content');
});

// Public proposal viewing (for clients)
Route::get('proposals/{proposal}/view', [ProposalController::class, 'markViewed'])->name('proposals.mark-viewed');
Route::get('proposals/{proposal}/public/{token}', [ProposalController::class, 'publicView'])->name('proposals.public');
Route::post('proposals/{proposal}/public/{token}/accept', [ProposalController::class, 'publicAccept'])->name('proposals.public.accept');
Route::post('proposals/{proposal}/public/{token}/reject', [ProposalController::class, 'publicReject'])->name('proposals.public.reject');

// Client Portal (for invoice viewing and payments)
Route::prefix('client-portal')->name('client-portal.')->middleware(['client.portal.rate.limit:30,1'])->group(function () {
    // Invoice viewing routes (more permissive rate limit)
    Route::get('invoice/{invoice}/view/{token}', [App\Http\Controllers\ClientPortalController::class, 'viewInvoice'])->name('invoice.view');
    Route::get('invoice/{invoice}/download/{token}', [App\Http\Controllers\ClientPortalController::class, 'downloadInvoice'])->name('invoice.download');

    // Payment routes (stricter rate limit)
    Route::middleware(['client.portal.rate.limit:10,1'])->group(function () {
        Route::get('invoice/{invoice}/payment/{token}', [App\Http\Controllers\ClientPortalController::class, 'showPaymentGateway'])->name('invoice.payment');
        Route::post('invoice/{invoice}/payment/{token}/paypal', [App\Http\Controllers\ClientPortalController::class, 'processPayPalPayment'])->name('payment.paypal');
        Route::post('invoice/{invoice}/payment/{token}/razorpay', [App\Http\Controllers\ClientPortalController::class, 'processRazorpayPayment'])->name('payment.razorpay');
    });

    // Payment callback routes (moderate rate limit)
    Route::middleware(['client.portal.rate.limit:20,1'])->group(function () {
        Route::get('invoice/{invoice}/payment/{token}/success', [App\Http\Controllers\ClientPortalController::class, 'paymentSuccess'])->name('payment.success');
        Route::get('invoice/{invoice}/payment/{token}/failure', [App\Http\Controllers\ClientPortalController::class, 'paymentFailure'])->name('payment.failure');

        // PayPal specific callback routes
        Route::get('invoice/{invoice}/payment/{token}/paypal/success', [App\Http\Controllers\ClientPortalController::class, 'paymentSuccess'])->name('payment.paypal.success');
        Route::get('invoice/{invoice}/payment/{token}/paypal/cancel', [App\Http\Controllers\ClientPortalController::class, 'paymentFailure'])->name('payment.paypal.cancel');
    });
});

// Expense Management
Route::middleware('auth')->group(function () {
    Route::resource('expenses', ExpenseController::class);
    Route::patch('expenses/{expense}/submit', [ExpenseController::class, 'submit'])->name('expenses.submit');
    Route::patch('expenses/{expense}/approve', [ExpenseController::class, 'approve'])->name('expenses.approve');
    Route::patch('expenses/{expense}/reject', [ExpenseController::class, 'reject'])->name('expenses.reject');
    Route::get('expenses/export', [ExpenseController::class, 'export'])->name('expenses.export');

    // Expense Reports routes
    Route::prefix('expenses/reports')->name('expenses.reports.')->group(function () {
        Route::get('/', [ExpenseReportController::class, 'index'])->name('index');
        Route::get('/export', [ExpenseReportController::class, 'export'])->name('export');
        Route::get('/tax-summary', [ExpenseReportController::class, 'taxSummary'])->name('tax-summary');
        Route::get('/tax-summary/export', [ExpenseReportController::class, 'exportTaxSummary'])->name('tax-summary.export');
        Route::get('/monthly-comparison', [ExpenseReportController::class, 'monthlyComparison'])->name('monthly-comparison');
        Route::get('/category-analysis', [ExpenseReportController::class, 'categoryAnalysis'])->name('category-analysis');
    });
});

// Project Management
Route::middleware('auth')->group(function () {
    Route::resource('projects', ProjectController::class);
    Route::get('projects/{project}/dashboard', [ProjectController::class, 'dashboard'])->name('projects.dashboard');
    Route::patch('projects/{project}/status', [ProjectController::class, 'updateStatus'])->name('projects.status');

    // Project Members
    Route::post('projects/{project}/members', [ProjectController::class, 'addMember'])->name('projects.members.add');
    Route::delete('projects/{project}/members/{member}', [ProjectController::class, 'removeMember'])->name('projects.members.remove');
    Route::patch('projects/{project}/members/{member}', [ProjectController::class, 'updateMember'])->name('projects.members.update');

    // Project API endpoints
    Route::get('projects/{project}/tasks', [ProjectController::class, 'getTasks'])->name('projects.tasks.api');
    Route::get('projects/{project}/details', [ProjectController::class, 'getDetails'])->name('projects.details.api');

    // Project-specific management pages
    Route::get('projects/{project}/manage-tasks', [ProjectController::class, 'manageTasks'])->name('projects.manage-tasks');
    Route::get('projects/{project}/track-time', [ProjectController::class, 'trackTime'])->name('projects.track-time');
    Route::get('projects/{project}/manage-team', [ProjectController::class, 'manageTeam'])->name('projects.manage-team');

    // Tasks within projects
    Route::resource('projects.tasks', TaskController::class)->except(['index']);
    Route::patch('projects/{project}/tasks/{task}/status', [TaskController::class, 'updateStatus'])->name('projects.tasks.status');
    Route::patch('projects/{project}/tasks/{task}/assign', [TaskController::class, 'assign'])->name('projects.tasks.assign');
});

// Standalone Task Management
Route::middleware('auth')->group(function () {
    Route::get('tasks', [TaskController::class, 'index'])->name('tasks.index');
});

// Time Tracking
Route::middleware('auth')->prefix('time-tracking')->name('time-tracking.')->group(function () {
    Route::get('/', [TimeEntryController::class, 'index'])->name('index');
    Route::get('/create', [TimeEntryController::class, 'create'])->name('create');
    Route::post('/', [TimeEntryController::class, 'store'])->name('store');
    Route::get('/{timeEntry}', [TimeEntryController::class, 'show'])->name('show');
    Route::get('/{timeEntry}/edit', [TimeEntryController::class, 'edit'])->name('edit');
    Route::put('/{timeEntry}', [TimeEntryController::class, 'update'])->name('update');
    Route::delete('/{timeEntry}', [TimeEntryController::class, 'destroy'])->name('destroy');

    // Timer controls
    Route::post('/start', [TimeEntryController::class, 'startTimer'])->name('start');
    Route::post('/stop', [TimeEntryController::class, 'stopTimer'])->name('stop');
    Route::post('/pause', [TimeEntryController::class, 'pauseTimer'])->name('pause');
    Route::get('/active', [TimeEntryController::class, 'getActiveTimer'])->name('active');

    // Reports
    Route::get('/reports/timesheet', [TimeEntryController::class, 'timesheet'])->name('reports.timesheet');
    Route::get('/reports/summary', [TimeEntryController::class, 'summary'])->name('reports.summary');
    Route::get('/reports/export', [TimeEntryController::class, 'export'])->name('reports.export');
});

// TDS Management
Route::middleware('auth')->group(function () {
    Route::get('tds', [TdsController::class, 'index'])->name('tds.index');
    Route::get('tds/{tdsRecord}', [TdsController::class, 'show'])->name('tds.show');
    Route::patch('tds/{tdsRecord}/certificate', [TdsController::class, 'updateCertificate'])->name('tds.update-certificate');
    Route::get('tds/export/excel', [TdsController::class, 'export'])->name('tds.export');
    Route::get('tds/summary/view', [TdsController::class, 'summary'])->name('tds.summary');
});

// Follow-up Management
Route::middleware('auth')->group(function () {
    Route::resource('followups', FollowUpController::class);
    Route::patch('followups/{followUp}/mark-sent', [FollowUpController::class, 'markAsSent'])->name('followups.mark-sent');
    Route::get('followup-templates', [FollowUpController::class, 'getTemplates'])->name('followup-templates');
});

// File Management
Route::middleware('auth')->group(function () {
    Route::get('files', [FileController::class, 'index'])->name('files.index');
    Route::get('files/create', [FileController::class, 'create'])->name('files.create');
    Route::post('files', [FileController::class, 'store'])->name('files.store')->middleware('file.limits');
    Route::get('files/{file}', [FileController::class, 'show'])->name('files.show');
    Route::get('files/{file}/edit', [FileController::class, 'edit'])->name('files.edit');
    Route::put('files/{file}', [FileController::class, 'update'])->name('files.update');
    Route::delete('files/{file}', [FileController::class, 'destroy'])->name('files.destroy');
    Route::get('files/{file}/download', [FileController::class, 'download'])->name('files.download');
    Route::get('files/picker', [FileController::class, 'picker'])->name('files.picker');
});

// Automation Management
Route::middleware('auth')->prefix('automation')->name('automation.')->group(function () {
    Route::get('/dashboard', [AutomationController::class, 'dashboard'])->name('dashboard');
    Route::get('/workflows', [AutomationController::class, 'workflows'])->name('workflows');
    Route::post('/workflows', [AutomationController::class, 'createWorkflow'])->name('workflows.create');
    Route::put('/workflows/{workflow}', [AutomationController::class, 'updateWorkflow'])->name('workflows.update');
    Route::delete('/workflows/{workflow}', [AutomationController::class, 'deleteWorkflow'])->name('workflows.delete');
    Route::patch('/workflows/{workflow}/toggle', [AutomationController::class, 'toggleWorkflow'])->name('workflows.toggle');
    Route::post('/workflows/{workflow}/test', [AutomationController::class, 'testWorkflow'])->name('workflows.test');

    Route::get('/templates', [AutomationController::class, 'templates'])->name('templates');
    Route::post('/templates', [AutomationController::class, 'createTemplate'])->name('templates.create');
    Route::put('/templates/{template}', [AutomationController::class, 'updateTemplate'])->name('templates.update');

    Route::get('/preferences', [AutomationController::class, 'preferences'])->name('preferences');
    Route::put('/preferences', [AutomationController::class, 'updatePreferences'])->name('preferences.update');

    Route::post('/initialize-defaults', [AutomationController::class, 'initializeDefaults'])->name('initialize');
    Route::get('/analytics', [AutomationController::class, 'analyticsApi'])->name('analytics');
});

// Subscription Management
Route::middleware('auth')->group(function () {
    Route::get('subscriptions', [SubscriptionController::class, 'index'])->name('subscriptions.index');
    Route::get('subscriptions/plans', [SubscriptionController::class, 'plans'])->name('subscriptions.plans');
    Route::post('subscriptions/subscribe/{plan}', [SubscriptionController::class, 'subscribe'])->name('subscriptions.subscribe');
    Route::post('subscriptions/upgrade/{plan}', [SubscriptionController::class, 'upgrade'])->name('subscriptions.upgrade');
    Route::post('subscriptions/cancel', [SubscriptionController::class, 'cancel'])->name('subscriptions.cancel');
    Route::get('subscriptions/billing', [SubscriptionController::class, 'billing'])->name('subscriptions.billing');
});

// Payment Management
Route::middleware(['auth', 'rate.limit:payment'])->group(function () {
    Route::get('payment/{payment}/gateway', [PaymentController::class, 'gateway'])->name('payment.gateway');
    Route::post('payment/{payment}/paypal', [PaymentController::class, 'paypal'])->name('payment.paypal');
    Route::post('payment/{payment}/razorpay', [PaymentController::class, 'razorpay'])->name('payment.razorpay');
    Route::get('payment/{payment}/success', [PaymentController::class, 'success'])->name('payment.success');
    Route::get('payment/{payment}/failure', [PaymentController::class, 'failure'])->name('payment.failure');
});

// Payment Webhooks (no auth required)
Route::post('webhooks/paypal/invoice-payments', [PaymentController::class, 'paypalInvoiceWebhook'])->name('webhooks.paypal.invoice');
Route::post('webhooks/razorpay/invoice-payments', [PaymentController::class, 'razorpayInvoiceWebhook'])->name('webhooks.razorpay.invoice');

// Payment Webhooks (no auth required but with signature validation)
Route::post('webhooks/paypal', [PaymentController::class, 'paypalWebhook'])
    ->middleware('rate.limit:webhook')
    ->name('webhooks.paypal');
Route::post('webhooks/razorpay', [PaymentController::class, 'razorpayWebhook'])
    ->middleware('rate.limit:webhook')
    ->name('webhooks.razorpay');



require __DIR__.'/auth.php';
