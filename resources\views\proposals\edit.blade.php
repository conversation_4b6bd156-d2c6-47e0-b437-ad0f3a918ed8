<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Edit Proposal') }} - {{ $proposal->title }}
            </h2>
            <a href="{{ route('proposals.show', $proposal) }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                <i class="fas fa-arrow-left mr-2"></i>Back to Proposal
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <form method="POST" action="{{ route('proposals.update', $proposal) }}">
                @csrf
                @method('PUT')
                
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Title -->
                            <div class="md:col-span-2">
                                <label for="title" class="block text-sm font-medium text-gray-700">Proposal Title *</label>
                                <input type="text" name="title" id="title" value="{{ old('title', $proposal->title) }}" required
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('title')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Client -->
                            <div>
                                <label for="client_id" class="block text-sm font-medium text-gray-700">Client</label>
                                <select name="client_id" id="client_id" 
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="">Select a client (optional)</option>
                                    @foreach($clients as $client)
                                        <option value="{{ $client->id }}" 
                                                {{ old('client_id', $proposal->client_id) == $client->id ? 'selected' : '' }}>
                                            {{ $client->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('client_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Status -->
                            <div>
                                <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                                <select name="status" id="status" 
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="draft" {{ old('status', $proposal->status) === 'draft' ? 'selected' : '' }}>Draft</option>
                                    <option value="sent" {{ old('status', $proposal->status) === 'sent' ? 'selected' : '' }}>Sent</option>
                                    <option value="viewed" {{ old('status', $proposal->status) === 'viewed' ? 'selected' : '' }}>Viewed</option>
                                    <option value="accepted" {{ old('status', $proposal->status) === 'accepted' ? 'selected' : '' }}>Accepted</option>
                                    <option value="rejected" {{ old('status', $proposal->status) === 'rejected' ? 'selected' : '' }}>Rejected</option>
                                    <option value="expired" {{ old('status', $proposal->status) === 'expired' ? 'selected' : '' }}>Expired</option>
                                </select>
                                @error('status')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Total Amount -->
                            <div>
                                <label for="total_amount" class="block text-sm font-medium text-gray-700">Total Amount</label>
                                <div class="mt-1 relative rounded-md shadow-sm">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm">$</span>
                                    </div>
                                    <input type="number" name="total_amount" id="total_amount" step="0.01" min="0" 
                                           value="{{ old('total_amount', $proposal->total_amount) }}"
                                           class="pl-7 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                </div>
                                @error('total_amount')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Valid Until -->
                            <div>
                                <label for="valid_until" class="block text-sm font-medium text-gray-700">Valid Until</label>
                                <input type="date" name="valid_until" id="valid_until" 
                                       value="{{ old('valid_until', $proposal->valid_until ? $proposal->valid_until->format('Y-m-d') : '') }}"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('valid_until')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Description -->
                            <div class="md:col-span-2">
                                <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                                <textarea name="description" id="description" rows="3" 
                                          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('description', $proposal->description) }}</textarea>
                                @error('description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Content -->
                            <div class="md:col-span-2" x-data="{ aiLoading: false, aiLoadingText: 'Processing...' }">
                                <div class="flex justify-between items-center mb-2">
                                    <label for="content" class="block text-sm font-medium text-gray-700">Proposal Content *</label>
                                    @if(auth()->user()->currentPlan && auth()->user()->currentPlan->slug === 'business')
                                        <button type="button" @click="improveContent()"
                                                class="text-sm bg-blue-500 hover:bg-blue-700 text-white px-3 py-1 rounded">
                                            <i class="fas fa-wand-magic-sparkles mr-1"></i>AI Improve
                                        </button>
                                    @endif
                                </div>
                                <div class="mt-1">
                                    <textarea name="content" id="content" rows="15" required
                                              class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('content', $proposal->content) }}</textarea>
                                </div>
                                @error('content')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror

                                <!-- AI Loading Indicator -->
                                <div x-show="aiLoading" class="mt-2 flex items-center text-sm text-blue-600">
                                    <i class="fas fa-spinner fa-spin mr-2"></i>
                                    <span x-text="aiLoadingText">Processing...</span>
                                </div>

                                <script>
                                    async function improveContent() {
                                        const contentElement = document.getElementById('content');
                                        const currentContent = contentElement.value;

                                        if (!currentContent.trim()) {
                                            alert('Please enter some content first.');
                                            return;
                                        }

                                        // Set loading state
                                        const component = Alpine.$data(contentElement.closest('[x-data]'));
                                        component.aiLoading = true;
                                        component.aiLoadingText = 'AI is improving your proposal content...';

                                        try {
                                            const response = await fetch('/ai-assistant/improve-proposal', {
                                                method: 'POST',
                                                headers: {
                                                    'Content-Type': 'application/json',
                                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                                                },
                                                body: JSON.stringify({
                                                    content: currentContent,
                                                    focus_areas: ['clarity', 'persuasion', 'professionalism']
                                                })
                                            });

                                            const data = await response.json();

                                            if (data.improved_content) {
                                                contentElement.value = data.improved_content;
                                            } else {
                                                alert(data.error || 'Failed to improve content');
                                            }
                                        } catch (error) {
                                            console.error('Error improving content:', error);
                                            alert('An error occurred while improving content');
                                        } finally {
                                            component.aiLoading = false;
                                        }
                                    }
                                </script>
                            </div>

                            <!-- Internal Notes -->
                            <div class="md:col-span-2">
                                <label for="internal_notes" class="block text-sm font-medium text-gray-700">Internal Notes</label>
                                <textarea name="internal_notes" id="internal_notes" rows="3" 
                                          placeholder="Private notes for your reference..."
                                          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('internal_notes', $proposal->internal_notes) }}</textarea>
                                @error('internal_notes')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Client Notes (if exists) -->
                            @if($proposal->client_notes)
                                <div class="md:col-span-2">
                                    <label for="client_notes" class="block text-sm font-medium text-gray-700">Client Notes</label>
                                    <textarea name="client_notes" id="client_notes" rows="3" 
                                              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('client_notes', $proposal->client_notes) }}</textarea>
                                    @error('client_notes')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            @endif
                        </div>

                        <!-- Action Buttons -->
                        <div class="mt-6 flex justify-between">
                            <div class="flex space-x-3">
                                <a href="{{ route('proposals.show', $proposal) }}" 
                                   class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                    Cancel
                                </a>
                                <button type="submit" 
                                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                    <i class="fas fa-save mr-2"></i>Update Proposal
                                </button>
                            </div>
                            
                            <div class="flex space-x-3">
                                <form method="POST" action="{{ route('proposals.create-revision', $proposal) }}" class="inline">
                                    @csrf
                                    <button type="submit" 
                                            class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                                        <i class="fas fa-copy mr-2"></i>Create Revision
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</x-app-layout>
