<x-app-layout>
    <div class="min-h-screen bg-gradient-to-br from-success-50 via-white to-primary-50 py-12">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Success Animation Container -->
            <div class="text-center mb-12">
                <div class="relative inline-block">
                    <!-- Success Circle with Animation -->
                    <div class="w-32 h-32 bg-gradient-to-br from-success-500 to-success-600 rounded-full flex items-center justify-center mx-auto mb-8 shadow-glow-lg animate-bounce-in">
                        <i class="fas fa-check text-5xl text-white"></i>
                    </div>
                    
                    <!-- Celebration Confetti -->
                    <div class="absolute -top-4 -left-4 w-4 h-4 bg-primary-400 rounded-full animate-ping"></div>
                    <div class="absolute -top-2 -right-6 w-3 h-3 bg-accent-400 rounded-full animate-ping" style="animation-delay: 0.2s"></div>
                    <div class="absolute -bottom-2 -left-6 w-3 h-3 bg-warning-400 rounded-full animate-ping" style="animation-delay: 0.4s"></div>
                    <div class="absolute -bottom-4 -right-4 w-4 h-4 bg-success-400 rounded-full animate-ping" style="animation-delay: 0.6s"></div>
                </div>
                
                <h1 class="text-4xl lg:text-5xl font-bold text-secondary-900 mb-4">
                    Payment Successful! 🎉
                </h1>
                <p class="text-xl text-secondary-600 max-w-2xl mx-auto">
                    Welcome to your new plan! Your account has been upgraded and you now have access to all premium features.
                </p>
            </div>

            <!-- Plan Details Card -->
            <div class="card shadow-card-lg mb-8">
                <div class="card-header bg-gradient-to-r from-success-50 to-primary-50">
                    <h2 class="text-2xl font-bold text-secondary-900">Subscription Details</h2>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div>
                            <h3 class="font-semibold text-secondary-900 mb-4">Plan Information</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-secondary-600">Plan:</span>
                                    <span class="font-semibold text-secondary-900">{{ $subscription->plan->name ?? 'Pro Plan' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-secondary-600">Billing Cycle:</span>
                                    <span class="font-semibold text-secondary-900">{{ ucfirst($subscription->plan->billing_cycle ?? 'Monthly') }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-secondary-600">Amount Paid:</span>
                                    <span class="font-semibold text-success-600">₹{{ $subscription->plan->price ?? '199' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-secondary-600">Next Billing:</span>
                                    <span class="font-semibold text-secondary-900">{{ $subscription->next_billing_date ?? now()->addMonth()->format('M d, Y') }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <h3 class="font-semibold text-secondary-900 mb-4">What's Next?</h3>
                            <div class="space-y-4">
                                <div class="flex items-start space-x-3">
                                    <div class="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center mt-0.5">
                                        <i class="fas fa-check text-primary-600 text-xs"></i>
                                    </div>
                                    <div>
                                        <div class="font-medium text-secondary-900">Access Premium Features</div>
                                        <div class="text-sm text-secondary-600">All premium features are now unlocked</div>
                                    </div>
                                </div>
                                
                                <div class="flex items-start space-x-3">
                                    <div class="w-6 h-6 bg-success-100 rounded-full flex items-center justify-center mt-0.5">
                                        <i class="fas fa-envelope text-success-600 text-xs"></i>
                                    </div>
                                    <div>
                                        <div class="font-medium text-secondary-900">Confirmation Email</div>
                                        <div class="text-sm text-secondary-600">Receipt sent to your email address</div>
                                    </div>
                                </div>
                                
                                <div class="flex items-start space-x-3">
                                    <div class="w-6 h-6 bg-accent-100 rounded-full flex items-center justify-center mt-0.5">
                                        <i class="fas fa-cog text-accent-600 text-xs"></i>
                                    </div>
                                    <div>
                                        <div class="font-medium text-secondary-900">Manage Subscription</div>
                                        <div class="text-sm text-secondary-600">Update billing info anytime in settings</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="card hover:shadow-card-hover transition-all duration-300">
                    <div class="card-body text-center">
                        <div class="w-12 h-12 bg-primary-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-tachometer-alt text-primary-600 text-xl"></i>
                        </div>
                        <h3 class="font-semibold text-secondary-900 mb-2">Go to Dashboard</h3>
                        <p class="text-secondary-600 text-sm mb-4">Start managing your business</p>
                        <x-ui.button href="{{ route('dashboard') }}" variant="primary" size="sm" class="w-full">
                            Dashboard
                        </x-ui.button>
                    </div>
                </div>
                
                <div class="card hover:shadow-card-hover transition-all duration-300">
                    <div class="card-body text-center">
                        <div class="w-12 h-12 bg-success-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-file-invoice-dollar text-success-600 text-xl"></i>
                        </div>
                        <h3 class="font-semibold text-secondary-900 mb-2">Create Invoice</h3>
                        <p class="text-secondary-600 text-sm mb-4">Send your first professional invoice</p>
                        <x-ui.button href="{{ route('invoices.create') }}" variant="outline" size="sm" class="w-full">
                            New Invoice
                        </x-ui.button>
                    </div>
                </div>
                
                <div class="card hover:shadow-card-hover transition-all duration-300">
                    <div class="card-body text-center">
                        <div class="w-12 h-12 bg-accent-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-cog text-accent-600 text-xl"></i>
                        </div>
                        <h3 class="font-semibold text-secondary-900 mb-2">Account Settings</h3>
                        <p class="text-secondary-600 text-sm mb-4">Manage your subscription</p>
                        <x-ui.button href="{{ route('profile.edit') }}" variant="ghost" size="sm" class="w-full">
                            Settings
                        </x-ui.button>
                    </div>
                </div>
            </div>

            <!-- Support Section -->
            <div class="card">
                <div class="card-body text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-primary-100 to-primary-200 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-headset text-2xl text-primary-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-secondary-900 mb-2">Need Help Getting Started?</h3>
                    <p class="text-secondary-600 mb-6">Our support team is here to help you make the most of your new plan.</p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <x-ui.button href="#" variant="outline" icon="fas fa-book">
                            View Documentation
                        </x-ui.button>
                        <x-ui.button href="#" variant="primary" icon="fas fa-comments">
                            Contact Support
                        </x-ui.button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
