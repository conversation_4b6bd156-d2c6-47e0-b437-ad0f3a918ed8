<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\InvoicePayment;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ClientPortalService
{
    /**
     * Get invoice by public token for client viewing.
     */
    public function getInvoiceByPublicToken(int $invoiceId, string $token): ?Invoice
    {
        $invoice = Invoice::with(['client', 'items', 'user', 'invoicePayments'])
            ->where('id', $invoiceId)
            ->where('public_token', $token)
            ->first();

        if ($invoice) {
            $invoice->markAsViewedByClient();
        }

        return $invoice;
    }

    /**
     * Get invoice by payment token for payment processing.
     */
    public function getInvoiceByPaymentToken(int $invoiceId, string $token): ?Invoice
    {
        return Invoice::with(['client', 'user', 'invoicePayments'])
            ->where('id', $invoiceId)
            ->where('payment_token', $token)
            ->first();
    }

    /**
     * Check if client can make payment for invoice.
     */
    public function canMakePayment(Invoice $invoice): array
    {
        $result = ['can_pay' => true, 'reason' => null];

        // Check if invoice is already fully paid
        if ($invoice->status === 'paid' || $invoice->isFullyPaidByClients()) {
            $result = ['can_pay' => false, 'reason' => 'Invoice is already fully paid'];
        }

        // Check if invoice is cancelled
        if ($invoice->status === 'cancelled') {
            $result = ['can_pay' => false, 'reason' => 'Invoice has been cancelled'];
        }

        // Check payment attempts limit (max 5 attempts per day)
        if ($invoice->getTodayPaymentAttempts() >= 5) {
            $result = ['can_pay' => false, 'reason' => 'Maximum payment attempts reached for today'];
        }

        return $result;
    }

    /**
     * Create payment record for invoice.
     */
    public function createInvoicePayment(Invoice $invoice, array $paymentData): InvoicePayment
    {
        return DB::transaction(function () use ($invoice, $paymentData) {
            $payment = InvoicePayment::create([
                'invoice_id' => $invoice->id,
                'gateway' => $paymentData['gateway'],
                'amount' => $paymentData['amount'],
                'currency' => $paymentData['currency'] ?? config('services.currency.code', 'INR'),
                'status' => 'pending',
                'client_email' => $invoice->client->email,
                'client_name' => $invoice->client->name,
            ]);

            // Record payment attempt
            $invoice->recordPaymentAttempt([
                'payment_id' => $payment->payment_id,
                'gateway' => $paymentData['gateway'],
                'amount' => $paymentData['amount'],
                'status' => 'initiated'
            ]);

            return $payment;
        });
    }

    /**
     * Process successful payment.
     */
    public function processSuccessfulPayment(InvoicePayment $payment, array $gatewayResponse): void
    {
        DB::transaction(function () use ($payment, $gatewayResponse) {
            // Update payment record
            $payment->update([
                'status' => 'completed',
                'gateway_payment_id' => $gatewayResponse['gateway_payment_id'] ?? null,
                'gateway_response' => $gatewayResponse,
                'paid_at' => now(),
            ]);

            $invoice = $payment->invoice;

            // Check if invoice is now fully paid
            if ($invoice->isFullyPaidByClients()) {
                $invoice->update([
                    'status' => 'paid',
                    'paid_date' => now(),
                ]);

                // Log successful payment
                Log::info('Invoice fully paid by client', [
                    'invoice_id' => $invoice->id,
                    'invoice_number' => $invoice->invoice_number,
                    'payment_id' => $payment->payment_id,
                    'amount' => $payment->amount,
                    'gateway' => $payment->gateway,
                    'total_amount' => $invoice->total_amount,
                    'client_email' => $invoice->client->email,
                ]);

                // Send payment confirmation emails
                $this->sendPaymentConfirmationEmails($invoice, $payment, 'full_payment');
            } else {
                // Partial payment received
                Log::info('Partial payment received for invoice', [
                    'invoice_id' => $invoice->id,
                    'invoice_number' => $invoice->invoice_number,
                    'payment_id' => $payment->payment_id,
                    'amount' => $payment->amount,
                    'remaining_amount' => $invoice->remaining_amount,
                    'gateway' => $payment->gateway,
                ]);

                // Send partial payment notification emails
                $this->sendPaymentConfirmationEmails($invoice, $payment, 'partial_payment');
            }

            // Record successful payment attempt
            $invoice->recordPaymentAttempt([
                'payment_id' => $payment->payment_id,
                'gateway' => $payment->gateway,
                'amount' => $payment->amount,
                'status' => 'completed',
                'gateway_payment_id' => $payment->gateway_payment_id
            ]);
        });
    }

    /**
     * Process failed payment.
     */
    public function processFailedPayment(InvoicePayment $payment, string $failureReason, array $gatewayResponse = []): void
    {
        DB::transaction(function () use ($payment, $failureReason, $gatewayResponse) {
            // Update payment record
            $payment->update([
                'status' => 'failed',
                'failure_reason' => $failureReason,
                'gateway_response' => $gatewayResponse,
            ]);

            $invoice = $payment->invoice;

            // Record failed payment attempt
            $invoice->recordPaymentAttempt([
                'payment_id' => $payment->payment_id,
                'gateway' => $payment->gateway,
                'amount' => $payment->amount,
                'status' => 'failed',
                'failure_reason' => $failureReason
            ]);

            // Log failed payment
            Log::warning('Invoice payment failed', [
                'invoice_id' => $invoice->id,
                'invoice_number' => $invoice->invoice_number,
                'payment_id' => $payment->payment_id,
                'failure_reason' => $failureReason,
                'gateway' => $payment->gateway,
            ]);
        });
    }

    /**
     * Get payment summary for invoice.
     */
    public function getPaymentSummary(Invoice $invoice): array
    {
        $completedPayments = $invoice->completedPayments;
        $totalPaid = $completedPayments->sum('amount');
        $remainingAmount = max(0, $invoice->total_amount - $totalPaid);

        return [
            'total_amount' => $invoice->total_amount,
            'total_paid' => $totalPaid,
            'remaining_amount' => $remainingAmount,
            'is_fully_paid' => $remainingAmount <= 0.01,
            'payment_count' => $completedPayments->count(),
            'last_payment_date' => $completedPayments->max('paid_at'),
            'payments' => $completedPayments->map(function ($payment) {
                return [
                    'id' => $payment->id,
                    'amount' => $payment->amount,
                    'gateway' => $payment->gateway,
                    'paid_at' => $payment->paid_at,
                    'formatted_amount' => $payment->formatted_amount,
                ];
            }),
        ];
    }

    /**
     * Validate payment amount.
     */
    public function validatePaymentAmount(Invoice $invoice, float $amount): array
    {
        $result = ['valid' => true, 'message' => null];

        if ($amount <= 0) {
            $result = ['valid' => false, 'message' => 'Payment amount must be greater than zero'];
        } elseif ($amount > $invoice->remaining_amount) {
            $result = ['valid' => false, 'message' => 'Payment amount cannot exceed remaining invoice amount'];
        } elseif ($amount < 1) {
            $result = ['valid' => false, 'message' => 'Minimum payment amount is ₹1'];
        }

        return $result;
    }

    /**
     * Get invoice statistics for client portal.
     */
    public function getInvoiceStats(Invoice $invoice): array
    {
        return [
            'days_since_issued' => $invoice->invoice_date->diffInDays(now()),
            'days_until_due' => $invoice->due_date->diffInDays(now(), false),
            'is_overdue' => $invoice->isOverdue(),
            'payment_attempts_today' => $invoice->getTodayPaymentAttempts(),
            'total_payment_attempts' => count($invoice->payment_attempts ?? []),
            'viewed_by_client' => $invoice->hasBeenViewedByClient(),
            'client_viewed_at' => $invoice->client_viewed_at,
        ];
    }

    /**
     * Send payment confirmation emails to both client and freelancer.
     */
    private function sendPaymentConfirmationEmails(Invoice $invoice, InvoicePayment $payment, string $type): void
    {
        try {
            // Send confirmation email to client
            \Illuminate\Support\Facades\Mail::to($invoice->client->email)
                ->send(new \App\Mail\PaymentReceivedMail($invoice, $payment, 'client'));

            // Send notification email to freelancer
            \Illuminate\Support\Facades\Mail::to($invoice->user->email)
                ->send(new \App\Mail\PaymentReceivedMail($invoice, $payment, 'freelancer'));

            Log::info('Payment confirmation emails sent', [
                'invoice_id' => $invoice->id,
                'payment_id' => $payment->id,
                'client_email' => $invoice->client->email,
                'freelancer_email' => $invoice->user->email,
                'payment_type' => $type,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send payment confirmation emails', [
                'invoice_id' => $invoice->id,
                'payment_id' => $payment->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Check and update overdue invoice status.
     */
    public function checkAndUpdateOverdueStatus(Invoice $invoice): bool
    {
        if ($invoice->status === 'sent' && $invoice->due_date->isPast()) {
            $invoice->update(['status' => 'overdue']);

            Log::info('Invoice marked as overdue', [
                'invoice_id' => $invoice->id,
                'invoice_number' => $invoice->invoice_number,
                'due_date' => $invoice->due_date,
                'days_overdue' => $invoice->due_date->diffInDays(now()),
            ]);

            return true;
        }

        return false;
    }

    /**
     * Get payment analytics for invoice.
     */
    public function getPaymentAnalytics(Invoice $invoice): array
    {
        $payments = $invoice->invoicePayments;
        $completedPayments = $payments->where('status', 'completed');
        $failedPayments = $payments->where('status', 'failed');

        return [
            'total_attempts' => $payments->count(),
            'successful_payments' => $completedPayments->count(),
            'failed_attempts' => $failedPayments->count(),
            'success_rate' => $payments->count() > 0 ? ($completedPayments->count() / $payments->count()) * 100 : 0,
            'average_payment_amount' => $completedPayments->avg('amount') ?? 0,
            'payment_methods_used' => $completedPayments->pluck('gateway')->unique()->values(),
            'first_payment_date' => $completedPayments->min('paid_at'),
            'last_payment_date' => $completedPayments->max('paid_at'),
            'total_processing_time' => $completedPayments->count() > 0 ?
                $completedPayments->avg(function ($payment) {
                    return $payment->paid_at->diffInMinutes($payment->created_at);
                }) : 0,
        ];
    }
}
