@props([
    'size' => 'md',
    'color' => 'blue',
    'text' => null,
    'overlay' => false,
    'fullscreen' => false
])

@php
$sizeClasses = [
    'sm' => 'w-4 h-4',
    'md' => 'w-6 h-6',
    'lg' => 'w-8 h-8',
    'xl' => 'w-12 h-12'
];

$colorClasses = [
    'primary' => 'text-primary-600',
    'secondary' => 'text-secondary-600',
    'success' => 'text-success-600',
    'danger' => 'text-danger-600',
    'warning' => 'text-warning-600',
    'white' => 'text-white'
];

$spinnerSize = $sizeClasses[$size] ?? $sizeClasses['md'];
$spinnerColor = $colorClasses[$color] ?? $colorClasses['primary'];
@endphp

@if($fullscreen)
    <div class="fixed inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50">
        <div class="text-center">
            <div class="inline-block {{ $spinnerSize }} {{ $spinnerColor }} animate-spin">
                <svg class="w-full h-full" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </div>
            @if($text)
                <p class="mt-3 text-sm text-gray-600">{{ $text }}</p>
            @endif
        </div>
    </div>
@elseif($overlay)
    <div class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
        <div class="text-center">
            <div class="inline-block {{ $spinnerSize }} {{ $spinnerColor }} animate-spin">
                <svg class="w-full h-full" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </div>
            @if($text)
                <p class="mt-2 text-sm text-gray-600">{{ $text }}</p>
            @endif
        </div>
    </div>
@else
    <div class="flex items-center {{ $attributes->get('class') }}">
        <div class="inline-block {{ $spinnerSize }} {{ $spinnerColor }} animate-spin">
            <svg class="w-full h-full" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
        </div>
        @if($text)
            <span class="ml-2 text-sm text-gray-600">{{ $text }}</span>
        @endif
    </div>
@endif
