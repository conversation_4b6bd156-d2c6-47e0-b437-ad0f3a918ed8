<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions
        $permissions = [
            // Client permissions
            'view clients',
            'create clients',
            'edit clients',
            'delete clients',

            // Invoice permissions
            'view invoices',
            'create invoices',
            'edit invoices',
            'delete invoices',
            'send invoices',
            'track payments',
            'export payment data',

            // Proposal permissions
            'view proposals',
            'create proposals',
            'edit proposals',
            'delete proposals',
            'send proposals',

            // Contract permissions
            'view contracts',
            'create contracts',
            'edit contracts',
            'delete contracts',
            'send contracts',

            // TDS permissions
            'view tds',
            'create tds',
            'edit tds',
            'export tds',

            // Follow-up permissions
            'view followups',
            'create followups',
            'edit followups',
            'delete followups',
            'send followups',

            // Lead permissions
            'view leads',
            'create leads',
            'edit leads',
            'delete leads',

            // Lead permissions
            'view leads',
            'create leads',
            'edit leads',
            'delete leads',
            'convert leads',
            'view lead analytics',
            'export leads',

            // Expense permissions
            'view expenses',
            'create expenses',
            'edit expenses',
            'delete expenses',
            'submit expenses',
            'approve expenses',
            'reject expenses',
            'export expenses',

            // Expense Category permissions (admin only)
            'view expense categories',
            'create expense categories',
            'edit expense categories',
            'delete expense categories',

            // Expense Report permissions
            'view expense reports',
            'export expense reports',

            // Project permissions
            'view projects',
            'create projects',
            'edit projects',
            'delete projects',

            // Task permissions
            'view tasks',
            'create tasks',
            'edit tasks',
            'delete tasks',

            // Time Entry permissions
            'view time entries',
            'create time entries',
            'edit time entries',
            'delete time entries',

            // Dashboard permissions
            'view dashboard',
            'view reports',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles
        $freelancerRole = Role::firstOrCreate(['name' => 'freelancer']);
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $clientRole = Role::firstOrCreate(['name' => 'client']);

        // Assign permissions to roles
        $freelancerRole->givePermissionTo([
            'view clients', 'create clients', 'edit clients', 'delete clients',
            'view invoices', 'create invoices', 'edit invoices', 'delete invoices', 'send invoices', 'track payments', 'export payment data',
            'view proposals', 'create proposals', 'edit proposals', 'delete proposals', 'send proposals',
            'view contracts', 'create contracts', 'edit contracts', 'delete contracts', 'send contracts',
            'view tds', 'create tds', 'edit tds', 'export tds',
            'view followups', 'create followups', 'edit followups', 'delete followups', 'send followups',
            'view leads', 'create leads', 'edit leads', 'delete leads',
            'view expenses', 'create expenses', 'edit expenses', 'delete expenses', 'submit expenses', 'export expenses',
            'view expense reports', 'export expense reports',
            'view projects', 'create projects', 'edit projects', 'delete projects',
            'view tasks', 'create tasks', 'edit tasks', 'delete tasks',
            'view time entries', 'create time entries', 'edit time entries', 'delete time entries',
            'view dashboard', 'view reports',
        ]);

        $adminRole->givePermissionTo(Permission::all());

        $clientRole->givePermissionTo([
            'view invoices',
            'view contracts',
        ]);

        // Create default admin user
        $adminUser = \App\Models\User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => \Illuminate\Support\Facades\Hash::make('admin123'),
                'email_verified_at' => now(),
                'business_name' => 'Freeligo Admin',
            ]
        );

        // Assign admin role to the default admin user
        if (!$adminUser->hasRole('admin')) {
            $adminUser->assignRole('admin');
        }
    }
}
