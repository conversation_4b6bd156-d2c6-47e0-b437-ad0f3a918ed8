<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'business_name',
        'address',
        'phone',
        'gst_number',
        'logo_path',
        'bank_name',
        'account_number',
        'ifsc_code',
        'pan_number',
        'current_plan_id',
        'usage_stats',
        'trial_ends_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'usage_stats' => 'array',
            'trial_ends_at' => 'datetime',
        ];
    }

    /**
     * Get the clients for the user.
     */
    public function clients(): HasMany
    {
        return $this->hasMany(Client::class);
    }

    /**
     * Get the invoices for the user.
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Get the contracts for the user.
     */
    public function contracts(): HasMany
    {
        return $this->hasMany(Contract::class);
    }

    /**
     * Get the proposals for the user.
     */
    public function proposals(): HasMany
    {
        return $this->hasMany(Proposal::class);
    }

    /**
     * Get the follow-ups for the user.
     */
    public function followUps(): HasMany
    {
        return $this->hasMany(FollowUp::class);
    }

    /**
     * Get the leads for the user.
     */
    public function leads(): HasMany
    {
        return $this->hasMany(Lead::class);
    }

    /**
     * Get the TDS records for the user.
     */
    public function tdsRecords(): HasMany
    {
        return $this->hasMany(TdsRecord::class);
    }

    /**
     * Get the current plan for the user.
     */
    public function currentPlan(): BelongsTo
    {
        return $this->belongsTo(Plan::class, 'current_plan_id');
    }

    /**
     * Get the businesses this user belongs to.
     */
    public function businesses(): BelongsToMany
    {
        return $this->belongsToMany(Business::class, 'business_users')
            ->withPivot(['role', 'permissions', 'is_owner', 'joined_at', 'status'])
            ->withTimestamps();
    }

    /**
     * Get the primary business for this user (the one they own or first joined).
     */
    public function primaryBusiness()
    {
        return $this->businesses()
            ->wherePivot('is_owner', true)
            ->first() ?? $this->businesses()->first();
    }

    /**
     * Get businesses where user is owner.
     */
    public function ownedBusinesses(): BelongsToMany
    {
        return $this->businesses()->wherePivot('is_owner', true);
    }

    /**
     * Check if user owns any business.
     */
    public function ownsAnyBusiness(): bool
    {
        return $this->ownedBusinesses()->exists();
    }

    /**
     * Check if user belongs to a specific business.
     */
    public function belongsToBusiness(int $businessId): bool
    {
        return $this->businesses()->where('business_id', $businessId)->exists();
    }

    /**
     * Get user's role in a specific business.
     */
    public function getRoleInBusiness(int $businessId): ?string
    {
        $business = $this->businesses()->where('business_id', $businessId)->first();
        return $business ? $business->pivot->role : null;
    }

    /**
     * Check if user is owner of a specific business.
     */
    public function isOwnerOfBusiness(int $businessId): bool
    {
        $business = $this->businesses()->where('business_id', $businessId)->first();
        return $business ? $business->pivot->is_owner : false;
    }

    /**
     * Check if user is a freelancer (doesn't belong to any business or owns a freelancer business).
     */
    public function isFreelancer(): bool
    {
        $primaryBusiness = $this->primaryBusiness();
        return !$primaryBusiness || $primaryBusiness->isFreelancer();
    }

    /**
     * Check if user is a business member.
     */
    public function isBusinessMember(): bool
    {
        return $this->businesses()->exists();
    }

    /**
     * Get effective business context (primary business).
     */
    public function getEffectiveBusiness()
    {
        return $this->primaryBusiness();
    }

    /**
     * Get current business context from session or primary business.
     */
    public function getCurrentBusinessContext()
    {
        // Check session for current business context
        $currentBusinessId = session('current_business_id');

        if ($currentBusinessId && $this->belongsToBusiness($currentBusinessId)) {
            return $this->businesses()->find($currentBusinessId);
        }

        return $this->primaryBusiness();
    }

    /**
     * Switch to a different business context (store in session).
     */
    public function switchToBusiness(int $businessId): bool
    {
        if ($this->belongsToBusiness($businessId)) {
            session(['current_business_id' => $businessId]);
            return true;
        }
        return false;
    }

    /**
     * Get all subscriptions for the user.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(UserSubscription::class);
    }

    /**
     * Get the active subscription for the user.
     */
    public function activeSubscription(): HasOne
    {
        return $this->hasOne(UserSubscription::class)->where('status', 'active');
    }

    /**
     * Get all payments for the user.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get all files for the user.
     */
    public function files(): HasMany
    {
        return $this->hasMany(File::class);
    }

    /**
     * Get all tasks created by the user.
     */
    public function createdTasks(): HasMany
    {
        return $this->hasMany(Task::class, 'created_by');
    }

    /**
     * Get all tasks assigned to the user.
     */
    public function assignedTasks(): HasMany
    {
        return $this->hasMany(Task::class, 'assigned_to');
    }

    /**
     * Get the projects owned by the user.
     */
    public function projects(): HasMany
    {
        return $this->hasMany(Project::class);
    }

    /**
     * Get the tasks created by the user.
     */
    public function tasks(): HasMany
    {
        return $this->hasMany(Task::class, 'created_by');
    }

    /**
     * Get the time entries for the user.
     */
    public function timeEntries(): HasMany
    {
        return $this->hasMany(TimeEntry::class);
    }

    /**
     * Get the projects where user is a member.
     */
    public function memberProjects(): BelongsToMany
    {
        return $this->belongsToMany(Project::class, 'project_members')
            ->withPivot(['role', 'hourly_rate', 'can_track_time', 'can_manage_tasks', 'can_view_reports', 'joined_at', 'left_at'])
            ->withTimestamps();
    }

    /**
     * Get the project member records for the user.
     */
    public function projectMemberships(): HasMany
    {
        return $this->hasMany(ProjectMember::class);
    }

    /**
     * Get all workflows for the user.
     */
    public function workflows(): HasMany
    {
        return $this->hasMany(Workflow::class);
    }

    /**
     * Get all expenses for the user.
     */
    public function expenses(): HasMany
    {
        return $this->hasMany(Expense::class);
    }



    /**
     * Check if user has an active subscription.
     */
    public function hasActiveSubscription(): bool
    {
        return $this->activeSubscription()->exists();
    }

    /**
     * Get current usage for a feature.
     */
    public function getCurrentUsage(string $featureKey): int
    {
        $stats = $this->usage_stats ?? [];
        return $stats[$featureKey] ?? 0;
    }

    /**
     * Increment usage for a feature.
     */
    public function incrementUsage(string $featureKey, int $amount = 1): void
    {
        $stats = $this->usage_stats ?? [];
        $stats[$featureKey] = ($stats[$featureKey] ?? 0) + $amount;
        $this->update(['usage_stats' => $stats]);
    }

    /**
     * Check if user can perform an action based on plan limits.
     */
    public function canPerformAction(string $featureKey): bool
    {
        if (!$this->currentPlan) {
            return false; // No plan assigned
        }

        $limit = $this->currentPlan->getFeatureLimit($featureKey);
        if ($limit === null) {
            return true; // Unlimited
        }

        $currentUsage = $this->getCurrentUsage($featureKey);
        return $currentUsage < $limit;
    }

    /**
     * Check if user has a specific feature.
     */
    public function hasFeature(string $featureKey): bool
    {
        if (!$this->currentPlan) {
            return false;
        }

        $feature = $this->currentPlan->getFeature($featureKey);
        if (!$feature) {
            return false;
        }

        // For boolean features, check the value
        if ($feature->feature_type === 'boolean') {
            return $feature->getBooleanValue();
        }

        // For limit features, check if it's not zero
        if ($feature->feature_type === 'limit') {
            return $feature->feature_value !== '0';
        }

        return true;
    }

    /**
     * Get feature limit for user.
     */
    public function getFeatureLimit(string $featureKey): int|string
    {
        if (!$this->currentPlan) {
            return 0;
        }

        return $this->currentPlan->getFeatureLimit($featureKey);
    }



    /**
     * Get all projects user is involved in (owned or member).
     */
    public function getAllInvolvedProjects()
    {
        return Project::where(function ($query) {
            $query->where('user_id', $this->id)
                  ->orWhereHas('members', function ($memberQuery) {
                      $memberQuery->where('user_id', $this->id);
                  });
        });
    }

    /**
     * Check if user can access a specific project.
     */
    public function canAccessProject(Project $project): bool
    {
        // Owner can always access
        if ($project->user_id === $this->id) {
            return true;
        }

        // Project member can access
        return $project->members()->where('user_id', $this->id)->exists();
    }



    /**
     * Reset monthly usage stats.
     */
    public function resetMonthlyUsage(): void
    {
        $this->update(['usage_stats' => []]);
    }
}
