<?php

namespace App\Repositories;

use App\Models\ExpenseCategory;

class ExpenseCategoryRepository extends BaseRepository
{
    public function __construct(ExpenseCategory $model)
    {
        parent::__construct($model);
    }

    protected function getModel(): \Illuminate\Database\Eloquent\Model
    {
        return new ExpenseCategory();
    }

    /**
     * Get active categories ordered by sort order
     */
    public function getActive()
    {
        return $this->model->active()->ordered()->limit(200)->get();
    }

    /**
     * Get categories with expense counts
     */
    public function getWithExpenseCounts()
    {
        return $this->model->withCount('expenses')
                          ->ordered()
                          ->limit(200)
                          ->get();
    }

    /**
     * Get categories for dropdown/select options
     */
    public function getForDropdown()
    {
        return $this->model->active()
                          ->ordered()
                          ->pluck('name', 'id');
    }

    /**
     * Search categories by name or description
     */
    public function search(string $term, array $fields = ['name', 'description']): ExpenseCategoryRepository
    {
        $this->model = $this->model->where(function ($query) use ($term, $fields) {
            foreach ($fields as $field) {
                $query->orWhere($field, 'like', "%{$term}%");
            }
        });

        return $this;
    }

    /**
     * Get categories by tax deductible status
     */
    public function getByTaxDeductible(bool $taxDeductible = true)
    {
        return $this->model->where('is_tax_deductible', $taxDeductible)
                          ->active()
                          ->ordered()
                          ->limit(200)
                          ->get();
    }

    /**
     * Get next sort order
     */
    public function getNextSortOrder(): int
    {
        return $this->model->max('sort_order') + 1;
    }

    /**
     * Check if slug exists
     */
    public function slugExists(string $slug, int $excludeId = null): bool
    {
        $query = $this->model->where('slug', $slug);
        
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }
        
        return $query->exists();
    }

    /**
     * Get category usage statistics
     */
    public function getUsageStats(int $categoryId): array
    {
        $category = $this->model->withCount([
            'expenses',
            'expenses as total_amount' => function ($query) {
                $query->selectRaw('SUM(amount + tax_amount)');
            }
        ])->find($categoryId);

        if (!$category) {
            return [];
        }

        return [
            'total_expenses' => $category->expenses_count,
            'total_amount' => $category->total_amount ?? 0,
            'average_amount' => $category->expenses_count > 0 
                ? ($category->total_amount ?? 0) / $category->expenses_count 
                : 0,
        ];
    }

    /**
     * Get categories with recent activity
     */
    public function getWithRecentActivity(int $days = 30)
    {
        return $this->model->withCount([
            'expenses as recent_expenses_count' => function ($query) use ($days) {
                $query->where('created_at', '>=', now()->subDays($days));
            }
        ])->ordered()->get();
    }
}
