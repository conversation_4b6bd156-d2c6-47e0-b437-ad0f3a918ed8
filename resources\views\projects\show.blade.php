<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ $project->name }}</h1>
                <p class="text-gray-600 mt-1">Project Details</p>
            </div>
            <div class="flex space-x-3">
                @can('update', $project)
                    <a href="{{ route('projects.edit', $project) }}" class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2.5 px-4 rounded-lg transition-colors">
                        <i class="fas fa-edit mr-2"></i>Edit Project
                    </a>
                @endcan
                <a href="{{ route('projects.index') }}" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-lg transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Projects
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Project Overview -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                <!-- Main Project Info -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                        <div class="p-6">
                            <div class="flex justify-between items-start mb-6">
                                <div>
                                    <h2 class="text-xl font-semibold text-gray-900 mb-2">{{ $project->name }}</h2>
                                    <div class="flex items-center space-x-4">
                                        <span class="px-3 py-1 text-sm font-medium rounded-full
                                            @if($project->status === 'active') bg-green-100 text-green-800
                                            @elseif($project->status === 'planning') bg-blue-100 text-blue-800
                                            @elseif($project->status === 'on_hold') bg-yellow-100 text-yellow-800
                                            @elseif($project->status === 'completed') bg-gray-100 text-gray-800
                                            @else bg-red-100 text-red-800
                                            @endif">
                                            {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                                        </span>
                                        @if($project->priority)
                                            <span class="px-3 py-1 text-sm font-medium rounded-full
                                                @if($project->priority === 'urgent') bg-red-100 text-red-800
                                                @elseif($project->priority === 'high') bg-orange-100 text-orange-800
                                                @elseif($project->priority === 'medium') bg-yellow-100 text-yellow-800
                                                @else bg-green-100 text-green-800
                                                @endif">
                                                {{ ucfirst($project->priority) }} Priority
                                            </span>
                                        @endif
                                    </div>
                                </div>
                                @can('update', $project)
                                    <div class="flex space-x-2">
                                        <select id="status-select" class="text-sm border-gray-300 rounded-lg focus:border-blue-500 focus:ring-blue-500" onchange="updateStatus()">
                                            <option value="planning" {{ $project->status === 'planning' ? 'selected' : '' }}>Planning</option>
                                            <option value="active" {{ $project->status === 'active' ? 'selected' : '' }}>Active</option>
                                            <option value="on_hold" {{ $project->status === 'on_hold' ? 'selected' : '' }}>On Hold</option>
                                            <option value="completed" {{ $project->status === 'completed' ? 'selected' : '' }}>Completed</option>
                                            <option value="cancelled" {{ $project->status === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                        </select>
                                    </div>
                                @endcan
                            </div>

                            @if($project->description)
                                <div class="mb-6">
                                    <h3 class="text-sm font-medium text-gray-700 mb-2">Description</h3>
                                    <p class="text-gray-600">{{ $project->description }}</p>
                                </div>
                            @endif

                            <!-- Project Details Grid -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <h3 class="text-sm font-medium text-gray-700 mb-3">Project Information</h3>
                                    <dl class="space-y-2">
                                        <div class="flex justify-between">
                                            <dt class="text-sm text-gray-500">Client:</dt>
                                            <dd class="text-sm font-medium text-gray-900">{{ $project->client_name }}</dd>
                                        </div>
                                        @if($project->start_date)
                                            <div class="flex justify-between">
                                                <dt class="text-sm text-gray-500">Start Date:</dt>
                                                <dd class="text-sm font-medium text-gray-900">{{ $project->start_date->format('M j, Y') }}</dd>
                                            </div>
                                        @endif
                                        @if($project->due_date)
                                            <div class="flex justify-between">
                                                <dt class="text-sm text-gray-500">Due Date:</dt>
                                                <dd class="text-sm font-medium text-gray-900 {{ $project->is_overdue ? 'text-red-600' : '' }}">
                                                    {{ $project->due_date->format('M j, Y') }}
                                                    @if($project->is_overdue)
                                                        <span class="text-red-600 ml-1">(Overdue)</span>
                                                    @endif
                                                </dd>
                                            </div>
                                        @endif
                                        @if($project->completed_date)
                                            <div class="flex justify-between">
                                                <dt class="text-sm text-gray-500">Completed:</dt>
                                                <dd class="text-sm font-medium text-gray-900">{{ $project->completed_date->format('M j, Y') }}</dd>
                                            </div>
                                        @endif
                                    </dl>
                                </div>

                                <div>
                                    <h3 class="text-sm font-medium text-gray-700 mb-3">Budget & Billing</h3>
                                    <dl class="space-y-2">
                                        @if($project->budget)
                                            <div class="flex justify-between">
                                                <dt class="text-sm text-gray-500">Budget:</dt>
                                                <dd class="text-sm font-medium text-gray-900">₹{{ number_format($project->budget, 2) }}</dd>
                                            </div>
                                        @endif
                                        @if($project->hourly_rate)
                                            <div class="flex justify-between">
                                                <dt class="text-sm text-gray-500">Hourly Rate:</dt>
                                                <dd class="text-sm font-medium text-gray-900">₹{{ number_format($project->hourly_rate, 2) }}/hr</dd>
                                            </div>
                                        @endif
                                        <div class="flex justify-between">
                                            <dt class="text-sm text-gray-500">Billing Type:</dt>
                                            <dd class="text-sm font-medium text-gray-900">{{ ucfirst($project->billing_type ?? 'hourly') }}</dd>
                                        </div>
                                        <div class="flex justify-between">
                                            <dt class="text-sm text-gray-500">Billable:</dt>
                                            <dd class="text-sm font-medium text-gray-900">{{ $project->is_billable ? 'Yes' : 'No' }}</dd>
                                        </div>
                                    </dl>
                                </div>
                            </div>

                            <!-- Progress Bar -->
                            @if($project->progress_percentage > 0)
                                <div class="mt-6">
                                    <div class="flex justify-between text-sm text-gray-600 mb-2">
                                        <span>Overall Progress</span>
                                        <span>{{ $project->progress_percentage }}%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-3">
                                        <div class="bg-blue-600 h-3 rounded-full transition-all duration-300" style="width: {{ $project->progress_percentage }}%"></div>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Project Stats -->
                <div class="space-y-6">
                    <!-- Quick Stats -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                        <div class="p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-500">Total Tasks</span>
                                    <span class="text-lg font-semibold text-gray-900">{{ $project->tasks->count() }}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-500">Completed Tasks</span>
                                    <span class="text-lg font-semibold text-green-600">{{ $project->tasks->where('status', 'completed')->count() }}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-500">Team Members</span>
                                    <span class="text-lg font-semibold text-gray-900">{{ $project->projectMembers->count() }}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-500">Time Logged</span>
                                    <span class="text-lg font-semibold text-gray-900">{{ number_format($project->total_hours, 1) }}h</span>
                                </div>
                                @if($project->budget)
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-gray-500">Budget Used</span>
                                        <span class="text-lg font-semibold text-gray-900">₹{{ number_format($project->total_earned, 2) }}</span>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                        <div class="p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                            <div class="space-y-3">
                                <a href="{{ route('projects.dashboard', $project) }}" class="w-full bg-blue-50 hover:bg-blue-100 text-blue-700 font-medium py-2.5 px-4 rounded-lg transition-colors flex items-center">
                                    <i class="fas fa-chart-line mr-3"></i>View Dashboard
                                </a>
                                @can('manageTasks', $project)
                                    <a href="{{ route('projects.manage-tasks', $project) }}" class="w-full bg-green-50 hover:bg-green-100 text-green-700 font-medium py-2.5 px-4 rounded-lg transition-colors flex items-center">
                                        <i class="fas fa-tasks mr-3"></i>Manage Tasks
                                    </a>
                                @endcan
                                @can('trackTime', $project)
                                    <a href="{{ route('projects.track-time', $project) }}" class="w-full bg-purple-50 hover:bg-purple-100 text-purple-700 font-medium py-2.5 px-4 rounded-lg transition-colors flex items-center">
                                        <i class="fas fa-clock mr-3"></i>Track Time
                                    </a>
                                @endcan
                                @can('manageMembers', $project)
                                    <a href="{{ route('projects.manage-team', $project) }}" class="w-full bg-orange-50 hover:bg-orange-100 text-orange-700 font-medium py-2.5 px-4 rounded-lg transition-colors flex items-center">
                                        <i class="fas fa-users mr-3"></i>Manage Team
                                    </a>
                                @endcan
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity / Tasks Preview -->
            @if($project->tasks->count() > 0)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-900">Recent Tasks</h3>
                            <a href="#" class="text-blue-600 hover:text-blue-800 text-sm font-medium">View All Tasks</a>
                        </div>
                        <div class="space-y-3">
                            @foreach($project->tasks->take(5) as $task)
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div class="flex items-center">
                                        <div class="w-4 h-4 rounded border-2 mr-3 {{ $task->status === 'completed' ? 'bg-green-500 border-green-500' : 'border-gray-300' }}"></div>
                                        <span class="text-sm font-medium text-gray-900 {{ $task->status === 'completed' ? 'line-through text-gray-500' : '' }}">
                                            {{ $task->title }}
                                        </span>
                                    </div>
                                    <span class="text-xs text-gray-500">
                                        {{ $task->due_date ? $task->due_date->format('M j') : 'No due date' }}
                                    </span>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>

    @can('update', $project)
        <script>
            function updateStatus() {
                const status = document.getElementById('status-select').value;
                
                fetch(`{{ route('projects.status', $project) }}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ status: status })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error updating status');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error updating status');
                });
            }
        </script>
    @endcan
</x-app-layout>
