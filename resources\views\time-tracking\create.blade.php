<x-app-layout>
    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">Add Time Entry</h2>
                        <a href="{{ route('time-tracking.index') }}" 
                           class="text-gray-600 hover:text-gray-800">
                            <i class="fas fa-arrow-left mr-2"></i>Back to Time Tracking
                        </a>
                    </div>

                    <form method="POST" action="{{ route('time-tracking.store') }}" class="space-y-6">
                        @csrf

                        <!-- Project Selection -->
                        <div>
                            <label for="project_id" class="block text-sm font-medium text-gray-700 mb-2">
                                Project <span class="text-red-500">*</span>
                            </label>
                            <select name="project_id" 
                                    id="project_id"
                                    required
                                    onchange="loadProjectTasks()"
                                    class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">Select a project</option>
                                @foreach($projects as $project)
                                    <option value="{{ $project->id }}" {{ old('project_id') == $project->id ? 'selected' : '' }}>
                                        {{ $project->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('project_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Task Selection -->
                        <div>
                            <label for="task_id" class="block text-sm font-medium text-gray-700 mb-2">
                                Task (Optional)
                            </label>
                            <select name="task_id" 
                                    id="task_id"
                                    class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">Select a task</option>
                                <!-- Tasks will be loaded via JavaScript -->
                            </select>
                            @error('task_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                                Description <span class="text-red-500">*</span>
                            </label>
                            <textarea id="description" 
                                      name="description" 
                                      rows="3"
                                      required
                                      class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                      placeholder="What did you work on?">{{ old('description') }}</textarea>
                            @error('description')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Time Entry Method -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Time Entry Method
                            </label>
                            <div class="flex space-x-6">
                                <label class="flex items-center">
                                    <input type="radio" 
                                           name="entry_method" 
                                           value="duration" 
                                           checked
                                           class="text-blue-600 focus:ring-blue-500"
                                           onchange="toggleTimeMethod()">
                                    <span class="ml-2 text-sm text-gray-700">Duration</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" 
                                           name="entry_method" 
                                           value="start_end"
                                           class="text-blue-600 focus:ring-blue-500"
                                           onchange="toggleTimeMethod()">
                                    <span class="ml-2 text-sm text-gray-700">Start & End Time</span>
                                </label>
                            </div>
                        </div>

                        <!-- Duration Method -->
                        <div id="duration-method" class="time-method">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="start_time" class="block text-sm font-medium text-gray-700 mb-2">
                                        Date & Start Time <span class="text-red-500">*</span>
                                    </label>
                                    <input type="datetime-local" 
                                           id="start_time" 
                                           name="start_time" 
                                           value="{{ old('start_time', now()->format('Y-m-d\TH:i')) }}"
                                           required
                                           class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    @error('start_time')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="duration_minutes" class="block text-sm font-medium text-gray-700 mb-2">
                                        Duration (minutes) <span class="text-red-500">*</span>
                                    </label>
                                    <input type="number" 
                                           id="duration_minutes" 
                                           name="duration_minutes" 
                                           value="{{ old('duration_minutes') }}"
                                           min="1"
                                           required
                                           class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                           placeholder="60">
                                    @error('duration_minutes')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Start & End Time Method -->
                        <div id="start-end-method" class="time-method hidden">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="start_time_alt" class="block text-sm font-medium text-gray-700 mb-2">
                                        Start Time <span class="text-red-500">*</span>
                                    </label>
                                    <input type="datetime-local" 
                                           id="start_time_alt" 
                                           name="start_time_alt" 
                                           value="{{ old('start_time_alt') }}"
                                           class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                </div>

                                <div>
                                    <label for="end_time" class="block text-sm font-medium text-gray-700 mb-2">
                                        End Time <span class="text-red-500">*</span>
                                    </label>
                                    <input type="datetime-local" 
                                           id="end_time" 
                                           name="end_time" 
                                           value="{{ old('end_time') }}"
                                           class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                </div>
                            </div>
                        </div>

                        <!-- Hourly Rate and Billable -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="hourly_rate" class="block text-sm font-medium text-gray-700 mb-2">
                                    Hourly Rate
                                </label>
                                <input type="number" 
                                       id="hourly_rate" 
                                       name="hourly_rate" 
                                       value="{{ old('hourly_rate') }}"
                                       step="0.01"
                                       min="0"
                                       class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                       placeholder="0.00">
                                @error('hourly_rate')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="flex items-end">
                                <label class="flex items-center">
                                    <input type="checkbox" 
                                           name="is_billable" 
                                           value="1"
                                           {{ old('is_billable', true) ? 'checked' : '' }}
                                           class="text-blue-600 focus:ring-blue-500 rounded">
                                    <span class="ml-2 text-sm text-gray-700">This time is billable</span>
                                </label>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                            <a href="{{ route('time-tracking.index') }}" 
                               class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                                Cancel
                            </a>
                            <button type="submit" 
                                    class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                                <i class="fas fa-save mr-2"></i>Add Time Entry
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        function toggleTimeMethod() {
            const method = document.querySelector('input[name="entry_method"]:checked').value;
            const durationMethod = document.getElementById('duration-method');
            const startEndMethod = document.getElementById('start-end-method');
            
            if (method === 'duration') {
                durationMethod.classList.remove('hidden');
                startEndMethod.classList.add('hidden');
                // Enable duration fields
                document.getElementById('start_time').required = true;
                document.getElementById('duration_minutes').required = true;
                // Disable start/end fields
                document.getElementById('start_time_alt').required = false;
                document.getElementById('end_time').required = false;
            } else {
                durationMethod.classList.add('hidden');
                startEndMethod.classList.remove('hidden');
                // Disable duration fields
                document.getElementById('start_time').required = false;
                document.getElementById('duration_minutes').required = false;
                // Enable start/end fields
                document.getElementById('start_time_alt').required = true;
                document.getElementById('end_time').required = true;
            }
        }

        function loadProjectTasks() {
            const projectId = document.getElementById('project_id').value;
            const taskSelect = document.getElementById('task_id');
            
            // Clear existing options
            taskSelect.innerHTML = '<option value="">Select a task</option>';
            
            if (!projectId) return;
            
            // In a real implementation, you would fetch tasks via AJAX
            // For now, we'll just show the placeholder
            console.log('Loading tasks for project:', projectId);
        }

        // Handle form submission based on method
        document.querySelector('form').addEventListener('submit', function(e) {
            const method = document.querySelector('input[name="entry_method"]:checked').value;
            
            if (method === 'start_end') {
                // Copy values from alternative fields
                const startTimeAlt = document.getElementById('start_time_alt').value;
                const endTime = document.getElementById('end_time').value;
                
                document.getElementById('start_time').value = startTimeAlt;
                document.getElementById('start_time').name = 'start_time';
                
                // Clear duration
                document.getElementById('duration_minutes').value = '';
                document.getElementById('duration_minutes').removeAttribute('required');
            }
        });
    </script>
    @endpush
</x-app-layout>
