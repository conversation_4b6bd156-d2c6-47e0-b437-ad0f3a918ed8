<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    Site Settings
                </h2>
                <p class="text-gray-600 text-sm mt-1">Manage your website configuration and settings</p>
            </div>
            <div class="flex space-x-3">
                <button onclick="exportSettings()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm">
                    <i class="fas fa-download mr-2"></i>Export
                </button>
                <button onclick="document.getElementById('import-modal').classList.remove('hidden')" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm">
                    <i class="fas fa-upload mr-2"></i>Import
                </button>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Category Tabs -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                        @foreach($categories as $key => $label)
                            <a href="{{ route('admin.settings.index', ['category' => $key]) }}"
                               class="py-4 px-1 border-b-2 font-medium text-sm {{ $category === $key ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                                {{ $label }}
                                @if($allSettings->has($key))
                                    <span class="ml-2 bg-gray-100 text-gray-600 py-0.5 px-2 rounded-full text-xs">
                                        {{ $allSettings->get($key)->count() }}
                                    </span>
                                @endif
                            </a>
                        @endforeach
                    </nav>
                </div>
            </div>

            <!-- Settings Form -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    @if(session('success'))
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if($errors->any())
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                            <ul class="list-disc list-inside">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    @if($settings->isEmpty())
                        <div class="text-center py-12">
                            <i class="fas fa-cogs text-gray-400 text-6xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No Settings Found</h3>
                            <p class="text-gray-600 mb-6">No settings have been configured for this category yet.</p>
                            <button onclick="seedDefaultSettings('{{ $category }}')" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                                <i class="fas fa-plus mr-2"></i>Create Default Settings
                            </button>
                        </div>
                    @else
                        <form action="{{ route('admin.settings.update') }}" method="POST" class="space-y-6">
                            @csrf
                            <input type="hidden" name="category" value="{{ $category }}">

                            @foreach($settings as $setting)
                                <div class="border-b border-gray-200 pb-6 last:border-b-0">
                                    <div class="flex justify-between items-start mb-3">
                                        <div>
                                            <label for="setting_{{ $setting->key }}" class="block text-sm font-medium text-gray-700">
                                                {{ $setting->label }}
                                                @if($setting->is_required)
                                                    <span class="text-red-500">*</span>
                                                @endif
                                            </label>
                                            @if($setting->description)
                                                <p class="text-sm text-gray-500 mt-1">{{ $setting->description }}</p>
                                            @endif
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            @if($setting->is_public)
                                                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Public</span>
                                            @endif
                                            <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">{{ ucfirst($setting->type) }}</span>
                                        </div>
                                    </div>

                                    <div class="mt-2">
                                        @if($setting->type === 'boolean')
                                            <div class="flex items-center">
                                                <input type="hidden" name="settings[{{ $setting->key }}]" value="0">
                                                <input type="checkbox" 
                                                       id="setting_{{ $setting->key }}" 
                                                       name="settings[{{ $setting->key }}]" 
                                                       value="1"
                                                       {{ $setting->getTypedValue() ? 'checked' : '' }}
                                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                                <label for="setting_{{ $setting->key }}" class="ml-2 block text-sm text-gray-900">
                                                    Enable this setting
                                                </label>
                                            </div>
                                        @elseif($setting->options && is_array($setting->options))
                                            <select id="setting_{{ $setting->key }}"
                                                    name="settings[{{ $setting->key }}]"
                                                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                                @foreach($setting->options as $value => $label)
                                                    <option value="{{ $value }}" {{ $setting->getTypedValue() == $value ? 'selected' : '' }}>
                                                        {{ $label }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        @elseif($setting->type === 'text')
                                            <textarea id="setting_{{ $setting->key }}" 
                                                      name="settings[{{ $setting->key }}]" 
                                                      rows="3"
                                                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                                      {{ $setting->is_required ? 'required' : '' }}>{{ $setting->getTypedValue() }}</textarea>
                                        @else
                                            <input type="{{ $setting->type === 'integer' ? 'number' : ($setting->key === 'mail.from_address' ? 'email' : 'text') }}" 
                                                   id="setting_{{ $setting->key }}" 
                                                   name="settings[{{ $setting->key }}]" 
                                                   value="{{ $setting->getTypedValue() }}"
                                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                                   {{ $setting->is_required ? 'required' : '' }}>
                                        @endif
                                    </div>
                                </div>
                            @endforeach

                            <div class="flex justify-between items-center pt-6">
                                <div class="flex space-x-3">
                                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg">
                                        <i class="fas fa-save mr-2"></i>Save Settings
                                    </button>
                                    <button type="button" onclick="resetSettings('{{ $category }}')" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg">
                                        <i class="fas fa-undo mr-2"></i>Reset to Defaults
                                    </button>
                                </div>
                                @if($category === 'email')
                                    <button type="button" onclick="testEmail()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                                        <i class="fas fa-envelope mr-2"></i>Test Email
                                    </button>
                                @endif
                            </div>
                        </form>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Import Modal -->
    <div id="import-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Import Settings</h3>
                <form action="{{ route('admin.settings.import') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Settings File (JSON)</label>
                        <input type="file" name="settings_file" accept=".json" required
                               class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="document.getElementById('import-modal').classList.add('hidden')" 
                                class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-lg">
                            Cancel
                        </button>
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                            Import
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Test Email Modal -->
    <div id="test-email-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Test Email Configuration</h3>
                <form id="test-email-form">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Test Email Address</label>
                        <input type="email" id="test_email" required
                               class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Enter email address to test">
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="document.getElementById('test-email-modal').classList.add('hidden')" 
                                class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-lg">
                            Cancel
                        </button>
                        <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                            Send Test Email
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function exportSettings() {
            const category = '{{ $category }}';
            window.location.href = `{{ route('admin.settings.export') }}?category=${category}`;
        }

        function resetSettings(category) {
            if (confirm('Are you sure you want to reset all settings in this category to their default values? This action cannot be undone.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '{{ route('admin.settings.reset') }}';
                
                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = '{{ csrf_token() }}';
                
                const categoryInput = document.createElement('input');
                categoryInput.type = 'hidden';
                categoryInput.name = 'category';
                categoryInput.value = category;
                
                form.appendChild(csrfToken);
                form.appendChild(categoryInput);
                document.body.appendChild(form);
                form.submit();
            }
        }

        function testEmail() {
            document.getElementById('test-email-modal').classList.remove('hidden');
        }

        document.getElementById('test-email-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('test_email').value;
            const button = e.target.querySelector('button[type="submit"]');
            const originalText = button.innerHTML;
            
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';
            button.disabled = true;
            
            fetch('{{ route('admin.settings.test-email') }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({ test_email: email })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Test email sent successfully!');
                    document.getElementById('test-email-modal').classList.add('hidden');
                } else {
                    alert('Failed to send test email: ' + data.message);
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            })
            .finally(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            });
        });
    </script>
</x-app-layout>
