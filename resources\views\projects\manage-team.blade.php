<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Manage Team</h1>
                <p class="text-gray-600 mt-1">{{ $project->name }} - Team Management</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('projects.show', $project) }}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Project
                </a>
                @if($availableUsers->count() > 0)
                    <button onclick="openAddMemberModal()"
                            class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-200">
                        <i class="fas fa-user-plus mr-2"></i>Add Member
                    </button>
                @endif
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Project Info -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">{{ $project->name }}</h3>
                            @if($project->client)
                                <p class="text-gray-600">Client: {{ $project->client->name }}</p>
                            @endif
                        </div>
                        <div class="flex items-center space-x-4">
                            <span class="text-sm text-gray-600">{{ $projectMembers->where('left_at', null)->count() + 1 }} team members</span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                @if($project->status === 'active') bg-green-100 text-green-800
                                @elseif($project->status === 'completed') bg-blue-100 text-blue-800
                                @elseif($project->status === 'on_hold') bg-yellow-100 text-yellow-800
                                @else bg-gray-100 text-gray-800 @endif">
                                {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Team Members -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Team Members</h3>

                    <!-- Project Owner -->
                    <div class="border-b border-gray-200 pb-6 mb-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-crown text-blue-600"></i>
                                </div>
                                <div class="ml-4">
                                    <h4 class="text-sm font-medium text-gray-900">{{ $project->user->name }}</h4>
                                    <p class="text-sm text-gray-500">{{ $project->user->email }}</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    Project Owner
                                </span>
                                <div class="text-sm text-gray-500">
                                    All Permissions
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Project Members -->
                    @if($projectMembers->count() > 0)
                        <div class="space-y-4">
                            @foreach($projectMembers as $member)
                                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-user text-gray-600"></i>
                                        </div>
                                        <div class="ml-4">
                                            <h4 class="text-sm font-medium text-gray-900">{{ $member->user->name }}</h4>
                                            <p class="text-sm text-gray-500">{{ $member->user->email }}</p>
                                            @if($member->left_at)
                                                <p class="text-xs text-red-500">Left on {{ $member->left_at->format('M d, Y') }}</p>
                                            @else
                                                <p class="text-xs text-green-600">Joined {{ $member->joined_at->format('M d, Y') }}</p>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-4">
                                        <div class="text-right">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                @if($member->role === 'manager') bg-purple-100 text-purple-800
                                                @else bg-gray-100 text-gray-800 @endif">
                                                {{ ucfirst($member->role) }}
                                            </span>
                                            @if($member->hourly_rate)
                                                <p class="text-xs text-gray-500 mt-1">${{ number_format($member->hourly_rate, 2) }}/hr</p>
                                            @endif
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            <div class="flex flex-col space-y-1">
                                                @if($member->can_track_time)
                                                    <span class="text-green-600"><i class="fas fa-check text-xs"></i> Track Time</span>
                                                @endif
                                                @if($member->can_manage_tasks)
                                                    <span class="text-green-600"><i class="fas fa-check text-xs"></i> Manage Tasks</span>
                                                @endif
                                                @if($member->can_view_reports)
                                                    <span class="text-green-600"><i class="fas fa-check text-xs"></i> View Reports</span>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="flex space-x-2">
                                            @if(!$member->left_at)
                                                <button onclick="editMember({{ $member->id }})" 
                                                        class="text-indigo-600 hover:text-indigo-900 text-sm">
                                                    Edit
                                                </button>
                                                <button onclick="removeMember({{ $member->id }})" 
                                                        class="text-red-600 hover:text-red-900 text-sm">
                                                    Remove
                                                </button>
                                            @else
                                                <span class="text-gray-400 text-sm">Inactive</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <i class="fas fa-users text-gray-400 text-3xl mb-4"></i>
                            <h4 class="text-lg font-medium text-gray-900 mb-2">No team members yet</h4>
                            <p class="text-gray-600 mb-4">Add team members to collaborate on this project.</p>
                            @if($availableUsers->count() > 0)
                                <button onclick="openAddMemberModal()" 
                                        class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-200">
                                    <i class="fas fa-user-plus mr-2"></i>Add First Member
                                </button>
                            @else
                                <p class="text-sm text-gray-500">No users available to add to this project.</p>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Add Member Modal -->
    @if($availableUsers->count() > 0)
        <div id="addMemberModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Add Team Member</h3>
                    <form id="addMemberForm">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">User</label>
                            <select id="memberUser" required class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">Select a user</option>
                                @foreach($availableUsers as $user)
                                    <option value="{{ $user->id }}">{{ $user->name }} ({{ $user->email }})</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Role</label>
                            <select id="memberRole" required class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="member">Member</option>
                                <option value="manager">Manager</option>
                            </select>
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Hourly Rate (Optional)</label>
                            <input type="number" id="memberRate" step="0.01" min="0" class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="0.00">
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Permissions</label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" id="canTrackTime" checked class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    <span class="ml-2 text-sm text-gray-700">Can track time</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" id="canManageTasks" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    <span class="ml-2 text-sm text-gray-700">Can manage tasks</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" id="canViewReports" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    <span class="ml-2 text-sm text-gray-700">Can view reports</span>
                                </label>
                            </div>
                        </div>
                        <div class="flex justify-end space-x-3">
                            <button type="button" onclick="closeAddMemberModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded">
                                Cancel
                            </button>
                            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded">
                                Add Member
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endif

    <!-- Edit Member Modal -->
    <div id="editMemberModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Edit Team Member</h3>
                <form id="editMemberForm">
                    <input type="hidden" id="editMemberId">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Role</label>
                        <select id="editMemberRole" required class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="member">Member</option>
                            <option value="manager">Manager</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Hourly Rate (Optional)</label>
                        <input type="number" id="editMemberRate" step="0.01" min="0" class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="0.00">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Permissions</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" id="editCanTrackTime" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700">Can track time</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" id="editCanManageTasks" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700">Can manage tasks</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" id="editCanViewReports" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700">Can view reports</span>
                            </label>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeEditMemberModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded">
                            Cancel
                        </button>
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded">
                            Update Member
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        // Make functions globally accessible
        window.openAddMemberModal = function() {
            document.getElementById('addMemberModal').classList.remove('hidden');
        };

        window.closeAddMemberModal = function() {
            document.getElementById('addMemberModal').classList.add('hidden');
            const form = document.getElementById('addMemberForm');
            if (form) form.reset();
        };

        // Wait for DOM to be ready
        document.addEventListener('DOMContentLoaded', function() {
            const addMemberForm = document.getElementById('addMemberForm');
            if (addMemberForm) {
                addMemberForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = {
                        user_id: document.getElementById('memberUser').value,
                        role: document.getElementById('memberRole').value,
                        hourly_rate: document.getElementById('memberRate').value || null,
                        can_track_time: document.getElementById('canTrackTime').checked,
                        can_manage_tasks: document.getElementById('canManageTasks').checked,
                        can_view_reports: document.getElementById('canViewReports').checked
                    };

                    fetch('/projects/{{ $project->id }}/members', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify(formData)
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            location.reload();
                        } else {
                            alert(data.error || 'Error adding member');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Error adding member');
                    });
                });
            }
        });

        // Edit member functionality - make globally accessible
        window.editMember = function(memberId) {
            // Get member data and populate form
            const memberData = @json($projectMembers->keyBy('id'));
            const member = memberData[memberId];

            document.getElementById('editMemberId').value = memberId;
            document.getElementById('editMemberRole').value = member.role;
            document.getElementById('editMemberRate').value = member.hourly_rate || '';
            document.getElementById('editCanTrackTime').checked = member.can_track_time;
            document.getElementById('editCanManageTasks').checked = member.can_manage_tasks;
            document.getElementById('editCanViewReports').checked = member.can_view_reports;

            document.getElementById('editMemberModal').classList.remove('hidden');
        };

        window.closeEditMemberModal = function() {
            document.getElementById('editMemberModal').classList.add('hidden');
        };

        // Wait for DOM to be ready for edit form
        document.addEventListener('DOMContentLoaded', function() {
            const editMemberForm = document.getElementById('editMemberForm');
            if (editMemberForm) {
                editMemberForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const memberId = document.getElementById('editMemberId').value;
                    const formData = {
                        role: document.getElementById('editMemberRole').value,
                        hourly_rate: document.getElementById('editMemberRate').value || null,
                        can_track_time: document.getElementById('editCanTrackTime').checked,
                        can_manage_tasks: document.getElementById('editCanManageTasks').checked,
                        can_view_reports: document.getElementById('editCanViewReports').checked
                    };

                    fetch(`/projects/{{ $project->id }}/members/${memberId}`, {
                        method: 'PATCH',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify(formData)
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            location.reload();
                        } else {
                            alert(data.error || 'Error updating member');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Error updating member');
                    });
                });
            }
        });

        // Remove member functionality - make globally accessible
        window.removeMember = function(memberId) {
            if (confirm('Are you sure you want to remove this team member from the project?')) {
                fetch(`/projects/{{ $project->id }}/members/${memberId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert(data.error || 'Error removing member');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error removing member');
                });
            }
        };

        // Auto-set permissions based on role - wait for DOM
        document.addEventListener('DOMContentLoaded', function() {
            const memberRole = document.getElementById('memberRole');
            const editMemberRole = document.getElementById('editMemberRole');

            if (memberRole) {
                memberRole.addEventListener('change', function() {
                    const role = this.value;
                    if (role === 'manager') {
                        document.getElementById('canTrackTime').checked = true;
                        document.getElementById('canManageTasks').checked = true;
                        document.getElementById('canViewReports').checked = true;
                    }
                });
            }

            if (editMemberRole) {
                editMemberRole.addEventListener('change', function() {
                    const role = this.value;
                    if (role === 'manager') {
                        document.getElementById('editCanTrackTime').checked = true;
                        document.getElementById('editCanManageTasks').checked = true;
                        document.getElementById('editCanViewReports').checked = true;
                    }
                });
            }
        });
    </script>
    @endpush
</x-app-layout>
