<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_id')->constrained()->onDelete('cascade');
            $table->string('payment_id')->unique(); // Our internal payment ID
            $table->string('gateway_payment_id')->nullable(); // Gateway's payment ID
            $table->string('gateway'); // paypal, razorpay
            $table->decimal('amount', 12, 2);
            $table->string('currency', 3)->default('INR');
            $table->enum('status', ['pending', 'completed', 'failed', 'cancelled', 'refunded'])->default('pending');
            $table->json('gateway_response')->nullable(); // Store full gateway response
            $table->timestamp('paid_at')->nullable();
            $table->text('failure_reason')->nullable();
            $table->string('client_email')->nullable(); // Store client email for reference
            $table->string('client_name')->nullable(); // Store client name for reference
            $table->timestamps();

            // Indexes for performance
            $table->index(['invoice_id', 'status']);
            $table->index('gateway_payment_id');
            $table->index('status');
            $table->index('paid_at');
            $table->index(['status', 'created_at']);
            $table->index('amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_payments');
    }
};
