@extends('emails.layouts.app')

@section('content')
    <!-- Security Header -->
    <div style="text-align: center; margin-bottom: 32px;">
        @if($eventType === 'suspicious_activity' || $eventType === 'account_locked')
            <div style="font-size: 64px; margin-bottom: 16px;">🚨</div>
            <h2 style="color: #dc2626; margin-bottom: 8px;">{{ $header_title }}</h2>
            <p class="lead" style="color: #ef4444;">{{ $header_subtitle }}</p>
        @elseif($eventType === 'login_from_new_device')
            <div style="font-size: 64px; margin-bottom: 16px;">📱</div>
            <h2 style="color: #f59e0b; margin-bottom: 8px;">{{ $header_title }}</h2>
            <p class="lead" style="color: #d97706;">{{ $header_subtitle }}</p>
        @else
            <div style="font-size: 64px; margin-bottom: 16px;">🔒</div>
            <h2 style="color: #10b981; margin-bottom: 8px;">{{ $header_title }}</h2>
            <p class="lead" style="color: #059669;">{{ $header_subtitle }}</p>
        @endif
    </div>
    
    <!-- Personal Greeting -->
    <p style="font-size: 18px; color: #374151; margin-bottom: 24px;">
        Hi {{ $user->name }},
    </p>

    <!-- Event-Specific Content -->
    @if($eventType === 'password_changed')
        <p>Your password was successfully changed on {{ now()->format('M d, Y \a\t g:i A') }}.</p>
        
        <div class="success-box">
            <h3 style="color: #166534; margin: 0 0 12px 0;">✅ Password Updated Successfully</h3>
            <p style="color: #15803d; margin: 0;">
                Your account is now secured with your new password. You may need to sign in again on your other devices.
            </p>
        </div>

    @elseif($eventType === 'email_changed')
        <p>Your email address was successfully changed from <strong>{{ $eventData['old_email'] ?? 'previous email' }}</strong> to <strong>{{ $user->email }}</strong>.</p>
        
        <div class="success-box">
            <h3 style="color: #166534; margin: 0 0 12px 0;">📧 Email Updated Successfully</h3>
            <p style="color: #15803d; margin: 0;">
                All future notifications will be sent to your new email address. Please verify your new email if you haven't already.
            </p>
        </div>

    @elseif($eventType === 'login_from_new_device')
        <p>We detected a login to your account from a new device or location:</p>
        
        <div class="warning-box">
            <h3 style="color: #92400e; margin: 0 0 16px 0;">📱 Login Details</h3>
            <table style="width: 100%; border-collapse: collapse;">
                <tr style="border-bottom: 1px solid #fde68a;">
                    <td style="padding: 8px 0; color: #b45309; font-weight: 600;">Date & Time:</td>
                    <td style="padding: 8px 0; color: #92400e; font-weight: 700; text-align: right;">{{ $eventData['login_time'] ?? now()->format('M d, Y \a\t g:i A') }}</td>
                </tr>
                <tr style="border-bottom: 1px solid #fde68a;">
                    <td style="padding: 8px 0; color: #b45309; font-weight: 600;">IP Address:</td>
                    <td style="padding: 8px 0; color: #92400e; font-weight: 700; text-align: right;">{{ $eventData['ip_address'] ?? request()->ip() }}</td>
                </tr>
                <tr style="border-bottom: 1px solid #fde68a;">
                    <td style="padding: 8px 0; color: #b45309; font-weight: 600;">Device:</td>
                    <td style="padding: 8px 0; color: #92400e; font-weight: 700; text-align: right;">{{ $eventData['device'] ?? 'Unknown Device' }}</td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; color: #b45309; font-weight: 600;">Location:</td>
                    <td style="padding: 8px 0; color: #92400e; font-weight: 700; text-align: right;">{{ $eventData['location'] ?? 'Unknown Location' }}</td>
                </tr>
            </table>
        </div>

    @elseif($eventType === 'suspicious_activity')
        <p>We detected unusual activity on your account that may indicate unauthorized access:</p>
        
        <div style="background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%); border: 1px solid #f87171; border-radius: 12px; padding: 24px; margin: 24px 0;">
            <h3 style="color: #dc2626; margin: 0 0 16px 0;">🚨 Suspicious Activity Detected</h3>
            <ul style="color: #dc2626; margin: 0; padding-left: 20px;">
                @foreach($eventData['activities'] ?? ['Multiple failed login attempts'] as $activity)
                    <li style="margin-bottom: 8px;">{{ $activity }}</li>
                @endforeach
            </ul>
        </div>

    @elseif($eventType === 'account_locked')
        <p>Your account has been temporarily locked due to suspicious activity or multiple failed login attempts.</p>
        
        <div style="background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%); border: 1px solid #f87171; border-radius: 12px; padding: 24px; margin: 24px 0;">
            <h3 style="color: #dc2626; margin: 0 0 12px 0;">🔒 Account Locked</h3>
            <p style="color: #dc2626; margin: 0 0 16px 0;">
                Your account will be automatically unlocked in {{ $eventData['unlock_time'] ?? '30 minutes' }}, or you can contact support for immediate assistance.
            </p>
        </div>

    @elseif($eventType === 'two_factor_enabled')
        <p>Two-factor authentication has been successfully enabled on your account.</p>
        
        <div class="success-box">
            <h3 style="color: #166534; margin: 0 0 12px 0;">🔐 Enhanced Security Active</h3>
            <p style="color: #15803d; margin: 0;">
                Your account is now protected with an additional layer of security. You'll need your authentication app or SMS code when signing in.
            </p>
        </div>

    @elseif($eventType === 'two_factor_disabled')
        <p>Two-factor authentication has been disabled on your account.</p>
        
        <div class="warning-box">
            <h3 style="color: #92400e; margin: 0 0 12px 0;">⚠️ Security Level Reduced</h3>
            <p style="color: #b45309; margin: 0;">
                Your account is now less secure without two-factor authentication. We recommend re-enabling it for better protection.
            </p>
        </div>
    @endif

    <!-- Action Required Section -->
    @if(in_array($eventType, ['suspicious_activity', 'account_locked', 'login_from_new_device']))
        <h3>🎯 What You Should Do</h3>
        
        @if($eventType === 'login_from_new_device')
            <div class="info-box">
                <h4 style="color: #1e40af; margin: 0 0 12px 0;">If this was you:</h4>
                <p style="color: #2563eb; margin: 0 0 16px 0;">No action needed! You can safely ignore this notification.</p>
                
                <h4 style="color: #dc2626; margin: 16px 0 12px 0;">If this wasn't you:</h4>
                <ul style="color: #dc2626; margin: 0; padding-left: 20px;">
                    <li style="margin-bottom: 8px;">Change your password immediately</li>
                    <li style="margin-bottom: 8px;">Review your account activity</li>
                    <li style="margin-bottom: 8px;">Enable two-factor authentication</li>
                    <li>Contact our security team</li>
                </ul>
            </div>
        @else
            <div style="background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%); border: 1px solid #f87171; border-radius: 12px; padding: 24px; margin: 24px 0;">
                <h4 style="color: #dc2626; margin: 0 0 12px 0;">Immediate Actions Required:</h4>
                <ol style="color: #dc2626; margin: 0; padding-left: 20px;">
                    <li style="margin-bottom: 8px;"><strong>Change your password</strong> - Create a new, strong password</li>
                    <li style="margin-bottom: 8px;"><strong>Review account activity</strong> - Check for unauthorized changes</li>
                    <li style="margin-bottom: 8px;"><strong>Enable 2FA</strong> - Add extra security to your account</li>
                    <li><strong>Contact support</strong> - Report this incident to our security team</li>
                </ol>
            </div>
        @endif

        <!-- Quick Actions -->
        <div style="text-align: center; margin: 32px 0;">
            @if($eventType !== 'account_locked')
                <a href="{{ route('profile.edit') }}" class="btn btn-primary" style="background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); margin: 4px;">
                    🔑 Change Password
                </a>
            @endif
            <a href="mailto:security@{{ config('app.domain', 'freeligo.com') }}" class="btn btn-outline" style="margin: 4px;">
                🚨 Report Issue
            </a>
        </div>
    @endif

    <!-- Security Tips -->
    <h3>🛡️ Keep Your Account Secure</h3>
    <p>Here are some ways to enhance your account security:</p>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 16px; margin: 24px 0;">
        <div style="background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); padding: 20px; border-radius: 12px; border: 1px solid #bbf7d0;">
            <div style="color: #166534; font-size: 24px; margin-bottom: 8px;">🔐</div>
            <h4 style="margin: 0 0 8px 0; color: #166534; font-size: 16px;">Strong Passwords</h4>
            <p style="margin: 0; color: #15803d; font-size: 14px;">Use unique, complex passwords for all accounts</p>
        </div>
        
        <div style="background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%); padding: 20px; border-radius: 12px; border: 1px solid #bae6fd;">
            <div style="color: #1e40af; font-size: 24px; margin-bottom: 8px;">📱</div>
            <h4 style="margin: 0 0 8px 0; color: #1e40af; font-size: 16px;">Two-Factor Auth</h4>
            <p style="margin: 0; color: #2563eb; font-size: 14px;">Enable 2FA for an extra layer of protection</p>
        </div>
        
        <div style="background: linear-gradient(135deg, #fdf4ff 0%, #fae8ff 100%); padding: 20px; border-radius: 12px; border: 1px solid #f5d0fe;">
            <div style="color: #a21caf; font-size: 24px; margin-bottom: 8px;">👀</div>
            <h4 style="margin: 0 0 8px 0; color: #a21caf; font-size: 16px;">Monitor Activity</h4>
            <p style="margin: 0; color: #c026d3; font-size: 14px;">Regularly check your account for suspicious activity</p>
        </div>
        
        <div style="background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%); padding: 20px; border-radius: 12px; border: 1px solid #fde68a;">
            <div style="color: #92400e; font-size: 24px; margin-bottom: 8px;">📧</div>
            <h4 style="margin: 0 0 8px 0; color: #92400e; font-size: 16px;">Secure Email</h4>
            <p style="margin: 0; color: #b45309; font-size: 14px;">Keep your email account secure with a strong password</p>
        </div>
    </div>

    <!-- Contact Support -->
    <div style="text-align: center; background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; padding: 32px; margin: 32px 0;">
        <div style="font-size: 48px; margin-bottom: 16px;">🛡️</div>
        <h3 style="color: #1e293b; margin: 0 0 12px 0;">Security Concerns?</h3>
        <p style="color: #64748b; margin: 0 0 24px 0;">
            If you have any security concerns or didn't authorize this activity, please contact our security team immediately.
        </p>
        
        <div style="display: flex; flex-wrap: wrap; gap: 12px; justify-content: center;">
            <a href="mailto:security@{{ config('app.domain', 'freeligo.com') }}" class="btn btn-primary" style="background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);">
                🚨 Security Team
            </a>
            <a href="mailto:support@{{ config('app.domain', 'freeligo.com') }}" class="btn btn-outline">
                📧 General Support
            </a>
        </div>
        
        <p style="color: #64748b; font-size: 14px; margin: 20px 0 0 0;">
            <strong>Security Team:</strong> Available 24/7 for urgent security matters
        </p>
    </div>

    <!-- Event Details -->
    <div class="highlight-box">
        <h3>📋 Event Details</h3>
        <table style="width: 100%; border-collapse: collapse;">
            <tr style="border-bottom: 1px solid #e2e8f0;">
                <td style="padding: 8px 0; color: #64748b; width: 30%;">Event Type:</td>
                <td style="padding: 8px 0; color: #1e293b; font-weight: 600;">{{ ucwords(str_replace('_', ' ', $eventType)) }}</td>
            </tr>
            <tr style="border-bottom: 1px solid #e2e8f0;">
                <td style="padding: 8px 0; color: #64748b;">Date & Time:</td>
                <td style="padding: 8px 0; color: #1e293b; font-weight: 600;">{{ now()->format('M d, Y \a\t g:i A T') }}</td>
            </tr>
            <tr style="border-bottom: 1px solid #e2e8f0;">
                <td style="padding: 8px 0; color: #64748b;">Account:</td>
                <td style="padding: 8px 0; color: #1e293b; font-weight: 600;">{{ $user->email }}</td>
            </tr>
            <tr>
                <td style="padding: 8px 0; color: #64748b;">IP Address:</td>
                <td style="padding: 8px 0; color: #1e293b; font-weight: 600;">{{ $eventData['ip_address'] ?? request()->ip() ?? 'Unknown' }}</td>
            </tr>
        </table>
    </div>

    <!-- Footer Message -->
    <div style="text-align: center; margin: 40px 0;">
        <h3 style="color: #1e293b; margin-bottom: 16px;">Your Security is Our Priority 🛡️</h3>
        <p style="color: #64748b; font-size: 16px;">
            We continuously monitor your account for suspicious activity to keep your data safe and secure.
        </p>
    </div>

    <!-- Footer Note -->
    <div style="border-top: 1px solid #e2e8f0; padding-top: 20px; margin-top: 32px;">
        <p style="color: #64748b; font-size: 14px; text-align: center; margin: 0;">
            This security notification was sent to {{ $user->email }} regarding your {{ config('app.name') }} account.
            <br>
            For security questions, contact our security team at 
            <a href="mailto:security@{{ config('app.domain', 'freeligo.com') }}" style="color: #dc2626; text-decoration: none;">security@{{ config('app.domain', 'freeligo.com') }}</a>.
        </p>
    </div>
@endsection
