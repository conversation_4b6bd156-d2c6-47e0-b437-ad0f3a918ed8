@props([
    'variant' => 'primary',
    'size' => 'md',
    'icon' => null,
    'iconPosition' => 'left',
    'dot' => false
])

@php
    $variants = [
        'primary' => 'badge-primary',
        'secondary' => 'badge-secondary',
        'success' => 'badge-success',
        'warning' => 'badge-warning',
        'danger' => 'badge-danger',
        'accent' => 'bg-accent-100 text-accent-800',
        'neutral' => 'bg-neutral-100 text-neutral-800',
    ];

    $sizes = [
        'sm' => 'px-2 py-0.5 text-xs',
        'md' => 'px-2.5 py-0.5 text-xs',
        'lg' => 'px-3 py-1 text-sm'
    ];

    $classes = 'badge ' . $variants[$variant] . ' ' . $sizes[$size];
@endphp

<span {{ $attributes->merge(['class' => $classes]) }}>
    @if($dot)
        <span class="w-1.5 h-1.5 rounded-full bg-current mr-1.5"></span>
    @endif

    @if($icon && $iconPosition === 'left')
        <i class="{{ $icon }} {{ $slot->isNotEmpty() ? 'mr-1' : '' }}"></i>
    @endif

    {{ $slot }}

    @if($icon && $iconPosition === 'right')
        <i class="{{ $icon }} {{ $slot->isNotEmpty() ? 'ml-1' : '' }}"></i>
    @endif
</span>
