<?php

namespace App\Http\Controllers;

use App\Models\Lead;
use App\Models\LeadSource;
use App\Models\LeadStage;
use App\Services\LeadService;
use App\Services\PlanChecker;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class LeadController extends Controller
{
    protected LeadService $leadService;

    public function __construct(LeadService $leadService)
    {
        $this->leadService = $leadService;
    }

    /**
     * Display a listing of leads.
     */
    public function index(Request $request): View
    {
        $this->authorize('viewAny', Lead::class);

        $leads = $this->leadService->getLeadsForUser(Auth::id(), $request);
        $sources = LeadSource::forUser(Auth::id())->active()->limit(100)->get();
        $stages = LeadStage::forUser(Auth::id())->active()->ordered()->limit(50)->get();

        // Get lead usage for plan limitations
        $leadUsage = PlanChecker::getMonthlyLeadUsage(Auth::user());
        $canCreateLead = $this->leadService->canCreateLead(Auth::id());

        return view('leads.index', compact(
            'leads', 
            'sources', 
            'stages', 
            'leadUsage', 
            'canCreateLead'
        ));
    }

    /**
     * Display the pipeline view.
     */
    public function pipeline(): View
    {
        $this->authorize('viewAny', Lead::class);

        $pipelineData = $this->leadService->getPipelineData(Auth::id());
        $sources = LeadSource::forUser(Auth::id())->active()->get();

        return view('leads.pipeline', compact('pipelineData', 'sources'));
    }

    /**
     * Show the form for creating a new lead.
     */
    public function create(): View
    {
        $this->authorize('create', Lead::class);

        if (!$this->leadService->canCreateLead(Auth::id())) {
            return redirect()->route('leads.index')
                ->with('error', 'You have reached your monthly lead limit. Please upgrade your plan.');
        }

        $sources = LeadSource::forUser(Auth::id())->active()->limit(100)->get();
        $stages = LeadStage::forUser(Auth::id())->active()->ordered()->limit(50)->get();

        return view('leads.create', compact('sources', 'stages'));
    }

    /**
     * Store a newly created lead.
     */
    public function store(Request $request): RedirectResponse
    {
        $this->authorize('create', Lead::class);

        if (!$this->leadService->canCreateLead(Auth::id())) {
            return redirect()->route('leads.index')
                ->with('error', 'You have reached your monthly lead limit. Please upgrade your plan.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'company_name' => 'nullable|string|max:255',
            'title' => 'nullable|string|max:255',
            'address' => 'nullable|string',
            'lead_source_id' => 'required|exists:lead_sources,id',
            'lead_stage_id' => 'required|exists:lead_stages,id',
            'estimated_value' => 'nullable|numeric|min:0',
            'priority' => 'required|in:low,medium,high,urgent',
            'notes' => 'nullable|string',
            'next_follow_up_at' => 'nullable|date|after:now',
        ]);

        $lead = $this->leadService->createLead($validated, Auth::id());

        return redirect()->route('leads.show', $lead)
            ->with('success', 'Lead created successfully.');
    }

    /**
     * Display the specified lead.
     */
    public function show(Lead $lead): View
    {
        $this->authorize('view', $lead);

        $lead->load([
            'leadSource', 
            'leadStage', 
            'activities.user', 
            'notes.user',
            'convertedClient'
        ]);

        $stages = LeadStage::forUser(Auth::id())->active()->ordered()->limit(50)->get();

        return view('leads.show', compact('lead', 'stages'));
    }

    /**
     * Show the form for editing the specified lead.
     */
    public function edit(Lead $lead): View
    {
        $this->authorize('update', $lead);

        $sources = LeadSource::forUser(Auth::id())->active()->limit(100)->get();
        $stages = LeadStage::forUser(Auth::id())->active()->ordered()->limit(50)->get();

        return view('leads.edit', compact('lead', 'sources', 'stages'));
    }

    /**
     * Update the specified lead.
     */
    public function update(Request $request, Lead $lead): RedirectResponse
    {
        $this->authorize('update', $lead);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'company_name' => 'nullable|string|max:255',
            'title' => 'nullable|string|max:255',
            'address' => 'nullable|string',
            'lead_source_id' => 'required|exists:lead_sources,id',
            'lead_stage_id' => 'required|exists:lead_stages,id',
            'estimated_value' => 'nullable|numeric|min:0',
            'priority' => 'required|in:low,medium,high,urgent',
            'notes' => 'nullable|string',
            'next_follow_up_at' => 'nullable|date',
        ]);

        $lead = $this->leadService->updateLead($lead, $validated);

        return redirect()->route('leads.show', $lead)
            ->with('success', 'Lead updated successfully.');
    }

    /**
     * Remove the specified lead.
     */
    public function destroy(Lead $lead): RedirectResponse
    {
        $this->authorize('delete', $lead);

        $lead->delete();

        return redirect()->route('leads.index')
            ->with('success', 'Lead deleted successfully.');
    }

    /**
     * Convert lead to client.
     */
    public function convert(Lead $lead): RedirectResponse
    {
        $this->authorize('update', $lead);

        try {
            $client = $this->leadService->convertLead($lead);
            
            return redirect()->route('clients.show', $client)
                ->with('success', 'Lead converted to client successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', $e->getMessage());
        }
    }

    /**
     * Mark lead as lost.
     */
    public function markAsLost(Request $request, Lead $lead): RedirectResponse
    {
        $this->authorize('update', $lead);

        $validated = $request->validate([
            'lost_reason' => 'nullable|string|max:255',
        ]);

        $this->leadService->markLeadAsLost($lead, $validated['lost_reason'] ?? null);

        return redirect()->route('leads.show', $lead)
            ->with('success', 'Lead marked as lost.');
    }

    /**
     * Move lead to different stage (AJAX).
     */
    public function moveToStage(Request $request, Lead $lead): JsonResponse
    {
        $this->authorize('update', $lead);

        $validated = $request->validate([
            'stage_id' => 'required|exists:lead_stages,id',
        ]);

        try {
            $lead = $this->leadService->moveLeadToStage($lead, $validated['stage_id']);
            
            return response()->json([
                'success' => true,
                'message' => 'Lead moved successfully.',
                'lead' => $lead->load(['leadSource', 'leadStage']),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Add activity to lead.
     */
    public function addActivity(Request $request, Lead $lead): RedirectResponse
    {
        $this->authorize('update', $lead);

        $validated = $request->validate([
            'type' => 'required|in:call,email,meeting,proposal_sent,follow_up,note',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'scheduled_at' => 'nullable|date',
            'duration_minutes' => 'nullable|integer|min:1',
            'outcome' => 'nullable|in:positive,neutral,negative',
            'outcome_notes' => 'nullable|string',
        ]);

        $validated['status'] = $request->filled('scheduled_at') ? 'scheduled' : 'completed';

        $this->leadService->addActivity($lead, $validated);

        return redirect()->route('leads.show', $lead)
            ->with('success', 'Activity added successfully.');
    }

    /**
     * Add note to lead.
     */
    public function addNote(Request $request, Lead $lead): RedirectResponse
    {
        $this->authorize('update', $lead);

        $validated = $request->validate([
            'content' => 'required|string',
            'type' => 'required|in:general,meeting,call,email,reminder',
            'is_important' => 'boolean',
            'reminder_at' => 'nullable|date|after:now',
        ]);

        $this->leadService->addNote($lead, $validated);

        return redirect()->route('leads.show', $lead)
            ->with('success', 'Note added successfully.');
    }

    /**
     * Get lead analytics.
     */
    public function analytics(Request $request): View
    {
        $this->authorize('viewAny', Lead::class);

        $filters = [];
        if ($request->filled('start_date')) {
            $filters['start_date'] = $request->start_date;
        }
        if ($request->filled('end_date')) {
            $filters['end_date'] = $request->end_date;
        }

        $analytics = $this->leadService->getLeadAnalytics(Auth::id(), $filters);

        return view('leads.analytics', compact('analytics'));
    }

    /**
     * Export leads to CSV.
     */
    public function export(Request $request)
    {
        $this->authorize('viewAny', Lead::class);

        $filters = $request->only(['name', 'email', 'company', 'min_score', 'max_score']);
        $leads = $this->leadService->exportLeads(Auth::id(), $filters);

        $filename = 'leads_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($leads) {
            $file = fopen('php://output', 'w');
            
            // Add CSV headers
            if (!empty($leads)) {
                fputcsv($file, array_keys($leads[0]));
                
                // Add data rows
                foreach ($leads as $lead) {
                    fputcsv($file, $lead);
                }
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
