<?php

namespace App\Services;

use App\Models\ExpenseCategory;
use App\Repositories\ExpenseCategoryRepository;
use Illuminate\Support\Str;
use Exception;

class ExpenseCategoryService
{
    protected ExpenseCategoryRepository $repository;

    public function __construct(ExpenseCategoryRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Get all active categories
     */
    public function getActiveCategories()
    {
        return $this->repository->getActive();
    }

    /**
     * Get categories for dropdown
     */
    public function getCategoriesForDropdown()
    {
        return $this->repository->getForDropdown();
    }

    /**
     * Create a new expense category
     */
    public function createCategory(array $data): ExpenseCategory
    {
        // Generate unique slug
        $data['slug'] = $this->generateUniqueSlug($data['name']);
        
        // Set default sort order if not provided
        if (!isset($data['sort_order'])) {
            $data['sort_order'] = $this->repository->getNextSortOrder();
        }

        return $this->repository->create($data);
    }

    /**
     * Update an expense category
     */
    public function updateCategory(ExpenseCategory $category, array $data): bool
    {
        // Update slug if name changed
        if (isset($data['name']) && $data['name'] !== $category->name) {
            $data['slug'] = $this->generateUniqueSlug($data['name'], $category->id);
        }

        return $this->repository->update($category->id, $data);
    }

    /**
     * Delete an expense category
     */
    public function deleteCategory(ExpenseCategory $category): bool
    {
        // Check if category has expenses
        if ($category->expenses()->count() > 0) {
            throw new Exception('Cannot delete category that has expenses. Please reassign or delete the expenses first.');
        }

        return $this->repository->delete($category->id);
    }

    /**
     * Toggle category status
     */
    public function toggleStatus(ExpenseCategory $category): bool
    {
        return $this->repository->update($category->id, [
            'is_active' => !$category->is_active
        ]);
    }

    /**
     * Update sort order for multiple categories
     */
    public function updateSortOrder(array $categories): bool
    {
        try {
            foreach ($categories as $categoryData) {
                $this->repository->update($categoryData['id'], [
                    'sort_order' => $categoryData['sort_order']
                ]);
            }
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Get category with usage statistics
     */
    public function getCategoryWithStats(int $categoryId): array
    {
        $category = $this->repository->find($categoryId);
        if (!$category) {
            return [];
        }

        $stats = $this->repository->getUsageStats($categoryId);
        
        return [
            'category' => $category,
            'stats' => $stats
        ];
    }

    /**
     * Search categories
     */
    public function searchCategories(string $term)
    {
        return $this->repository->search($term)->get();
    }

    /**
     * Get categories by tax deductible status
     */
    public function getCategoriesByTaxDeductible(bool $taxDeductible = true)
    {
        return $this->repository->getByTaxDeductible($taxDeductible);
    }

    /**
     * Get categories with recent activity
     */
    public function getCategoriesWithRecentActivity(int $days = 30)
    {
        return $this->repository->getWithRecentActivity($days);
    }

    /**
     * Generate unique slug for category
     */
    private function generateUniqueSlug(string $name, int $excludeId = null): string
    {
        $slug = Str::slug($name);
        $originalSlug = $slug;
        $counter = 1;

        while ($this->repository->slugExists($slug, $excludeId)) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Get default categories for seeding
     */
    public function getDefaultCategories(): array
    {
        return [
            [
                'name' => 'Office Supplies',
                'slug' => 'office-supplies',
                'description' => 'Stationery, paper, pens, and other office materials',
                'icon' => 'fa-paperclip',
                'color' => 'blue',
                'is_tax_deductible' => true,
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Travel & Transportation',
                'slug' => 'travel-transportation',
                'description' => 'Business travel, flights, hotels, car rentals, fuel',
                'icon' => 'fa-plane',
                'color' => 'green',
                'is_tax_deductible' => true,
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Software & Subscriptions',
                'slug' => 'software-subscriptions',
                'description' => 'Software licenses, SaaS subscriptions, tools',
                'icon' => 'fa-laptop',
                'color' => 'purple',
                'is_tax_deductible' => true,
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Equipment & Hardware',
                'slug' => 'equipment-hardware',
                'description' => 'Computers, phones, furniture, machinery',
                'icon' => 'fa-desktop',
                'color' => 'gray',
                'is_tax_deductible' => true,
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'Miscellaneous',
                'slug' => 'miscellaneous',
                'description' => 'Other business-related expenses',
                'icon' => 'fa-ellipsis-h',
                'color' => 'gray',
                'is_tax_deductible' => false,
                'is_active' => true,
                'sort_order' => 5,
            ],
        ];
    }
}
