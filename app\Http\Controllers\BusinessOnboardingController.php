<?php

namespace App\Http\Controllers;

use App\Models\Business;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

class BusinessOnboardingController extends Controller
{
    /**
     * Show welcome page after business registration.
     */
    public function welcome(Business $business): View
    {
        // Ensure user has access to this business
        if (!Auth::user()->belongsToBusiness($business->id)) {
            abort(403);
        }

        return view('business.onboarding.welcome', compact('business'));
    }

    /**
     * Show business setup step.
     */
    public function setup(Business $business): View
    {
        if (!Auth::user()->belongsToBusiness($business->id)) {
            abort(403);
        }

        return view('business.onboarding.setup', compact('business'));
    }

    /**
     * Update business setup.
     */
    public function updateSetup(Request $request, Business $business): RedirectResponse
    {
        if (!Auth::user()->belongsToBusiness($business->id)) {
            abort(403);
        }

        $validated = $request->validate([
            'logo' => ['nullable', 'image', 'max:2048'],
            'primary_color' => ['nullable', 'string', 'regex:/^#[0-9A-Fa-f]{6}$/'],
            'currency' => ['required', 'string', 'size:3'],
            'timezone' => ['required', 'string'],
            'invoice_prefix' => ['required', 'string', 'max:10'],
            'payment_terms' => ['required', 'integer', 'min:1', 'max:365'],
        ]);

        // Handle logo upload
        if ($request->hasFile('logo')) {
            $logoPath = $request->file('logo')->store('business-logos', 'public');
            $business->update(['logo_path' => $logoPath]);
        }

        // Update business settings
        $settings = $business->businessSettings;
        if ($settings) {
            $settings->update([
                'currency' => $validated['currency'],
                'timezone' => $validated['timezone'],
                'invoice_prefix' => $validated['invoice_prefix'],
                'payment_terms' => $validated['payment_terms'],
                'branding_settings' => array_merge(
                    $settings->branding_settings ?? [],
                    ['primary_color' => $validated['primary_color'] ?? '#10b981']
                ),
            ]);
        }

        return redirect()->route('business.onboarding.features', $business)
                        ->with('success', 'Business setup updated successfully!');
    }

    /**
     * Show features selection step.
     */
    public function features(Business $business): View
    {
        if (!Auth::user()->belongsToBusiness($business->id)) {
            abort(403);
        }

        return view('business.onboarding.features', compact('business'));
    }

    /**
     * Update feature preferences.
     */
    public function updateFeatures(Request $request, Business $business): RedirectResponse
    {
        if (!Auth::user()->belongsToBusiness($business->id)) {
            abort(403);
        }

        $validated = $request->validate([
            'features' => ['required', 'array'],
            'features.*' => ['boolean'],
        ]);

        $settings = $business->businessSettings;
        if ($settings) {
            $settings->update([
                'feature_settings' => $validated['features'],
            ]);
        }

        return redirect()->route('business.onboarding.complete', $business)
                        ->with('success', 'Feature preferences saved successfully!');
    }

    /**
     * Show onboarding completion page.
     */
    public function complete(Business $business): View
    {
        if (!Auth::user()->belongsToBusiness($business->id)) {
            abort(403);
        }

        return view('business.onboarding.complete', compact('business'));
    }

    /**
     * Complete onboarding and redirect to dashboard.
     */
    public function finish(Business $business): RedirectResponse
    {
        if (!Auth::user()->belongsToBusiness($business->id)) {
            abort(403);
        }

        // Mark onboarding as complete (you can add a field for this if needed)
        session(['onboarding_completed' => true]);

        return redirect()->route('dashboard')
                        ->with('success', 'Welcome to your business dashboard! You\'re all set to get started.');
    }
}
