<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    {{ $proposal->title }}
                </h2>
                <p class="text-sm text-gray-600">{{ $proposal->proposal_number }}</p>
            </div>
            <div class="flex space-x-2">
                @if($proposal->canBeEdited())
                    <a href="{{ route('proposals.edit', $proposal) }}" 
                       class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        <i class="fas fa-edit mr-2"></i>Edit
                    </a>
                @endif
                
                @if($proposal->canBeSent())
                    <form method="POST" action="{{ route('proposals.send', $proposal) }}" class="inline">
                        @csrf
                        <button type="submit" 
                                class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                                onclick="return confirm('Are you sure you want to send this proposal?')">
                            <i class="fas fa-paper-plane mr-2"></i>Send
                        </button>
                    </form>
                @endif

                @if($proposal->canBeConverted())
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" 
                                class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                            <i class="fas fa-exchange-alt mr-2"></i>Convert
                            <i class="fas fa-chevron-down ml-1"></i>
                        </button>
                        <div x-show="open" @click.away="open = false"
                             class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
                            <a href="{{ route('proposals.show-convert-to-project', $proposal) }}"
                               class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-project-diagram mr-2"></i>Convert to Project
                            </a>
                            <a href="{{ route('proposals.show-convert-to-contract', $proposal) }}"
                               class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-file-contract mr-2"></i>Convert to Contract
                            </a>
                        </div>
                    </div>
                @endif

                <a href="{{ route('proposals.index') }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    <i class="fas fa-arrow-left mr-2"></i>Back
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <!-- Proposal Details -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Proposal Information</h3>
                            <dl class="space-y-3">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                                    <dd class="mt-1">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                            @if($proposal->status === 'draft') bg-gray-100 text-gray-800
                                            @elseif($proposal->status === 'sent') bg-blue-100 text-blue-800
                                            @elseif($proposal->status === 'viewed') bg-yellow-100 text-yellow-800
                                            @elseif($proposal->status === 'accepted') bg-green-100 text-green-800
                                            @elseif($proposal->status === 'rejected') bg-red-100 text-red-800
                                            @elseif($proposal->status === 'expired') bg-gray-100 text-gray-800
                                            @endif">
                                            {{ ucfirst($proposal->status) }}
                                        </span>
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Total Amount</dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        @if($proposal->total_amount)
                                            {{ $proposal->formatted_amount }}
                                        @else
                                            <span class="text-gray-400">Not specified</span>
                                        @endif
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Valid Until</dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        @if($proposal->valid_until)
                                            {{ $proposal->valid_until->format('M d, Y') }}
                                            @if($proposal->valid_until->isPast())
                                                <span class="text-red-500 text-xs">(Expired)</span>
                                            @endif
                                        @else
                                            <span class="text-gray-400">Not set</span>
                                        @endif
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Version</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $proposal->version }}</dd>
                                </div>
                            </dl>
                        </div>

                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Client Information</h3>
                            @if($proposal->client)
                                <dl class="space-y-3">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Name</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $proposal->client->name }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Email</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $proposal->client->email }}</dd>
                                    </div>
                                    @if($proposal->client->company_name)
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Company</dt>
                                            <dd class="mt-1 text-sm text-gray-900">{{ $proposal->client->company_name }}</dd>
                                        </div>
                                    @endif
                                </dl>
                            @else
                                <p class="text-gray-500">No client assigned</p>
                            @endif
                        </div>

                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Timeline</h3>
                            <dl class="space-y-3">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Created</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $proposal->created_at->format('M d, Y g:i A') }}</dd>
                                </div>
                                @if($proposal->sent_date)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Sent</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $proposal->sent_date->format('M d, Y g:i A') }}</dd>
                                    </div>
                                @endif
                                @if($proposal->viewed_date)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Viewed</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $proposal->viewed_date->format('M d, Y g:i A') }}</dd>
                                    </div>
                                @endif
                                @if($proposal->responded_date)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Responded</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $proposal->responded_date->format('M d, Y g:i A') }}</dd>
                                    </div>
                                @endif
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Proposal Content -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Proposal Content</h3>
                    <div class="prose max-w-none">
                        {!! nl2br(e($proposal->content)) !!}
                    </div>
                </div>
            </div>

            @if($proposal->description)
                <!-- Description -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Description</h3>
                        <p class="text-gray-700">{{ $proposal->description }}</p>
                    </div>
                </div>
            @endif

            @if($proposal->internal_notes)
                <!-- Internal Notes -->
                <div class="bg-yellow-50 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            <i class="fas fa-sticky-note text-yellow-500 mr-2"></i>Internal Notes
                        </h3>
                        <p class="text-gray-700">{{ $proposal->internal_notes }}</p>
                    </div>
                </div>
            @endif

            @if($proposal->client_notes)
                <!-- Client Notes -->
                <div class="bg-blue-50 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            <i class="fas fa-comment text-blue-500 mr-2"></i>Client Notes
                        </h3>
                        <p class="text-gray-700">{{ $proposal->client_notes }}</p>
                    </div>
                </div>
            @endif

            <!-- Related Items -->
            @if($proposal->projects->count() > 0 || $proposal->contracts->count() > 0)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Related Items</h3>
                        
                        @if($proposal->projects->count() > 0)
                            <div class="mb-4">
                                <h4 class="font-medium text-gray-900 mb-2">Projects</h4>
                                <ul class="space-y-2">
                                    @foreach($proposal->projects as $project)
                                        <li>
                                            <a href="{{ route('projects.show', $project) }}" 
                                               class="text-blue-600 hover:text-blue-900">
                                                <i class="fas fa-project-diagram mr-2"></i>{{ $project->name }}
                                            </a>
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        @if($proposal->contracts->count() > 0)
                            <div>
                                <h4 class="font-medium text-gray-900 mb-2">Contracts</h4>
                                <ul class="space-y-2">
                                    @foreach($proposal->contracts as $contract)
                                        <li>
                                            <a href="{{ route('contracts.show', $contract) }}" 
                                               class="text-blue-600 hover:text-blue-900">
                                                <i class="fas fa-file-contract mr-2"></i>{{ $contract->title }}
                                            </a>
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
