<?php

namespace App\Services;

use App\Models\File;
use App\Models\User;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class FileService
{
    /**
     * Upload and store a file.
     */
    public function uploadFile(UploadedFile $file, ?string $description = null, bool $isPublic = false): File
    {
        // Validate file
        $this->validateFile($file);

        // Generate unique filename
        $filename = $this->generateUniqueFilename($file);
        
        // Determine storage disk
        $disk = $isPublic ? 'public' : 'local';
        
        // Store file
        $path = $file->storeAs('files/' . date('Y/m'), $filename, $disk);

        // Create file record
        return File::create([
            'user_id' => Auth::id(),
            'name' => $file->getClientOriginalName(),
            'filename' => $filename,
            'path' => $path,
            'disk' => $disk,
            'mime_type' => $file->getMimeType(),
            'size' => $file->getSize(),
            'extension' => $file->getClientOriginalExtension(),
            'description' => $description,
            'is_public' => $isPublic,
            'metadata' => [
                'uploaded_at' => now()->toISOString(),
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ],
        ]);
    }

    /**
     * Get files for the authenticated user.
     */
    public function getUserFiles(?string $type = null, ?string $search = null, int $perPage = 15)
    {
        $query = File::forUser(Auth::id())
            ->latest()
            ->with('user');

        if ($type) {
            $query->byType($type);
        }

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        return $query->paginate($perPage);
    }

    /**
     * Get all files for admin.
     */
    public function getAllFiles(?string $type = null, ?string $search = null, int $perPage = 15)
    {
        $query = File::latest()
            ->with('user');

        if ($type) {
            $query->byType($type);
        }

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        return $query->paginate($perPage);
    }

    /**
     * Delete a file.
     */
    public function deleteFile(File $file): bool
    {
        // Delete physical file
        if (Storage::disk($file->disk)->exists($file->path)) {
            Storage::disk($file->disk)->delete($file->path);
        }

        // Delete database record
        return $file->delete();
    }

    /**
     * Update file details.
     */
    public function updateFile(File $file, array $data): File
    {
        $file->update([
            'description' => $data['description'] ?? $file->description,
            'is_public' => $data['is_public'] ?? $file->is_public,
        ]);

        return $file->fresh();
    }

    /**
     * Get file download response.
     */
    public function downloadFile(File $file)
    {
        // Mark as accessed
        $file->markAsAccessed();

        // Return download response
        return Storage::disk($file->disk)->download($file->path, $file->name);
    }

    /**
     * Get file statistics for user.
     */
    public function getUserFileStats(int $userId): array
    {
        $files = File::forUser($userId);

        return [
            'total_files' => $files->count(),
            'total_size' => $files->sum('size'),
            'images_count' => $files->byType('images')->count(),
            'documents_count' => $files->byType('documents')->count(),
            'recent_files' => $files->latest()->limit(5)->get(),
        ];
    }

    /**
     * Validate uploaded file.
     */
    private function validateFile(UploadedFile $file): void
    {
        $config = config('security.uploads');
        
        // Check file size
        if ($file->getSize() > ($config['max_file_size'] * 1024)) {
            throw ValidationException::withMessages([
                'file' => 'File size exceeds maximum allowed size of ' . $config['max_file_size'] . 'KB.'
            ]);
        }

        // Check file extension
        $extension = strtolower($file->getClientOriginalExtension());
        if (!in_array($extension, $config['allowed_extensions'])) {
            throw ValidationException::withMessages([
                'file' => 'File type not allowed. Allowed types: ' . implode(', ', $config['allowed_extensions'])
            ]);
        }

        // Check MIME type
        if (!in_array($file->getMimeType(), $config['allowed_mime_types'])) {
            throw ValidationException::withMessages([
                'file' => 'Invalid file type.'
            ]);
        }
    }

    /**
     * Generate unique filename.
     */
    private function generateUniqueFilename(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $hash = Str::random(32);
        
        return $hash . '.' . $extension;
    }

    /**
     * Check if user can upload files (plan limits).
     */
    public function canUploadFile(int $userId): bool
    {
        $user = User::find($userId);
        
        if (!$user) {
            return false;
        }

        // Admin users have no limits
        if ($user->hasRole('admin')) {
            return true;
        }

        // Check file storage limit (if implemented in plan features)
        $storageLimit = $user->getFeatureLimit('file_storage_mb');

        if ($storageLimit === null || $storageLimit === 'unlimited') {
            return true;
        }

        if (is_numeric($storageLimit) && (int) $storageLimit > 0) {
            $currentUsage = File::forUser($userId)->sum('size') / (1024 * 1024); // Convert to MB
            return $currentUsage < (int) $storageLimit;
        }

        // Default: allow upload if no specific limit
        return true;
    }

    /**
     * Get storage usage for user.
     */
    public function getStorageUsage(int $userId): array
    {
        $totalSize = File::forUser($userId)->sum('size');
        $user = User::find($userId);
        $limit = $user ? $user->getFeatureLimit('file_storage_mb') : 0;

        return [
            'used_bytes' => $totalSize,
            'used_mb' => round($totalSize / (1024 * 1024), 2),
            'limit_mb' => $limit === 'unlimited' ? null : (int) $limit,
            'percentage' => $limit === 'unlimited' ? 0 : (is_numeric($limit) && (int) $limit > 0 ? round(($totalSize / (1024 * 1024)) / (int) $limit * 100, 1) : 0),
        ];
    }
}
