<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class LeadStage extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'description',
        'color',
        'sort_order',
        'is_active',
        'is_won_stage',
        'is_lost_stage',
        'is_system_stage',
        'conversion_probability',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'is_won_stage' => 'boolean',
            'is_lost_stage' => 'boolean',
            'is_system_stage' => 'boolean',
            'sort_order' => 'integer',
            'conversion_probability' => 'decimal:2',
        ];
    }

    /**
     * Get the user that owns the lead stage.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the leads in this stage.
     */
    public function leads(): Has<PERSON>any
    {
        return $this->hasMany(Lead::class);
    }

    /**
     * Scope a query to only include active stages.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include user's stages.
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope a query to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Scope a query to exclude won/lost stages.
     */
    public function scopeActiveStages($query)
    {
        return $query->where('is_won_stage', false)
                    ->where('is_lost_stage', false);
    }

    /**
     * Get the next stage in the pipeline.
     */
    public function getNextStage(): ?LeadStage
    {
        return static::forUser($this->user_id)
            ->active()
            ->where('sort_order', '>', $this->sort_order)
            ->ordered()
            ->first();
    }

    /**
     * Get the previous stage in the pipeline.
     */
    public function getPreviousStage(): ?LeadStage
    {
        return static::forUser($this->user_id)
            ->active()
            ->where('sort_order', '<', $this->sort_order)
            ->orderBy('sort_order', 'desc')
            ->first();
    }

    /**
     * Check if this is a final stage (won or lost).
     */
    public function isFinalStage(): bool
    {
        return $this->is_won_stage || $this->is_lost_stage;
    }

    /**
     * Get stage statistics.
     */
    public function getStageStats(): array
    {
        $leads = $this->leads();
        
        return [
            'total_leads' => $leads->count(),
            'active_leads' => $leads->where('status', 'active')->count(),
            'average_lead_score' => $leads->avg('lead_score') ?? 0,
            'total_estimated_value' => $leads->sum('estimated_value') ?? 0,
            'average_time_in_stage' => $this->getAverageTimeInStage(),
        ];
    }

    /**
     * Calculate average time leads spend in this stage.
     */
    private function getAverageTimeInStage(): float
    {
        // This would require tracking stage changes in lead_activities
        // For now, return 0 - can be implemented later with activity tracking
        return 0;
    }

    /**
     * Check if stage can be deleted.
     */
    public function canBeDeleted(): bool
    {
        return !$this->is_system_stage && $this->leads()->count() === 0;
    }
}
