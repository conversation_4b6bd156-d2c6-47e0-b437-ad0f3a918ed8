@props([
    'steps' => [],
    'autoStart' => false,
    'showProgress' => true
])

@php
    $tourSteps = $steps ?: [
        [
            'target' => '[data-tour="dashboard"]',
            'title' => 'Welcome to Your Dashboard',
            'content' => 'This is your business command center. Here you can see all your key metrics at a glance.',
            'position' => 'bottom'
        ],
        [
            'target' => '[data-tour="create-invoice"]',
            'title' => 'Create Your First Invoice',
            'content' => 'Click here to create professional invoices and get paid faster.',
            'position' => 'bottom'
        ],
        [
            'target' => '[data-tour="clients"]',
            'title' => 'Manage Your Clients',
            'content' => 'Keep track of all your client information and project details in one place.',
            'position' => 'right'
        ],
        [
            'target' => '[data-tour="reports"]',
            'title' => 'Business Analytics',
            'content' => 'Monitor your business performance with detailed reports and insights.',
            'position' => 'right'
        ]
    ];
@endphp

<div x-data="tourGuide({{ json_encode($tourSteps) }}, {{ $autoStart ? 'true' : 'false' }})" 
     x-init="init()"
     class="tour-guide">
    
    <!-- Tour Overlay -->
    <div x-show="isActive" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 z-50 pointer-events-none"
         style="display: none;">
        
        <!-- Dark Overlay -->
        <div class="absolute inset-0 bg-secondary-900 bg-opacity-75 backdrop-blur-sm"></div>
        
        <!-- Spotlight -->
        <div x-show="currentStep >= 0" 
             :style="spotlightStyle"
             class="absolute bg-white bg-opacity-10 rounded-2xl border-4 border-primary-400 shadow-glow-lg transition-all duration-500 ease-out pointer-events-none">
        </div>
    </div>
    
    <!-- Tour Tooltip -->
    <div x-show="isActive && currentStep >= 0" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 scale-95"
         x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-95"
         :style="tooltipStyle"
         class="fixed z-50 pointer-events-auto"
         style="display: none;">
        
        <div class="bg-white rounded-2xl shadow-card-lg border border-secondary-200 max-w-sm">
            <!-- Header -->
            <div class="bg-gradient-to-r from-primary-500 to-primary-600 text-white p-4 rounded-t-2xl">
                <div class="flex items-center justify-between">
                    <h3 class="font-bold text-lg" x-text="currentStepData.title"></h3>
                    <button @click="endTour()" 
                            class="text-white hover:text-primary-200 transition-colors duration-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                @if($showProgress)
                    <!-- Progress Bar -->
                    <div class="mt-3">
                        <div class="flex items-center justify-between text-sm text-primary-100 mb-1">
                            <span>Progress</span>
                            <span x-text="`${currentStep + 1} of ${steps.length}`"></span>
                        </div>
                        <div class="w-full bg-primary-400 bg-opacity-30 rounded-full h-2">
                            <div class="bg-white h-2 rounded-full transition-all duration-300" 
                                 :style="`width: ${((currentStep + 1) / steps.length) * 100}%`"></div>
                        </div>
                    </div>
                @endif
            </div>
            
            <!-- Content -->
            <div class="p-6">
                <p class="text-secondary-600 mb-6" x-text="currentStepData.content"></p>
                
                <!-- Navigation Buttons -->
                <div class="flex items-center justify-between">
                    <button x-show="currentStep > 0" 
                            @click="previousStep()"
                            class="btn btn-ghost">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Previous
                    </button>
                    
                    <div class="flex items-center space-x-3 ml-auto">
                        <button @click="endTour()" 
                                class="text-secondary-500 hover:text-secondary-700 font-medium">
                            Skip Tour
                        </button>
                        
                        <button @click="nextStep()"
                                class="btn btn-primary">
                            <span x-text="currentStep < steps.length - 1 ? 'Next' : 'Finish'"></span>
                            <i class="fas fa-arrow-right ml-2" x-show="currentStep < steps.length - 1"></i>
                            <i class="fas fa-check ml-2" x-show="currentStep >= steps.length - 1"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Tooltip Arrow -->
        <div :class="arrowClass" class="absolute w-4 h-4 bg-white border border-secondary-200 transform rotate-45"></div>
    </div>
    
    <!-- Tour Trigger Button (when not active) -->
    <button x-show="!isActive && !hasCompleted" 
            @click="startTour()"
            class="fixed bottom-6 right-6 z-40 bg-gradient-to-r from-primary-500 to-primary-600 text-white p-4 rounded-full shadow-glow-lg hover:shadow-glow-xl transition-all duration-300 transform hover:scale-110">
        <i class="fas fa-question text-xl"></i>
    </button>
</div>

<script>
function tourGuide(steps, autoStart) {
    return {
        steps: steps,
        currentStep: -1,
        isActive: false,
        hasCompleted: localStorage.getItem('tour-completed') === 'true',
        currentStepData: {},
        spotlightStyle: '',
        tooltipStyle: '',
        arrowClass: '',
        
        init() {
            if (autoStart && !this.hasCompleted) {
                setTimeout(() => this.startTour(), 1000);
            }
        },
        
        startTour() {
            this.isActive = true;
            this.currentStep = 0;
            this.updateStep();
        },
        
        nextStep() {
            if (this.currentStep < this.steps.length - 1) {
                this.currentStep++;
                this.updateStep();
            } else {
                this.endTour();
            }
        },
        
        previousStep() {
            if (this.currentStep > 0) {
                this.currentStep--;
                this.updateStep();
            }
        },
        
        endTour() {
            this.isActive = false;
            this.currentStep = -1;
            this.hasCompleted = true;
            localStorage.setItem('tour-completed', 'true');
        },
        
        updateStep() {
            if (this.currentStep >= 0 && this.currentStep < this.steps.length) {
                this.currentStepData = this.steps[this.currentStep];
                this.$nextTick(() => {
                    this.positionElements();
                });
            }
        },
        
        positionElements() {
            const target = document.querySelector(this.currentStepData.target);
            if (!target) return;
            
            const rect = target.getBoundingClientRect();
            const padding = 8;
            
            // Position spotlight
            this.spotlightStyle = `
                top: ${rect.top - padding}px;
                left: ${rect.left - padding}px;
                width: ${rect.width + (padding * 2)}px;
                height: ${rect.height + (padding * 2)}px;
            `;
            
            // Position tooltip
            const tooltipOffset = 20;
            let tooltipTop, tooltipLeft;
            
            switch (this.currentStepData.position) {
                case 'top':
                    tooltipTop = rect.top - tooltipOffset;
                    tooltipLeft = rect.left + (rect.width / 2);
                    this.arrowClass = 'top-full left-1/2 transform -translate-x-1/2 -translate-y-1/2';
                    break;
                case 'bottom':
                    tooltipTop = rect.bottom + tooltipOffset;
                    tooltipLeft = rect.left + (rect.width / 2);
                    this.arrowClass = 'bottom-full left-1/2 transform -translate-x-1/2 translate-y-1/2';
                    break;
                case 'left':
                    tooltipTop = rect.top + (rect.height / 2);
                    tooltipLeft = rect.left - tooltipOffset;
                    this.arrowClass = 'left-full top-1/2 transform -translate-y-1/2 -translate-x-1/2';
                    break;
                case 'right':
                default:
                    tooltipTop = rect.top + (rect.height / 2);
                    tooltipLeft = rect.right + tooltipOffset;
                    this.arrowClass = 'right-full top-1/2 transform -translate-y-1/2 translate-x-1/2';
                    break;
            }
            
            this.tooltipStyle = `
                top: ${tooltipTop}px;
                left: ${tooltipLeft}px;
                transform: translate(-50%, -50%);
            `;
        }
    }
}
</script>
