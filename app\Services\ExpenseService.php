<?php

namespace App\Services;

use App\Models\Expense;
use App\Models\ExpenseCategory;
use App\Repositories\ExpenseRepository;
use App\Services\PlanChecker;
use App\Traits\HasCalculations;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Exception;

class ExpenseService
{
    use HasCalculations;

    protected ExpenseRepository $expenseRepository;

    public function __construct(ExpenseRepository $expenseRepository)
    {
        $this->expenseRepository = $expenseRepository;
    }

    /**
     * Get expenses for a user with filters
     */
    public function getExpensesForUser(int $userId, $request = null)
    {
        return $this->expenseRepository->getForUser($userId, $request);
    }

    /**
     * Create a new expense
     */
    public function createExpense(array $data): Expense
    {
        DB::beginTransaction();
        
        try {
            // Calculate tax amount if tax percentage is provided
            if (isset($data['tax_percentage']) && $data['tax_percentage'] > 0) {
                $data['tax_amount'] = $this->calculateTaxAmount($data['amount'], $data['tax_percentage']);
            }

            // Set user ID
            $data['user_id'] = Auth::id();

            // Handle receipt upload
            if (isset($data['receipt']) && $data['receipt']) {
                $data['receipt_path'] = $this->handleReceiptUpload($data['receipt']);
                unset($data['receipt']);
            }

            $expense = Expense::create($data);

            DB::commit();
            return $expense;
        } catch (\Exception $e) {
            DB::rollBack();

            \Log::error('Expense creation failed', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'data' => array_except($data, ['receipt']) // Don't log file data
            ]);

            throw new \Exception('Failed to create expense: ' . $e->getMessage());
        }
    }

    /**
     * Update an expense
     */
    public function updateExpense(Expense $expense, array $data): bool
    {
        DB::beginTransaction();
        
        try {
            // Calculate tax amount if tax percentage is provided
            if (isset($data['tax_percentage']) && $data['tax_percentage'] > 0) {
                $data['tax_amount'] = $this->calculateTaxAmount($data['amount'], $data['tax_percentage']);
            }

            // Handle receipt upload
            if (isset($data['receipt']) && $data['receipt']) {
                // Delete old receipt if exists
                if ($expense->receipt_path) {
                    Storage::delete($expense->receipt_path);
                }
                $data['receipt_path'] = $this->handleReceiptUpload($data['receipt']);
                unset($data['receipt']);
            }

            $updated = $expense->update($data);

            DB::commit();
            return $updated;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Delete an expense
     */
    public function deleteExpense(Expense $expense): bool
    {
        DB::beginTransaction();
        
        try {
            // Delete receipt file if exists
            if ($expense->receipt_path) {
                Storage::delete($expense->receipt_path);
            }

            $deleted = $expense->delete();

            DB::commit();
            return $deleted;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Check if user can create expenses
     */
    public function canCreateExpense(int $userId): bool
    {
        $user = \App\Models\User::find($userId);
        return \App\Services\PlanChecker::canCreateExpense($user);
    }

    /**
     * Get expense statistics for dashboard
     */
    public function getDashboardStats(int $userId): array
    {
        return $this->expenseRepository->getStatistics($userId, 'month');
    }

    /**
     * Get monthly expense totals
     */
    public function getMonthlyTotals(int $userId, int $year = null): array
    {
        return $this->expenseRepository->getMonthlyTotals($userId, $year);
    }

    /**
     * Get expense totals by category
     */
    public function getCategoryTotals(int $userId, string $startDate = null, string $endDate = null): array
    {
        return $this->expenseRepository->getTotalsByCategory($userId, $startDate, $endDate);
    }

    /**
     * Get billable expenses for a client
     */
    public function getBillableExpensesForClient(int $clientId, bool $unbilledOnly = true)
    {
        return $this->expenseRepository->getBillableForClient($clientId, $unbilledOnly);
    }

    /**
     * Mark billable expenses as billed
     */
    public function markExpensesAsBilled(array $expenseIds): bool
    {
        return Expense::whereIn('id', $expenseIds)
            ->where('is_billable', true)
            ->update(['is_billed' => true]);
    }

    /**
     * Approve an expense
     */
    public function approveExpense(Expense $expense): bool
    {
        return $expense->update([
            'status' => 'approved',
            'approved_at' => now(),
            'approved_by' => Auth::id(),
        ]);
    }

    /**
     * Reject an expense
     */
    public function rejectExpense(Expense $expense, string $reason = null): bool
    {
        $data = ['status' => 'rejected'];
        
        if ($reason) {
            $data['notes'] = ($expense->notes ? $expense->notes . "\n\n" : '') . "Rejection reason: " . $reason;
        }

        return $expense->update($data);
    }

    /**
     * Submit expense for approval
     */
    public function submitForApproval(Expense $expense): bool
    {
        return $expense->update(['status' => 'submitted']);
    }

    /**
     * Get active expense categories
     */
    public function getActiveCategories()
    {
        return ExpenseCategory::active()->ordered()->limit(200)->get();
    }

    /**
     * Handle receipt file upload
     */
    private function handleReceiptUpload($file): string
    {
        $path = $file->store('receipts/' . Auth::id(), 'public');
        return $path;
    }

    /**
     * Calculate tax amount
     */
    private function calculateTaxAmount(float $amount, float $taxPercentage): float
    {
        return round(($amount * $taxPercentage) / 100, 2);
    }

    /**
     * Get expense export data
     */
    public function getExportData(int $userId, array $filters = []): array
    {
        $query = Expense::forUser($userId)
            ->with(['category:id,name', 'client:id,name,company_name', 'project:id,name']);

        // Apply filters
        if (isset($filters['start_date']) && isset($filters['end_date'])) {
            $query->whereBetween('expense_date', [$filters['start_date'], $filters['end_date']]);
        }

        if (isset($filters['category_id'])) {
            $query->where('category_id', $filters['category_id']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        return $query->orderBy('expense_date', 'desc')->get()->map(function ($expense) {
            return [
                'Expense Number' => $expense->expense_number,
                'Date' => $expense->expense_date->format('Y-m-d'),
                'Category' => $expense->category->name,
                'Description' => $expense->description,
                'Amount' => $expense->amount,
                'Tax Amount' => $expense->tax_amount,
                'Total Amount' => $expense->total_amount,
                'Client' => $expense->client ? $expense->client->display_name : '',
                'Project' => $expense->project ? $expense->project->name : '',
                'Vendor' => $expense->vendor_name ?? '',
                'Payment Method' => $expense->payment_method ?? '',
                'Status' => ucfirst($expense->status),
                'Billable' => $expense->is_billable ? 'Yes' : 'No',
                'Billed' => $expense->is_billed ? 'Yes' : 'No',
            ];
        })->toArray();
    }

    /**
     * Get recent expenses for dashboard
     */
    public function getRecentExpenses(int $userId, int $limit = 5)
    {
        return $this->expenseRepository->getRecent($userId, $limit);
    }

    /**
     * Get available years for expenses
     */
    public function getAvailableYears(int $userId): array
    {
        $years = $this->expenseRepository->model->newQuery()
            ->forUser($userId)
            ->selectRaw('YEAR(expense_date) as year')
            ->distinct()
            ->orderBy('year', 'desc')
            ->pluck('year')
            ->toArray();

        // Add current year if not present
        $currentYear = now()->year;
        if (!in_array($currentYear, $years)) {
            array_unshift($years, $currentYear);
        }

        return $years;
    }
}
