<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     */
    public function create(): View
    {
        return view('auth.login');
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request): RedirectResponse
    {
        $request->authenticate();

        $request->session()->regenerate();

        return redirect()->intended(route('dashboard', absolute: false));
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {
        try {
            // Log the logout attempt
            \Log::info('Logout attempt', [
                'user_id' => auth()->id(),
                'session_id' => $request->session()->getId(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            Auth::guard('web')->logout();

            $request->session()->invalidate();
            $request->session()->regenerateToken();

            // Log successful logout
            \Log::info('Logout successful');

            // Add success message
            session()->flash('success', 'You have been successfully logged out.');

            return redirect('/');
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Logout failed', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            // Add error message
            session()->flash('error', 'An error occurred during logout. Please try again.');

            return redirect()->back();
        }
    }
}
