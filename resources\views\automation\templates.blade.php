<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Notification Templates') }}
            </h2>
            <a href="{{ route('automation.dashboard') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Automation
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    @if($templates->isEmpty())
                        <div class="text-center py-8">
                            <i class="fas fa-envelope text-gray-400 text-4xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No Templates Found</h3>
                            <p class="text-gray-600 mb-4">You haven't created any notification templates yet.</p>
                            <a href="{{ route('automation.dashboard') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                                <i class="fas fa-plus mr-2"></i>
                                Create Template
                            </a>
                        </div>
                    @else
                        @foreach($templates as $type => $typeTemplates)
                            <div class="mb-8">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4 capitalize">{{ str_replace('_', ' ', $type) }} Templates</h3>
                                
                                @foreach($typeTemplates as $channel => $channelTemplates)
                                    <div class="mb-6">
                                        <h4 class="text-md font-medium text-gray-700 mb-3 capitalize">{{ $channel }}</h4>
                                        
                                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                            @foreach($channelTemplates as $template)
                                                <div class="border border-gray-200 rounded-lg p-4">
                                                    <div class="flex items-center justify-between mb-2">
                                                        <h5 class="font-medium text-gray-900">{{ $template->name }}</h5>
                                                        <span class="px-2 py-1 text-xs font-semibold rounded-full 
                                                            @if($template->is_active) bg-green-100 text-green-800 @else bg-gray-100 text-gray-800 @endif">
                                                            {{ $template->is_active ? 'Active' : 'Inactive' }}
                                                        </span>
                                                    </div>
                                                    
                                                    @if($template->description)
                                                        <p class="text-sm text-gray-600 mb-3">{{ $template->description }}</p>
                                                    @endif
                                                    
                                                    <div class="text-xs text-gray-500 mb-3">
                                                        <strong>Subject:</strong> {{ Str::limit($template->subject, 50) }}
                                                    </div>
                                                    
                                                    <div class="flex items-center justify-between">
                                                        <span class="text-xs text-gray-500">
                                                            Updated {{ $template->updated_at->diffForHumans() }}
                                                        </span>
                                                        <div class="flex space-x-2">
                                                            <button class="text-blue-600 hover:text-blue-800 text-sm">Edit</button>
                                                            <button class="text-red-600 hover:text-red-800 text-sm">Delete</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @endforeach
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
