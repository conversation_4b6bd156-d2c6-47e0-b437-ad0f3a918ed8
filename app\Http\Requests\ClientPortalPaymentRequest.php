<?php

namespace App\Http\Requests;

use App\Services\ClientPortalService;
use Illuminate\Foundation\Http\FormRequest;

class ClientPortalPaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Authorization is handled in the controller via token validation
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $invoice = $this->route('invoice');
        $maxAmount = $invoice ? $invoice->remaining_amount : 999999;

        return [
            'amount' => [
                'required',
                'numeric',
                'min:1',
                "max:{$maxAmount}",
                'regex:/^\d+(\.\d{1,2})?$/', // Allow up to 2 decimal places
            ],
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'amount.required' => 'Payment amount is required.',
            'amount.numeric' => 'Payment amount must be a valid number.',
            'amount.min' => 'Payment amount must be at least ₹1.00.',
            'amount.max' => 'Payment amount cannot exceed the remaining invoice amount.',
            'amount.regex' => 'Payment amount can have at most 2 decimal places.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $invoice = $this->route('invoice');
            $amount = $this->input('amount');

            if ($invoice && $amount) {
                $clientPortalService = app(ClientPortalService::class);

                // Check if payment can be made
                $canMakePayment = $clientPortalService->canMakePayment($invoice);
                if (!$canMakePayment['can_pay']) {
                    $validator->errors()->add('payment', $canMakePayment['reason']);
                }

                // Validate payment amount
                $validation = $clientPortalService->validatePaymentAmount($invoice, (float) $amount);
                if (!$validation['valid']) {
                    $validator->errors()->add('amount', $validation['message']);
                }
            }
        });
    }

    /**
     * Get the validated data with sanitized amount.
     */
    public function validated($key = null, $default = null): array
    {
        $validated = parent::validated($key, $default);

        // Ensure amount is properly formatted
        if (isset($validated['amount'])) {
            $validated['amount'] = round((float) $validated['amount'], 2);
        }

        return $validated;
    }
}
