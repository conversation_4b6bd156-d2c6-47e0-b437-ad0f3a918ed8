<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <nav class="flex mb-2" aria-label="Breadcrumb">
                    <ol class="flex items-center space-x-4">
                        <li>
                            <a href="{{ route('admin.subscriptions.index') }}" class="text-gray-400 hover:text-gray-500">
                                <span>Subscriptions</span>
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-right text-gray-400 text-sm"></i>
                                <span class="ml-4 text-sm font-medium text-gray-500">{{ $subscription->user->name }}</span>
                            </div>
                        </li>
                    </ol>
                </nav>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    {{ __('Subscription Details') }}
                </h2>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="mb-6 flex justify-end space-x-3">
                @if($subscription->status === 'active')
                    <form method="POST" action="{{ route('admin.subscriptions.cancel', $subscription) }}" class="inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit"
                                onclick="return confirm('Are you sure you want to cancel this subscription?')"
                                class="inline-flex items-center px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50">
                            <i class="fas fa-times mr-2"></i>
                            Cancel Subscription
                        </button>
                    </form>
                @elseif($subscription->status === 'cancelled')
                    <form method="POST" action="{{ route('admin.subscriptions.reactivate', $subscription) }}" class="inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit"
                                onclick="return confirm('Are you sure you want to reactivate this subscription?')"
                                class="inline-flex items-center px-4 py-2 border border-green-300 rounded-md shadow-sm text-sm font-medium text-green-700 bg-white hover:bg-green-50">
                            <i class="fas fa-play mr-2"></i>
                            Reactivate
                        </button>
                    </form>
                @endif
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Subscription Information -->
                <div class="lg:col-span-2">
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                        <div class="px-4 py-5 sm:px-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">Subscription Information</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500">Details about this subscription</p>
                        </div>
                        <div class="border-t border-gray-200">
                            <dl>
                                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            {{ $subscription->status === 'active' ? 'bg-green-100 text-green-800' : '' }}
                                            {{ $subscription->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : '' }}
                                            {{ $subscription->status === 'cancelled' ? 'bg-red-100 text-red-800' : '' }}
                                            {{ $subscription->status === 'expired' ? 'bg-gray-100 text-gray-800' : '' }}">
                                            {{ ucfirst($subscription->status) }}
                                        </span>
                                    </dd>
                                </div>
                                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">Plan</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $subscription->plan->name }}</dd>
                                </div>
                                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">Amount Paid</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        ${{ number_format($subscription->amount_paid, 2) }} {{ $subscription->currency }}
                                    </dd>
                                </div>
                                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">Started At</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ $subscription->starts_at?->format('M d, Y H:i:s') }}
                                    </dd>
                                </div>
                                @if($subscription->ends_at)
                                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                        <dt class="text-sm font-medium text-gray-500">Ends At</dt>
                                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                            {{ $subscription->ends_at->format('M d, Y H:i:s') }}
                                        </dd>
                                    </div>
                                @endif
                                @if($subscription->cancelled_at)
                                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                        <dt class="text-sm font-medium text-gray-500">Cancelled At</dt>
                                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                            {{ $subscription->cancelled_at->format('M d, Y H:i:s') }}
                                        </dd>
                                    </div>
                                @endif
                                @if($subscription->gateway_subscription_id)
                                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                        <dt class="text-sm font-medium text-gray-500">Gateway Subscription ID</dt>
                                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                            {{ $subscription->gateway_subscription_id }}
                                        </dd>
                                    </div>
                                @endif
                            </dl>
                        </div>
                    </div>

                    <!-- Change Plan Form -->
                    <div class="mt-6 bg-white shadow overflow-hidden sm:rounded-lg">
                        <div class="px-4 py-5 sm:px-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">Change Plan</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500">Update the subscription plan</p>
                        </div>
                        <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
                            <form method="POST" action="{{ route('admin.subscriptions.change-plan', $subscription) }}">
                                @csrf
                                @method('PATCH')
                                <div class="flex items-end space-x-4">
                                    <div class="flex-1">
                                        <label for="plan_id" class="block text-sm font-medium text-gray-700">New Plan</label>
                                        <select name="plan_id" id="plan_id" required 
                                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                            @foreach(\App\Models\Plan::all() as $plan)
                                                <option value="{{ $plan->id }}" {{ $plan->id === $subscription->plan_id ? 'selected' : '' }}>
                                                    {{ $plan->name }} - ${{ number_format($plan->price, 2) }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <button type="submit" 
                                            onclick="return confirm('Are you sure you want to change the plan?')"
                                            class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        Change Plan
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- User Information -->
                <div>
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                        <div class="px-4 py-5 sm:px-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">User Information</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500">Details about the subscriber</p>
                        </div>
                        <div class="border-t border-gray-200">
                            <dl>
                                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">Name</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $subscription->user->name }}</dd>
                                </div>
                                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">Email</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $subscription->user->email }}</dd>
                                </div>
                                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">Joined</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ $subscription->user->created_at->format('M d, Y') }}
                                    </dd>
                                </div>
                                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">Actions</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        <a href="{{ route('admin.users.show', $subscription->user) }}" 
                                           class="text-blue-600 hover:text-blue-900">
                                            View User Profile
                                        </a>
                                    </dd>
                                </div>
                            </dl>
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="mt-6 bg-white shadow overflow-hidden sm:rounded-lg">
                        <div class="px-4 py-5 sm:px-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">Payment History</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500">Recent payments for this subscription</p>
                        </div>
                        <div class="border-t border-gray-200">
                            @if($subscription->payments->count() > 0)
                                <ul class="divide-y divide-gray-200">
                                    @foreach($subscription->payments->take(5) as $payment)
                                        <li class="px-4 py-3">
                                            <div class="flex items-center justify-between">
                                                <div>
                                                    <p class="text-sm font-medium text-gray-900">
                                                        ${{ number_format($payment->amount, 2) }}
                                                    </p>
                                                    <p class="text-xs text-gray-500">
                                                        {{ $payment->created_at->format('M d, Y') }}
                                                    </p>
                                                </div>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                    {{ $payment->status === 'completed' ? 'bg-green-100 text-green-800' : '' }}
                                                    {{ $payment->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : '' }}
                                                    {{ $payment->status === 'failed' ? 'bg-red-100 text-red-800' : '' }}">
                                                    {{ ucfirst($payment->status) }}
                                                </span>
                                            </div>
                                        </li>
                                    @endforeach
                                </ul>
                                @if($subscription->payments->count() > 5)
                                    <div class="px-4 py-3 border-t border-gray-200">
                                        <a href="{{ route('admin.subscriptions.payments', $subscription) }}" 
                                           class="text-sm text-blue-600 hover:text-blue-900">
                                            View all payments ({{ $subscription->payments->count() }})
                                        </a>
                                    </div>
                                @endif
                            @else
                                <div class="px-4 py-6 text-center text-gray-500">
                                    <i class="fas fa-credit-card text-2xl mb-2"></i>
                                    <p>No payments found</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
