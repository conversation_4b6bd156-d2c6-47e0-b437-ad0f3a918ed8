@extends('emails.layouts.app')

@section('content')
    <!-- Verification Header -->
    <div style="text-align: center; margin-bottom: 32px;">
        <div style="font-size: 64px; margin-bottom: 16px;">🔐</div>
        <h2 style="color: #10b981; margin-bottom: 8px;">Verify Your Email Address</h2>
        <p class="lead" style="color: #059669;">
            Hi {{ $user->name }}, please verify your email address to secure your account and unlock all features.
        </p>
    </div>
    
    <!-- Verification Message -->
    <p style="font-size: 18px; color: #374151; margin-bottom: 24px;">
        Thank you for registering with {{ config('app.name') }}! To complete your account setup and ensure the security of your account, please verify your email address.
    </p>
    
    <!-- Security Information -->
    <div class="info-box">
        <h3>🛡️ Why Email Verification is Important</h3>
        <ul class="feature-list">
            <li><strong>Account Security</strong> - Protects your account from unauthorized access</li>
            <li><strong>Password Recovery</strong> - Enables secure password reset if needed</li>
            <li><strong>Important Notifications</strong> - Ensures you receive critical account updates</li>
            <li><strong>Full Access</strong> - Unlocks all platform features and capabilities</li>
        </ul>
    </div>
    
    <!-- Verification Button -->
    <div style="text-align: center; margin: 40px 0;">
        <a href="{{ $verificationUrl }}" class="btn btn-primary" style="font-size: 18px; padding: 18px 36px; text-decoration: none;">
            🔐 Verify Email Address
        </a>
        <p style="color: #64748b; font-size: 14px; margin-top: 16px;">
            This verification link will expire in 24 hours for security reasons.
        </p>
    </div>
    
    <!-- Alternative Verification -->
    <div class="highlight-box">
        <h3>📱 Can't Click the Button?</h3>
        <p>If you're having trouble clicking the verification button, copy and paste this link into your browser:</p>
        <div style="background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 16px; margin: 16px 0; word-break: break-all; font-family: monospace; font-size: 14px; color: #475569;">
            {{ $verificationUrl }}
        </div>
    </div>
    
    <!-- What Happens Next -->
    <h3>🎯 What Happens After Verification?</h3>
    <p>Once you verify your email address, you'll be able to:</p>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 16px; margin: 24px 0;">
        <div style="background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); padding: 20px; border-radius: 12px; border: 1px solid #bbf7d0;">
            <div style="color: #166534; font-size: 24px; margin-bottom: 8px;">🚀</div>
            <h4 style="margin: 0 0 8px 0; color: #166534; font-size: 16px;">Full Platform Access</h4>
            <p style="margin: 0; color: #15803d; font-size: 14px;">Access all features without restrictions</p>
        </div>
        
        <div style="background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%); padding: 20px; border-radius: 12px; border: 1px solid #bae6fd;">
            <div style="color: #1e40af; font-size: 24px; margin-bottom: 8px;">📧</div>
            <h4 style="margin: 0 0 8px 0; color: #1e40af; font-size: 16px;">Email Notifications</h4>
            <p style="margin: 0; color: #2563eb; font-size: 14px;">Receive important updates and alerts</p>
        </div>
        
        <div style="background: linear-gradient(135deg, #fdf4ff 0%, #fae8ff 100%); padding: 20px; border-radius: 12px; border: 1px solid #f5d0fe;">
            <div style="color: #a21caf; font-size: 24px; margin-bottom: 8px;">🔒</div>
            <h4 style="margin: 0 0 8px 0; color: #a21caf; font-size: 16px;">Enhanced Security</h4>
            <p style="margin: 0; color: #c026d3; font-size: 14px;">Secure password recovery options</p>
        </div>
        
        <div style="background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%); padding: 20px; border-radius: 12px; border: 1px solid #fde68a;">
            <div style="color: #92400e; font-size: 24px; margin-bottom: 8px;">⭐</div>
            <h4 style="margin: 0 0 8px 0; color: #92400e; font-size: 16px;">Premium Features</h4>
            <p style="margin: 0; color: #b45309; font-size: 14px;">Unlock advanced tools and capabilities</p>
        </div>
    </div>
    
    <!-- Security Notice -->
    <div class="warning-box">
        <h3 style="color: #92400e; margin: 0 0 12px 0;">🔒 Security Notice</h3>
        <p style="color: #b45309; margin: 0 0 16px 0;">
            For your security, this verification link is unique to your account and will expire in 24 hours. 
            If you didn't create an account with {{ config('app.name') }}, please ignore this email.
        </p>
        <div style="background-color: rgba(255, 255, 255, 0.7); padding: 16px; border-radius: 8px;">
            <p style="color: #b45309; margin: 0; font-size: 14px;">
                <strong>Important:</strong> Never share this verification link with anyone. Our team will never ask for your verification link or password via email.
            </p>
        </div>
    </div>
    
    <!-- Troubleshooting -->
    <h3>❓ Need Help?</h3>
    <p>If you're experiencing any issues with email verification, here are some common solutions:</p>
    
    <div style="background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px; margin: 20px 0;">
        <div style="display: flex; flex-wrap: wrap; gap: 20px;">
            <div style="flex: 1; min-width: 200px;">
                <h4 style="color: #1e293b; margin: 0 0 8px 0; font-size: 16px;">📧 Check Your Spam Folder</h4>
                <p style="color: #64748b; margin: 0; font-size: 14px;">Sometimes verification emails end up in spam or junk folders</p>
            </div>
            <div style="flex: 1; min-width: 200px;">
                <h4 style="color: #1e293b; margin: 0 0 8px 0; font-size: 16px;">⏰ Link Expired?</h4>
                <p style="color: #64748b; margin: 0; font-size: 14px;">Request a new verification email from your account settings</p>
            </div>
            <div style="flex: 1; min-width: 200px;">
                <h4 style="color: #1e293b; margin: 0 0 8px 0; font-size: 16px;">🔗 Link Not Working?</h4>
                <p style="color: #64748b; margin: 0; font-size: 14px;">Try copying and pasting the full URL into your browser</p>
            </div>
        </div>
    </div>
    
    <!-- Support Contact -->
    <div style="text-align: center; background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; padding: 32px; margin: 32px 0;">
        <div style="font-size: 48px; margin-bottom: 16px;">🎧</div>
        <h3 style="color: #1e293b; margin: 0 0 12px 0;">Still Need Help?</h3>
        <p style="color: #64748b; margin: 0 0 24px 0;">
            Our support team is here to help you get verified and started. We typically respond within 2 hours during business hours.
        </p>
        
        <div style="display: flex; flex-wrap: wrap; gap: 12px; justify-content: center;">
            <a href="mailto:support@{{ config('app.domain', 'freeligo.com') }}" class="btn btn-outline">
                📧 Email Support
            </a>
            <a href="#" class="btn btn-secondary">
                💬 Live Chat
            </a>
        </div>
        
        <p style="color: #64748b; font-size: 14px; margin: 20px 0 0 0;">
            <strong>Support Hours:</strong> Monday - Friday, 9:00 AM - 6:00 PM IST
        </p>
    </div>
    
    <!-- Welcome Message -->
    <div style="text-align: center; margin: 40px 0;">
        <h3 style="color: #1e293b; margin-bottom: 16px;">Welcome to {{ config('app.name') }}! 🎉</h3>
        <p style="color: #64748b; font-size: 18px; margin-bottom: 20px;">
            We're excited to have you on board and can't wait to help you grow your business.
        </p>
        <p style="color: #64748b; font-size: 16px;">
            Just one click away from getting started!
        </p>
    </div>
    
    <!-- Footer Note -->
    <div style="border-top: 1px solid #e2e8f0; padding-top: 20px; margin-top: 32px;">
        <p style="color: #64748b; font-size: 14px; text-align: center; margin: 0;">
            This email was sent to {{ $user->email }} because you created an account with {{ config('app.name') }}.
            <br>
            If you didn't create this account, please ignore this email or 
            <a href="mailto:security@{{ config('app.domain', 'freeligo.com') }}" style="color: #dc2626; text-decoration: none;">report it to our security team</a>.
        </p>
    </div>
@endsection
