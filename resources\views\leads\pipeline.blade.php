<x-app-layout>
    <div class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="md:flex md:items-center md:justify-between mb-6">
                <div class="flex-1 min-w-0">
                    <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                        Lead Pipeline
                    </h2>
                    <p class="mt-1 text-sm text-gray-500">
                        Drag and drop leads between stages to track progress
                    </p>
                </div>
                <div class="mt-4 flex md:mt-0 md:ml-4 space-x-3">
                    <a href="{{ route('leads.index') }}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <i class="fas fa-list mr-2"></i>
                        List View
                    </a>
                    <a href="{{ route('leads.create') }}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                        <i class="fas fa-plus mr-2"></i>
                        Add Lead
                    </a>
                </div>
            </div>

            <!-- Pipeline Stats -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                @php
                    $totalLeads = collect($pipelineData)->sum('count');
                    $totalValue = collect($pipelineData)->sum('total_value');
                @endphp
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-users text-gray-400 text-2xl"></i>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Total Leads</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ $totalLeads }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-dollar-sign text-green-400 text-2xl"></i>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Pipeline Value</dt>
                                    <dd class="text-lg font-medium text-gray-900">${{ number_format($totalValue, 0) }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                @php
                    $wonStage = collect($pipelineData)->firstWhere('stage.is_won_stage', true);
                    $wonCount = $wonStage ? $wonStage['count'] : 0;
                    $conversionRate = $totalLeads > 0 ? round(($wonCount / $totalLeads) * 100, 1) : 0;
                @endphp
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-chart-line text-blue-400 text-2xl"></i>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Conversion Rate</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ $conversionRate }}%</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-trophy text-yellow-400 text-2xl"></i>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Won Deals</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ $wonCount }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pipeline Board -->
            <div class="bg-white shadow rounded-lg overflow-hidden">
                <div class="p-6">
                    <div class="flex space-x-6 overflow-x-auto pb-4" id="pipeline-board">
                        @foreach($pipelineData as $stageData)
                            <div class="flex-shrink-0 w-80">
                                <!-- Stage Header -->
                                <div class="bg-gray-50 rounded-t-lg p-4 border-b">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-3 h-3 rounded-full mr-2" 
                                                 style="background-color: {{ $stageData['stage']->color }}"></div>
                                            <h3 class="text-sm font-medium text-gray-900">
                                                {{ $stageData['stage']->name }}
                                            </h3>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                {{ $stageData['count'] }}
                                            </span>
                                            @if($stageData['total_value'] > 0)
                                                <span class="text-xs text-gray-500">
                                                    ${{ number_format($stageData['total_value'], 0) }}
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                <!-- Stage Content -->
                                <div class="bg-gray-50 rounded-b-lg min-h-96 p-4 space-y-3" 
                                     data-stage-id="{{ $stageData['stage']->id }}"
                                     ondrop="drop(event)" 
                                     ondragover="allowDrop(event)">
                                    
                                    @foreach($stageData['leads'] as $lead)
                                        <div class="bg-white rounded-lg shadow-sm border p-4 cursor-move lead-card" 
                                             draggable="true" 
                                             ondragstart="drag(event)" 
                                             data-lead-id="{{ $lead->id }}">
                                            
                                            <!-- Lead Header -->
                                            <div class="flex items-start justify-between mb-2">
                                                <div class="flex-1">
                                                    <h4 class="text-sm font-medium text-gray-900 truncate">
                                                        {{ $lead->name }}
                                                    </h4>
                                                    @if($lead->company_name)
                                                        <p class="text-xs text-gray-500 truncate">
                                                            {{ $lead->company_name }}
                                                        </p>
                                                    @endif
                                                </div>
                                                <div class="flex-shrink-0 ml-2">
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                                                          style="background-color: {{ $lead->priority_color }}20; color: {{ $lead->priority_color }}">
                                                        {{ ucfirst($lead->priority) }}
                                                    </span>
                                                </div>
                                            </div>

                                            <!-- Lead Details -->
                                            <div class="space-y-2">
                                                @if($lead->estimated_value)
                                                    <div class="flex items-center text-xs text-gray-600">
                                                        <i class="fas fa-dollar-sign mr-1"></i>
                                                        ${{ number_format($lead->estimated_value, 0) }}
                                                    </div>
                                                @endif

                                                <div class="flex items-center text-xs text-gray-600">
                                                    <i class="fas fa-chart-line mr-1"></i>
                                                    Score: {{ $lead->lead_score }}
                                                </div>

                                                <div class="flex items-center text-xs text-gray-600">
                                                    <i class="fas fa-tag mr-1"></i>
                                                    {{ $lead->leadSource->name }}
                                                </div>

                                                @if($lead->next_follow_up_at)
                                                    <div class="flex items-center text-xs {{ $lead->isOverdueForFollowUp() ? 'text-red-600' : 'text-gray-600' }}">
                                                        <i class="fas fa-clock mr-1"></i>
                                                        {{ $lead->next_follow_up_at->format('M j') }}
                                                    </div>
                                                @endif
                                            </div>

                                            <!-- Lead Actions -->
                                            <div class="mt-3 flex items-center justify-between">
                                                <div class="flex items-center space-x-2">
                                                    @if($lead->email)
                                                        <a href="mailto:{{ $lead->email }}" 
                                                           class="text-gray-400 hover:text-gray-600">
                                                            <i class="fas fa-envelope text-xs"></i>
                                                        </a>
                                                    @endif
                                                    @if($lead->phone)
                                                        <a href="tel:{{ $lead->phone }}" 
                                                           class="text-gray-400 hover:text-gray-600">
                                                            <i class="fas fa-phone text-xs"></i>
                                                        </a>
                                                    @endif
                                                </div>
                                                <a href="{{ route('leads.show', $lead) }}" 
                                                   class="text-blue-600 hover:text-blue-800 text-xs">
                                                    View
                                                </a>
                                            </div>
                                        </div>
                                    @endforeach

                                    @if($stageData['leads']->isEmpty())
                                        <div class="text-center py-8 text-gray-400">
                                            <i class="fas fa-inbox text-2xl mb-2"></i>
                                            <p class="text-sm">No leads in this stage</p>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Drag and Drop JavaScript -->
    <script>
        function allowDrop(ev) {
            ev.preventDefault();
        }

        function drag(ev) {
            ev.dataTransfer.setData("text", ev.target.getAttribute('data-lead-id'));
        }

        function drop(ev) {
            ev.preventDefault();
            const leadId = ev.dataTransfer.getData("text");
            const stageId = ev.currentTarget.getAttribute('data-stage-id');
            
            // Move lead to new stage via AJAX
            fetch(`/leads/${leadId}/move-to-stage`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    stage_id: stageId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload the page to show updated pipeline
                    window.location.reload();
                } else {
                    alert('Error moving lead: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error moving lead. Please try again.');
            });
        }

        // Add visual feedback for drag and drop
        document.addEventListener('DOMContentLoaded', function() {
            const leadCards = document.querySelectorAll('.lead-card');
            
            leadCards.forEach(card => {
                card.addEventListener('dragstart', function() {
                    this.style.opacity = '0.5';
                });
                
                card.addEventListener('dragend', function() {
                    this.style.opacity = '1';
                });
            });
        });
    </script>
</x-app-layout>
