<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Webhook Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration for webhook handling in the Freeligo
    | application, including payment gateway webhooks and security settings.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Webhook Security
    |--------------------------------------------------------------------------
    */
    'security' => [
        'verify_signatures' => env('WEBHOOK_VERIFY_SIGNATURES', true),
        'allowed_ips' => [
            'paypal' => [
                '************/25',
                '************/25',
                '************/25',
                '************/25',
            ],
            'razorpay' => [
                '***********',
                '***********',
                '*************',
            ],
        ],
        'timeout' => env('WEBHOOK_TIMEOUT', 30),
        'max_retries' => env('WEBHOOK_MAX_RETRIES', 3),
    ],

    /*
    |--------------------------------------------------------------------------
    | PayPal Webhook Configuration
    |--------------------------------------------------------------------------
    */
    'paypal' => [
        'webhook_id' => env('PAYPAL_WEBHOOK_ID'),
        'verify_ssl' => env('PAYPAL_WEBHOOK_VERIFY_SSL', true),
        'events' => [
            'BILLING.SUBSCRIPTION.ACTIVATED',
            'BILLING.SUBSCRIPTION.CANCELLED',
            'BILLING.SUBSCRIPTION.SUSPENDED',
            'BILLING.SUBSCRIPTION.PAYMENT.FAILED',
            'PAYMENT.SALE.COMPLETED',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Razorpay Webhook Configuration
    |--------------------------------------------------------------------------
    */
    'razorpay' => [
        'webhook_secret' => env('RAZORPAY_WEBHOOK_SECRET'),
        'verify_ssl' => env('RAZORPAY_WEBHOOK_VERIFY_SSL', true),
        'events' => [
            'payment.captured',
            'payment.failed',
            'subscription.activated',
            'subscription.cancelled',
            'subscription.charged',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Webhook Logging
    |--------------------------------------------------------------------------
    */
    'logging' => [
        'enabled' => env('WEBHOOK_LOGGING_ENABLED', true),
        'log_channel' => env('WEBHOOK_LOG_CHANNEL', 'daily'),
        'log_failed_only' => env('WEBHOOK_LOG_FAILED_ONLY', false),
        'retention_days' => env('WEBHOOK_LOG_RETENTION_DAYS', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    */
    'rate_limiting' => [
        'enabled' => env('WEBHOOK_RATE_LIMITING_ENABLED', true),
        'max_attempts' => env('WEBHOOK_RATE_LIMIT_ATTEMPTS', 100),
        'decay_minutes' => env('WEBHOOK_RATE_LIMIT_DECAY', 1),
    ],

];
