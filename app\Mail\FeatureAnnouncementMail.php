<?php

namespace App\Mail;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class FeatureAnnouncementMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public User $user;
    public array $feature;

    /**
     * Create a new message instance.
     */
    public function __construct(User $user, array $feature)
    {
        $this->user = $user;
        $this->feature = $feature;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: "🚀 New Feature: " . ($this->feature['title'] ?? 'Exciting Update') . " - " . config('app.name'),
            to: [$this->user->email],
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.feature-announcement',
            with: [
                'user' => $this->user,
                'feature' => $this->feature,
                'header_title' => 'New Feature Available!',
                'header_subtitle' => 'We\'ve added something amazing to help you grow your business',
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
