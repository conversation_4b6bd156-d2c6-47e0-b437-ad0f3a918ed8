<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Proposal extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'client_id',
        'lead_id',
        'proposal_template_id',
        'title',
        'description',
        'content',
        'sections',
        'status',
        'total_amount',
        'currency',
        'valid_until',
        'sent_date',
        'viewed_date',
        'responded_date',
        'client_notes',
        'internal_notes',
        'custom_fields',
        'pdf_path',
        'proposal_number',
        'version',
        'parent_proposal_id',
        'is_template',
    ];

    protected function casts(): array
    {
        return [
            'sections' => 'array',
            'custom_fields' => 'array',
            'total_amount' => 'decimal:2',
            'valid_until' => 'date',
            'sent_date' => 'date',
            'viewed_date' => 'date',
            'responded_date' => 'date',
            'is_template' => 'boolean',
            'version' => 'integer',
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($proposal) {
            if (empty($proposal->proposal_number)) {
                $proposal->proposal_number = static::generateProposalNumber();
            }
            if (empty($proposal->currency)) {
                $proposal->currency = config('services.currency.code', 'USD');
            }
        });
    }

    /**
     * Generate unique proposal number.
     */
    public static function generateProposalNumber(): string
    {
        do {
            $number = 'PROP-' . date('Y') . '-' . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        } while (static::where('proposal_number', $number)->exists());

        return $number;
    }

    /**
     * Get the user that owns the proposal.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the client that owns the proposal.
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Get the lead that this proposal was created for (optional).
     */
    public function lead(): BelongsTo
    {
        return $this->belongsTo(Lead::class);
    }

    /**
     * Get the proposal template.
     */
    public function proposalTemplate(): BelongsTo
    {
        return $this->belongsTo(ProposalTemplate::class);
    }

    /**
     * Get the parent proposal (for revisions).
     */
    public function parentProposal(): BelongsTo
    {
        return $this->belongsTo(Proposal::class, 'parent_proposal_id');
    }

    /**
     * Get proposal revisions.
     */
    public function revisions(): HasMany
    {
        return $this->hasMany(Proposal::class, 'parent_proposal_id');
    }

    /**
     * Get projects created from this proposal.
     */
    public function projects(): HasMany
    {
        return $this->hasMany(Project::class);
    }

    /**
     * Get contracts created from this proposal.
     */
    public function contracts(): HasMany
    {
        return $this->hasMany(Contract::class);
    }

    /**
     * Scope a query to only include active proposals.
     */
    public function scopeActive($query)
    {
        return $query->where('status', '!=', 'expired');
    }

    /**
     * Scope a query to only include sent proposals.
     */
    public function scopeSent($query)
    {
        return $query->whereIn('status', ['sent', 'viewed', 'accepted', 'rejected']);
    }

    /**
     * Scope a query to only include pending proposals.
     */
    public function scopePending($query)
    {
        return $query->whereIn('status', ['sent', 'viewed']);
    }

    /**
     * Scope for optimized listing queries.
     */
    public function scopeForListing($query)
    {
        return $query->select([
            'id', 'user_id', 'client_id', 'title', 'status', 
            'total_amount', 'currency', 'sent_date', 'valid_until', 
            'proposal_number', 'created_at'
        ])->with('client:id,name,company_name');
    }

    /**
     * Check if proposal is expired.
     */
    public function isExpired(): bool
    {
        return $this->valid_until && $this->valid_until->isPast() && $this->status !== 'accepted';
    }

    /**
     * Check if proposal can be edited.
     */
    public function canBeEdited(): bool
    {
        return in_array($this->status, ['draft']);
    }

    /**
     * Check if proposal can be sent.
     */
    public function canBeSent(): bool
    {
        return $this->status === 'draft' && $this->client_id;
    }

    /**
     * Check if proposal can be converted.
     */
    public function canBeConverted(): bool
    {
        return $this->status === 'accepted';
    }

    /**
     * Get formatted total amount.
     */
    public function getFormattedTotalAttribute(): string
    {
        if (!$this->total_amount) {
            return 'N/A';
        }

        $symbol = $this->currency === 'USD' ? '$' : '₹';
        return $symbol . number_format($this->total_amount, 2);
    }

    /**
     * Get status badge color.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'draft' => 'gray',
            'sent' => 'blue',
            'viewed' => 'yellow',
            'accepted' => 'green',
            'rejected' => 'red',
            'expired' => 'gray',
            default => 'gray'
        };
    }

    /**
     * Get days until expiry.
     */
    public function getDaysUntilExpiryAttribute(): ?int
    {
        if (!$this->valid_until) {
            return null;
        }

        return now()->diffInDays($this->valid_until, false);
    }
}
