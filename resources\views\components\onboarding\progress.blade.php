@props([
    'steps' => [],
    'currentStep' => 1,
    'variant' => 'default'
])

@php
    $totalSteps = count($steps);
    $progressPercentage = (($currentStep - 1) / max($totalSteps - 1, 1)) * 100;
@endphp

<div class="w-full {{ $attributes->get('class') }}">
    <!-- Progress Bar -->
    <div class="mb-8">
        <div class="flex items-center justify-between mb-4">
            <span class="text-sm font-medium text-secondary-600">Step {{ $currentStep }} of {{ $totalSteps }}</span>
            <span class="text-sm font-medium text-secondary-600">{{ round($progressPercentage) }}% Complete</span>
        </div>
        <div class="w-full bg-secondary-200 rounded-full h-2">
            <div class="bg-gradient-to-r from-primary-500 to-primary-600 h-2 rounded-full transition-all duration-500 ease-out" 
                 style="width: {{ $progressPercentage }}%"></div>
        </div>
    </div>

    <!-- Steps -->
    <div class="flex items-center justify-between">
        @foreach($steps as $index => $step)
            @php
                $stepNumber = $index + 1;
                $isCompleted = $stepNumber < $currentStep;
                $isCurrent = $stepNumber === $currentStep;
                $isUpcoming = $stepNumber > $currentStep;
            @endphp
            
            <div class="flex flex-col items-center {{ $index < count($steps) - 1 ? 'flex-1' : '' }}">
                <!-- Step Circle -->
                <div class="relative">
                    <div class="w-12 h-12 rounded-full flex items-center justify-center border-2 transition-all duration-300
                        @if($isCompleted)
                            bg-primary-600 border-primary-600 text-white
                        @elseif($isCurrent)
                            bg-white border-primary-600 text-primary-600 shadow-glow
                        @else
                            bg-white border-secondary-300 text-secondary-400
                        @endif
                    ">
                        @if($isCompleted)
                            <i class="fas fa-check text-sm"></i>
                        @else
                            <span class="text-sm font-semibold">{{ $stepNumber }}</span>
                        @endif
                    </div>
                    
                    @if($isCurrent)
                        <div class="absolute -inset-1 bg-primary-600 rounded-full opacity-20 animate-pulse"></div>
                    @endif
                </div>
                
                <!-- Step Label -->
                <div class="mt-3 text-center">
                    <p class="text-sm font-medium 
                        @if($isCompleted || $isCurrent)
                            text-secondary-900
                        @else
                            text-secondary-500
                        @endif
                    ">
                        {{ $step['title'] }}
                    </p>
                    @if(isset($step['description']))
                        <p class="text-xs text-secondary-500 mt-1 max-w-24">{{ $step['description'] }}</p>
                    @endif
                </div>
            </div>
            
            <!-- Connector Line -->
            @if($index < count($steps) - 1)
                <div class="flex-1 h-0.5 mx-4 mb-8
                    @if($stepNumber < $currentStep)
                        bg-primary-600
                    @else
                        bg-secondary-200
                    @endif
                "></div>
            @endif
        @endforeach
    </div>
</div>
