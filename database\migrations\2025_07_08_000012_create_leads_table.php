<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('leads', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('lead_source_id')->constrained()->onDelete('cascade');
            $table->foreignId('lead_stage_id')->constrained()->onDelete('cascade');
            $table->foreignId('converted_client_id')->nullable()->constrained('clients')->onDelete('set null');
            
            // Lead basic information
            $table->string('name');
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('company_name')->nullable();
            $table->string('title')->nullable(); // Job title
            $table->text('address')->nullable();
            
            // Lead qualification data
            $table->decimal('estimated_value', 12, 2)->nullable(); // Potential project value
            $table->integer('lead_score')->default(0); // 0-100 scoring system
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->enum('status', ['active', 'converted', 'lost', 'archived'])->default('active');
            
            // Conversion tracking
            $table->timestamp('converted_at')->nullable();
            $table->string('lost_reason')->nullable();
            $table->text('notes')->nullable();
            
            // Engagement tracking
            $table->timestamp('last_contacted_at')->nullable();
            $table->timestamp('next_follow_up_at')->nullable();
            $table->integer('contact_attempts')->default(0);
            
            // Custom fields for flexibility
            $table->json('custom_fields')->nullable();
            
            $table->timestamps();

            // Indexes for performance
            $table->index(['user_id', 'status']);
            $table->index(['lead_stage_id', 'status']);
            $table->index(['lead_source_id', 'status']);
            $table->index(['email', 'phone']);
            $table->index(['priority', 'status']);
            $table->index(['next_follow_up_at', 'status']);
            $table->index(['converted_at', 'status']);
            $table->index(['created_at', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('leads');
    }
};
