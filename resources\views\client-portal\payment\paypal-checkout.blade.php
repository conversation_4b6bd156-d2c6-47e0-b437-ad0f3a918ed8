@extends('client-portal.layout')

@section('content')
<div class="max-w-2xl mx-auto">
    <div class="text-center">
        <!-- PayPal Logo -->
        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 mb-6">
            <i class="fab fa-paypal text-blue-600 text-2xl"></i>
        </div>

        <!-- Processing Message -->
        <h2 class="text-3xl font-bold text-gray-900 mb-2">Redirecting to PayPal</h2>
        <p class="text-lg text-gray-600 mb-8">
            Please wait while we redirect you to PayPal to complete your payment.
        </p>

        <!-- Payment Summary -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8 text-left">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Payment Summary</h3>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-gray-600">Invoice Number:</span>
                    <span class="font-medium text-gray-900">{{ $invoice->invoice_number }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Payment Amount:</span>
                    <span class="font-medium text-gray-900">{{ $payment->formatted_amount }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Payment Method:</span>
                    <span class="font-medium text-gray-900">PayPal</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Transaction ID:</span>
                    <span class="font-medium text-gray-900">{{ $payment->payment_id }}</span>
                </div>
            </div>
        </div>

        <!-- PayPal Button Container -->
        <div id="paypal-button-container" class="mb-8"></div>

        <!-- Loading State -->
        <div id="loading-state" class="hidden">
            <div class="flex items-center justify-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span class="text-gray-600">Processing payment...</span>
            </div>
        </div>

        <!-- Manual Redirect Button (fallback) -->
        <div id="manual-redirect" class="hidden">
            <p class="text-gray-600 mb-4">If you're not automatically redirected, click the button below:</p>
            <a href="{{ $orderData['links'][1]['href'] ?? '#' }}" 
               class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <i class="fab fa-paypal mr-2"></i>
                Continue to PayPal
            </a>
        </div>

        <!-- Cancel Button -->
        <div class="mt-6">
            <a href="{{ route('client-portal.invoice.payment', ['invoice' => $invoice->id, 'token' => $invoice->payment_token]) }}" 
               class="text-gray-500 hover:text-gray-700 text-sm">
                <i class="fas fa-arrow-left mr-1"></i>
                Cancel and go back
            </a>
        </div>
    </div>
</div>

@push('scripts')
<!-- PayPal SDK -->
<script src="https://www.paypal.com/sdk/js?client-id={{ config('services.paypal.client_id') }}&currency={{ $payment->currency }}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // PayPal Buttons
    paypal.Buttons({
        createOrder: function(data, actions) {
            return '{{ $orderData['id'] }}';
        },
        onApprove: function(data, actions) {
            document.getElementById('loading-state').classList.remove('hidden');
            document.getElementById('paypal-button-container').classList.add('hidden');
            
            return actions.order.capture().then(function(details) {
                // Redirect to success page
                window.location.href = '{{ route("client-portal.payment.success", ["invoice" => $invoice->id, "token" => $invoice->payment_token]) }}' + 
                    '?payment_id={{ $payment->payment_id }}&paypal_order_id=' + data.orderID;
            });
        },
        onError: function(err) {
            console.error('PayPal error:', err);
            // Redirect to failure page
            window.location.href = '{{ route("client-portal.payment.failure", ["invoice" => $invoice->id, "token" => $invoice->payment_token]) }}' + 
                '?payment_id={{ $payment->payment_id }}&reason=PayPal payment failed';
        },
        onCancel: function(data) {
            // Redirect to failure page
            window.location.href = '{{ route("client-portal.payment.failure", ["invoice" => $invoice->id, "token" => $invoice->payment_token]) }}' + 
                '?payment_id={{ $payment->payment_id }}&reason=Payment cancelled by user';
        }
    }).render('#paypal-button-container');

    // Fallback: Show manual redirect after 10 seconds
    setTimeout(function() {
        if (!document.getElementById('loading-state').classList.contains('hidden')) {
            return; // Payment is already processing
        }
        document.getElementById('manual-redirect').classList.remove('hidden');
    }, 10000);
});
</script>
@endpush
@endsection
