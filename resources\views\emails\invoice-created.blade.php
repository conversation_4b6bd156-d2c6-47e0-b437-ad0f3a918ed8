<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Invoice - {{ $invoice->invoice_number }}</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #374151;
            background-color: #f9fafb;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
            padding: 32px 24px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
        }
        .header p {
            margin: 8px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        .content {
            padding: 32px 24px;
        }
        .invoice-details {
            background-color: #f3f4f6;
            border-radius: 8px;
            padding: 24px;
            margin: 24px 0;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e5e7eb;
        }
        .detail-row:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }
        .detail-label {
            font-weight: 600;
            color: #6b7280;
        }
        .detail-value {
            font-weight: 600;
            color: #111827;
        }
        .amount {
            font-size: 24px;
            font-weight: 700;
            color: #059669;
        }
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            margin: 24px 0;
            transition: transform 0.2s;
        }
        .cta-button:hover {
            transform: translateY(-1px);
        }
        .secondary-button {
            display: inline-block;
            background-color: #f3f4f6;
            color: #374151;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;
            margin-left: 12px;
        }
        .footer {
            background-color: #f9fafb;
            padding: 24px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }
        .footer p {
            margin: 0;
            font-size: 14px;
            color: #6b7280;
        }
        .contact-info {
            background-color: #eff6ff;
            border-left: 4px solid #3b82f6;
            padding: 16px;
            margin: 24px 0;
        }
        .contact-info h3 {
            margin: 0 0 8px 0;
            color: #1e40af;
            font-size: 16px;
        }
        @media (max-width: 600px) {
            .container {
                margin: 0;
                border-radius: 0;
            }
            .content {
                padding: 24px 16px;
            }
            .header {
                padding: 24px 16px;
            }
            .cta-button {
                display: block;
                margin: 24px 0;
            }
            .secondary-button {
                display: block;
                margin: 12px 0 0 0;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>New Invoice</h1>
            <p>You have received a new invoice from {{ $freelancer->business_name ?: $freelancer->name }}</p>
        </div>

        <!-- Content -->
        <div class="content">
            <p>Hello {{ $client->name }},</p>
            
            <p>We hope this email finds you well. We have prepared a new invoice for the services provided. Please find the details below:</p>

            <!-- Invoice Details -->
            <div class="invoice-details">
                <div class="detail-row">
                    <span class="detail-label">Invoice Number:</span>
                    <span class="detail-value">{{ $invoice->invoice_number }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Issue Date:</span>
                    <span class="detail-value">{{ $invoice->invoice_date->format('M d, Y') }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Due Date:</span>
                    <span class="detail-value">{{ $invoice->due_date->format('M d, Y') }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Total Amount:</span>
                    <span class="detail-value amount">{{ config('services.currency.symbol', '₹') }}{{ number_format($invoice->total_amount, 2) }}</span>
                </div>
                @if($invoice->tds_amount > 0)
                    <div class="detail-row">
                        <span class="detail-label">Net Amount (After TDS):</span>
                        <span class="detail-value amount">{{ config('services.currency.symbol', '₹') }}{{ number_format($invoice->net_amount, 2) }}</span>
                    </div>
                @endif
            </div>

            <!-- Call to Action -->
            <div style="text-align: center;">
                <a href="{{ $invoice->public_url }}" class="cta-button">
                    📄 View Invoice Details
                </a>
                @if($invoice->status !== 'paid')
                    <a href="{{ $invoice->payment_url }}" class="cta-button" style="background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);">
                        💳 Make Payment
                    </a>
                @endif
            </div>

            <!-- Contact Information -->
            <div class="contact-info">
                <h3>Questions? Contact Us</h3>
                <p><strong>{{ $freelancer->business_name ?: $freelancer->name }}</strong></p>
                <p>📧 {{ $freelancer->email }}</p>
                @if($freelancer->phone)
                    <p>📞 {{ $freelancer->phone }}</p>
                @endif
                @if($freelancer->address)
                    <p>📍 {{ $freelancer->address }}</p>
                @endif
            </div>

            @if($invoice->notes)
                <div style="background-color: #fef3c7; border-left: 4px solid #f59e0b; padding: 16px; margin: 24px 0;">
                    <h3 style="margin: 0 0 8px 0; color: #92400e; font-size: 16px;">Additional Notes</h3>
                    <p style="margin: 0; color: #92400e;">{{ $invoice->notes }}</p>
                </div>
            @endif

            <p>Thank you for your business. We appreciate the opportunity to work with you.</p>
            
            <p>Best regards,<br>
            <strong>{{ $freelancer->business_name ?: $freelancer->name }}</strong></p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>This is an automated email from {{ config('app.name') }}. Please do not reply to this email.</p>
            <p style="margin-top: 8px;">
                <a href="{{ $invoice->public_url }}" style="color: #059669; text-decoration: none;">View Invoice Online</a>
                @if($invoice->status !== 'paid')
                    | <a href="{{ $invoice->payment_url }}" style="color: #dc2626; text-decoration: none;">Make Payment</a>
                @endif
            </p>
        </div>
    </div>
</body>
</html>
