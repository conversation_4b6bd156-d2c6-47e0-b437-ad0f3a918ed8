<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if we're using MySQL or SQLite
        $driver = DB::getDriverName();

        if ($driver === 'mysql') {
            // For MySQL, we can use raw SQL to modify enum values
            DB::statement("ALTER TABLE projects MODIFY COLUMN status ENUM('planning', 'active', 'completed', 'on_hold', 'cancelled') DEFAULT 'planning'");
        } else {
            // For SQLite and other databases, we need to handle this differently
            // SQLite doesn't support dropping columns easily, so we'll just add the new value if needed
            // Check if 'planning' status already exists in the data
            $hasPlanning = DB::table('projects')->where('status', 'planning')->exists();

            if (!$hasPlanning) {
                // If no projects have 'planning' status, we can safely assume the migration is needed
                // For SQLite, we'll just ensure the column accepts the new value
                // The enum constraint is not enforced in SQLite anyway
                DB::statement("UPDATE projects SET status = 'planning' WHERE status NOT IN ('active', 'completed', 'on_hold', 'cancelled')");
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $driver = DB::getDriverName();

        if ($driver === 'mysql') {
            // Revert back to original enum values
            DB::statement("ALTER TABLE projects MODIFY COLUMN status ENUM('active', 'completed', 'on_hold', 'cancelled') DEFAULT 'active'");
        } else {
            // For SQLite, just update planning status back to active
            DB::statement("UPDATE projects SET status = 'active' WHERE status = 'planning'");
        }
    }
};
