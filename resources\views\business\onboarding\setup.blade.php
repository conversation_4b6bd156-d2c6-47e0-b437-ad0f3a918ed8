<x-app-layout>
    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <!-- Header -->
                    <div class="mb-8">
                        <h1 class="text-3xl font-bold text-gray-900">Complete Your Business Setup</h1>
                        <p class="mt-2 text-gray-600">Let's finish setting up your business profile to get you started.</p>
                    </div>

                    <!-- Business Info Card -->
                    <div class="bg-gray-50 rounded-lg p-6 mb-8">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">{{ $business->name }}</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <span class="text-sm font-medium text-gray-500">Business Type:</span>
                                <p class="text-gray-900 capitalize">{{ str_replace('_', ' ', $business->business_type) }}</p>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Industry:</span>
                                <p class="text-gray-900">{{ $business->industry ?? 'Not specified' }}</p>
                            </div>
                            @if($business->website)
                            <div>
                                <span class="text-sm font-medium text-gray-500">Website:</span>
                                <p class="text-gray-900">{{ $business->website }}</p>
                            </div>
                            @endif
                            @if($business->founded_year)
                            <div>
                                <span class="text-sm font-medium text-gray-500">Founded:</span>
                                <p class="text-gray-900">{{ $business->founded_year }}</p>
                            </div>
                            @endif
                        </div>
                        @if($business->description)
                        <div class="mt-4">
                            <span class="text-sm font-medium text-gray-500">Description:</span>
                            <p class="text-gray-900 mt-1">{{ $business->description }}</p>
                        </div>
                        @endif
                    </div>

                    <!-- Setup Form -->
                    <form method="POST" action="{{ route('business.onboarding.setup.update', $business) }}" class="space-y-8">
                        @csrf
                        @method('PUT')

                        <!-- Business Settings Section -->
                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Business Settings</h3>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Currency -->
                                <div>
                                    <label for="currency" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-dollar-sign mr-2"></i>Currency
                                    </label>
                                    <select name="currency" id="currency" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option value="USD" {{ old('currency', 'USD') == 'USD' ? 'selected' : '' }}>USD - US Dollar</option>
                                        <option value="INR" {{ old('currency', 'USD') == 'INR' ? 'selected' : '' }}>INR - Indian Rupee</option>
                                        <option value="EUR" {{ old('currency', 'USD') == 'EUR' ? 'selected' : '' }}>EUR - Euro</option>
                                        <option value="GBP" {{ old('currency', 'USD') == 'GBP' ? 'selected' : '' }}>GBP - British Pound</option>
                                    </select>
                                </div>

                                <!-- Timezone -->
                                <div>
                                    <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-clock mr-2"></i>Timezone
                                    </label>
                                    <select name="timezone" id="timezone" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option value="UTC" {{ old('timezone', 'UTC') == 'UTC' ? 'selected' : '' }}>UTC</option>
                                        <option value="America/New_York" {{ old('timezone', 'UTC') == 'America/New_York' ? 'selected' : '' }}>Eastern Time</option>
                                        <option value="America/Chicago" {{ old('timezone', 'UTC') == 'America/Chicago' ? 'selected' : '' }}>Central Time</option>
                                        <option value="America/Denver" {{ old('timezone', 'UTC') == 'America/Denver' ? 'selected' : '' }}>Mountain Time</option>
                                        <option value="America/Los_Angeles" {{ old('timezone', 'UTC') == 'America/Los_Angeles' ? 'selected' : '' }}>Pacific Time</option>
                                        <option value="Asia/Kolkata" {{ old('timezone', 'UTC') == 'Asia/Kolkata' ? 'selected' : '' }}>India Standard Time</option>
                                    </select>
                                </div>

                                <!-- Invoice Prefix -->
                                <div>
                                    <label for="invoice_prefix" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-hashtag mr-2"></i>Invoice Prefix
                                    </label>
                                    <input type="text" name="invoice_prefix" id="invoice_prefix"
                                           value="{{ old('invoice_prefix', 'INV') }}"
                                           placeholder="INV" maxlength="10"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <!-- Payment Terms -->
                                <div>
                                    <label for="payment_terms" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-calendar mr-2"></i>Payment Terms (Days)
                                    </label>
                                    <input type="number" name="payment_terms" id="payment_terms"
                                           value="{{ old('payment_terms', 30) }}"
                                           placeholder="30" min="1" max="365"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                            </div>
                        </div>

                        <!-- Branding Section -->
                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Branding (Optional)</h3>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Logo Upload -->
                                <div>
                                    <label for="logo" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-image mr-2"></i>Business Logo
                                    </label>
                                    <input type="file" name="logo" id="logo" accept="image/*"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <p class="text-sm text-gray-500 mt-1">Upload your business logo (max 2MB)</p>
                                </div>

                                <!-- Primary Color -->
                                <div>
                                    <label for="primary_color" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-palette mr-2"></i>Primary Color
                                    </label>
                                    <input type="color" name="primary_color" id="primary_color"
                                           value="{{ old('primary_color', '#10b981') }}"
                                           class="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                            <a href="{{ route('dashboard') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <i class="fas fa-arrow-left mr-2"></i>
                                Skip for Now
                            </a>

                            <button type="submit" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <i class="fas fa-check mr-2"></i>
                                Complete Setup
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
