/* FontAwesome Icons */
@import '@fortawesome/fontawesome-free/css/all.css';

/* Toastify Styles */
@import 'toastify-js/src/toastify.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Professional Typography & Base Styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply text-secondary-700 leading-relaxed;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-heading font-semibold text-secondary-900 tracking-tight;
  }

  h1 {
    @apply text-3xl lg:text-4xl leading-tight;
  }

  h2 {
    @apply text-2xl lg:text-3xl leading-tight;
  }

  h3 {
    @apply text-xl lg:text-2xl leading-snug;
  }

  h4 {
    @apply text-lg lg:text-xl leading-snug;
  }

  h5 {
    @apply text-base lg:text-lg leading-normal;
  }

  h6 {
    @apply text-sm lg:text-base leading-normal;
  }

  p {
    @apply leading-relaxed;
  }

  /* Focus styles */
  *:focus {
    @apply outline-none;
  }

  *:focus-visible {
    @apply ring-2 ring-primary-500 ring-offset-2 ring-offset-white;
  }
}

/* Professional Component Styles */
@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 shadow-sm hover:shadow-md;
  }

  .btn-secondary {
    @apply bg-secondary-100 text-secondary-700 hover:bg-secondary-200 focus:ring-secondary-500 border border-secondary-200;
  }

  .btn-outline {
    @apply bg-transparent text-primary-600 hover:bg-primary-50 focus:ring-primary-500 border border-primary-300 hover:border-primary-400;
  }

  .btn-ghost {
    @apply bg-transparent text-secondary-600 hover:bg-secondary-100 focus:ring-secondary-500 hover:text-secondary-700;
  }

  /* Card styles */
  .card {
    @apply bg-white rounded-2xl border border-secondary-200 shadow-card hover:shadow-card-hover transition-all duration-200;
  }

  .card-header {
    @apply px-6 py-4 border-b border-secondary-100 bg-secondary-50/50;
  }

  .card-body {
    @apply p-6;
  }

  /* Form styles */
  .form-input {
    @apply block w-full rounded-xl border-secondary-300 shadow-sm transition-colors duration-200 focus:border-primary-500 focus:ring-primary-500 placeholder:text-secondary-400;
  }

  .form-input-error {
    @apply border-danger-300 focus:border-danger-500 focus:ring-danger-500;
  }

  .form-label {
    @apply block text-sm font-medium text-secondary-700 mb-2;
  }

  .form-error {
    @apply text-sm text-danger-600 mt-1;
  }

  .form-hint {
    @apply text-sm text-secondary-500 mt-1;
  }

  /* Navigation styles */
  .nav-link {
    @apply flex items-center px-3 py-2 rounded-lg transition-all duration-200;
  }

  .nav-link-active {
    @apply text-primary-600 bg-primary-50 border-r-2 border-primary-600;
  }

  .nav-link-inactive {
    @apply text-secondary-600 hover:text-primary-600 hover:bg-primary-50;
  }

  /* Badge styles */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-primary-100 text-primary-800;
  }

  .badge-secondary {
    @apply bg-secondary-100 text-secondary-800;
  }

  .badge-success {
    @apply bg-success-100 text-success-800;
  }

  .badge-warning {
    @apply bg-warning-100 text-warning-800;
  }

  .badge-danger {
    @apply bg-danger-100 text-danger-800;
  }
}

/* Professional Utility Classes */
@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-primary-700 bg-clip-text text-transparent;
  }

  .bg-gradient-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-700;
  }

  .bg-gradient-secondary {
    @apply bg-gradient-to-r from-secondary-600 to-secondary-700;
  }

  .glass {
    @apply bg-white/80 backdrop-blur-sm border border-white/20;
  }

  .glass-dark {
    @apply bg-secondary-900/80 backdrop-blur-sm border border-secondary-700/20;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: theme('colors.secondary.300') theme('colors.secondary.100');
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    @apply bg-secondary-100;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply bg-secondary-300 rounded-full;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    @apply bg-secondary-400;
  }

  /* Touch-friendly interactions */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  /* Responsive text sizing */
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl;
  }

  /* Responsive spacing */
  .space-responsive {
    @apply space-y-4 sm:space-y-6;
  }

  .gap-responsive {
    @apply gap-4 sm:gap-6;
  }

  .p-responsive {
    @apply p-4 sm:p-6;
  }

  .px-responsive {
    @apply px-4 sm:px-6;
  }

  .py-responsive {
    @apply py-4 sm:py-6;
  }

  /* Ripple effect */
  .ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
  }

  @keyframes ripple-animation {
    to {
      transform: scale(4);
      opacity: 0;
    }
  }

  /* Button positioning for ripple */
  .btn {
    position: relative;
    overflow: hidden;
  }

  /* Enhanced hover states */
  .hover-lift {
    transition: transform 0.2s ease-out;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
  }

  /* Smooth transitions for all interactive elements */
  .interactive {
    transition: all 0.2s ease-out;
  }

  .interactive:hover {
    transform: scale(1.02);
  }

  /* Focus states for accessibility */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
}

/* Custom Utilities */
@layer utilities {
    .shadow-soft {
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    }

    .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }

    .scrollbar-hide::-webkit-scrollbar {
        display: none;
    }

    /* Animation utilities */
    .animate-fadeIn {
        animation: fadeIn 0.5s ease-in-out;
    }

    .animate-slideUp {
        animation: slideUp 0.3s ease-out;
    }

    .animate-slideDown {
        animation: slideDown 0.3s ease-out;
    }

    .animate-scaleIn {
        animation: scaleIn 0.2s ease-out;
    }

    .animate-pulse-soft {
        animation: pulseSoft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }
}

/* Keyframe animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulseSoft {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

/* Smooth transitions for all elements */
* {
    @apply transition-colors duration-200;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
}
