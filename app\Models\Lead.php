<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Lead extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'lead_source_id',
        'lead_stage_id',
        'converted_client_id',
        'name',
        'email',
        'phone',
        'company_name',
        'title',
        'address',
        'estimated_value',
        'lead_score',
        'priority',
        'status',
        'converted_at',
        'lost_reason',
        'notes',
        'last_contacted_at',
        'next_follow_up_at',
        'contact_attempts',
        'custom_fields',
    ];

    protected function casts(): array
    {
        return [
            'estimated_value' => 'decimal:2',
            'lead_score' => 'integer',
            'contact_attempts' => 'integer',
            'converted_at' => 'datetime',
            'last_contacted_at' => 'datetime',
            'next_follow_up_at' => 'datetime',
            'custom_fields' => 'array',
        ];
    }

    /**
     * Get the user that owns the lead.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the lead source.
     */
    public function leadSource(): BelongsTo
    {
        return $this->belongsTo(LeadSource::class);
    }

    /**
     * Get the lead stage.
     */
    public function leadStage(): BelongsTo
    {
        return $this->belongsTo(LeadStage::class);
    }

    /**
     * Get the converted client.
     */
    public function convertedClient(): BelongsTo
    {
        return $this->belongsTo(Client::class, 'converted_client_id');
    }

    /**
     * Get the lead activities.
     */
    public function activities(): HasMany
    {
        return $this->hasMany(LeadActivity::class);
    }

    /**
     * Get the lead notes.
     */
    public function notes(): HasMany
    {
        return $this->hasMany(LeadNote::class);
    }

    /**
     * Get the proposals for this lead.
     */
    public function proposals(): HasMany
    {
        return $this->hasMany(Proposal::class);
    }

    /**
     * Get the proposals for this lead after conversion to client.
     */
    public function clientProposals(): HasMany
    {
        return $this->hasMany(Proposal::class, 'client_id', 'converted_client_id');
    }

    /**
     * Scope a query to only include user's leads.
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope a query to only include active leads.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include converted leads.
     */
    public function scopeConverted($query)
    {
        return $query->where('status', 'converted');
    }

    /**
     * Scope a query to only include lost leads.
     */
    public function scopeLost($query)
    {
        return $query->where('status', 'lost');
    }

    /**
     * Scope a query for leads needing follow-up.
     */
    public function scopeNeedsFollowUp($query)
    {
        return $query->where('next_follow_up_at', '<=', now())
                    ->where('status', 'active');
    }

    /**
     * Scope a query by priority.
     */
    public function scopeByPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope a query by stage.
     */
    public function scopeInStage($query, int $stageId)
    {
        return $query->where('lead_stage_id', $stageId);
    }

    /**
     * Get the display name for the lead.
     */
    public function getDisplayNameAttribute(): string
    {
        if ($this->company_name) {
            return $this->name . ' (' . $this->company_name . ')';
        }
        return $this->name;
    }

    /**
     * Get the priority color.
     */
    public function getPriorityColorAttribute(): string
    {
        return match($this->priority) {
            'urgent' => '#EF4444',
            'high' => '#F59E0B',
            'medium' => '#3B82F6',
            'low' => '#6B7280',
            default => '#6B7280',
        };
    }

    /**
     * Check if lead is overdue for follow-up.
     */
    public function isOverdueForFollowUp(): bool
    {
        return $this->next_follow_up_at && 
               $this->next_follow_up_at->isPast() && 
               $this->status === 'active';
    }

    /**
     * Calculate lead score based on various factors.
     */
    public function calculateLeadScore(): int
    {
        $score = 0;

        // Base score for having contact information
        if ($this->email) $score += 10;
        if ($this->phone) $score += 10;
        if ($this->company_name) $score += 15;

        // Score based on estimated value
        if ($this->estimated_value) {
            if ($this->estimated_value >= 10000) $score += 25;
            elseif ($this->estimated_value >= 5000) $score += 20;
            elseif ($this->estimated_value >= 1000) $score += 15;
            else $score += 10;
        }

        // Score based on engagement (activities)
        $recentActivities = $this->activities()
            ->where('created_at', '>=', now()->subDays(30))
            ->count();
        $score += min($recentActivities * 5, 25);

        // Score based on stage probability
        $score += ($this->leadStage->conversion_probability ?? 0) * 0.3;

        return min($score, 100);
    }

    /**
     * Update lead score.
     */
    public function updateLeadScore(): void
    {
        $this->update(['lead_score' => $this->calculateLeadScore()]);
    }

    /**
     * Move lead to next stage.
     */
    public function moveToNextStage(): bool
    {
        $nextStage = $this->leadStage->getNextStage();
        if ($nextStage) {
            $this->update(['lead_stage_id' => $nextStage->id]);
            $this->logActivity('stage_change', "Moved to {$nextStage->name}");
            return true;
        }
        return false;
    }

    /**
     * Convert lead to client.
     */
    public function convertToClient(): Client
    {
        $client = Client::create([
            'user_id' => $this->user_id,
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'company_name' => $this->company_name,
            'address' => $this->address,
        ]);

        $this->update([
            'status' => 'converted',
            'converted_client_id' => $client->id,
            'converted_at' => now(),
        ]);

        $this->logActivity('conversion', 'Lead converted to client');
        $this->leadSource->updateConversionStats();

        return $client;
    }

    /**
     * Mark lead as lost.
     */
    public function markAsLost(string $reason = null): void
    {
        $this->update([
            'status' => 'lost',
            'lost_reason' => $reason,
        ]);

        $this->logActivity('lost', $reason ? "Lead lost: {$reason}" : 'Lead marked as lost');
        $this->leadSource->updateConversionStats();
    }

    /**
     * Log an activity for this lead.
     */
    public function logActivity(string $type, string $description, array $metadata = []): LeadActivity
    {
        return $this->activities()->create([
            'user_id' => $this->user_id,
            'type' => $type,
            'title' => ucfirst(str_replace('_', ' ', $type)),
            'description' => $description,
            'completed_at' => now(),
            'metadata' => $metadata,
        ]);
    }

    /**
     * Schedule next follow-up.
     */
    public function scheduleFollowUp(Carbon $date, string $description = null): void
    {
        $this->update(['next_follow_up_at' => $date]);

        if ($description) {
            $this->activities()->create([
                'user_id' => $this->user_id,
                'type' => 'follow_up',
                'title' => 'Follow-up Scheduled',
                'description' => $description,
                'scheduled_at' => $date,
                'status' => 'scheduled',
            ]);
        }
    }

    /**
     * Handle proposal acceptance - auto-convert lead to client.
     */
    public function handleProposalAcceptance(Proposal $proposal): Client
    {
        if ($this->status !== 'active') {
            throw new \Exception('Only active leads can be converted via proposal acceptance');
        }

        // Convert lead to client
        $client = $this->convertToClient();

        // Update proposal to link to the new client
        $proposal->update(['client_id' => $client->id]);

        // Log the conversion activity
        $this->logActivity('conversion', "Lead converted to client via proposal acceptance: {$proposal->title}");

        return $client;
    }

    /**
     * Create a proposal for this lead.
     */
    public function createProposal(array $proposalData): Proposal
    {
        $proposalData['user_id'] = $this->user_id;
        $proposalData['lead_id'] = $this->id;

        // If lead is not yet converted, don't set client_id
        if ($this->status === 'converted' && $this->converted_client_id) {
            $proposalData['client_id'] = $this->converted_client_id;
        }

        $proposal = Proposal::create($proposalData);

        // Log activity
        $this->logActivity('proposal_sent', "Proposal created: {$proposal->title}");

        return $proposal;
    }
}
