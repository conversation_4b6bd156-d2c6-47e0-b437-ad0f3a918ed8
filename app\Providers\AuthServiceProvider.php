<?php

namespace App\Providers;

use App\Models\Client;
use App\Models\Contract;
use App\Models\Expense;
use App\Models\ExpenseCategory;
use App\Models\File;
use App\Models\FollowUp;
use App\Models\Invoice;
use App\Models\Lead;
use App\Models\Project;
use App\Models\Proposal;
use App\Models\TdsRecord;
use App\Policies\ClientPolicy;
use App\Policies\ContractPolicy;
use App\Policies\ExpensePolicy;
use App\Policies\ExpenseCategoryPolicy;
use App\Policies\FilePolicy;
use App\Policies\FollowUpPolicy;
use App\Policies\InvoicePolicy;
use App\Policies\LeadPolicy;
use App\Policies\ProjectPolicy;
use App\Policies\ProposalPolicy;
use App\Policies\TdsRecordPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        Client::class => ClientPolicy::class,
        Contract::class => ContractPolicy::class,
        Expense::class => ExpensePolicy::class,
        ExpenseCategory::class => ExpenseCategoryPolicy::class,
        File::class => FilePolicy::class,
        FollowUp::class => FollowUpPolicy::class,
        Invoice::class => InvoicePolicy::class,
        Lead::class => LeadPolicy::class,
        Project::class => ProjectPolicy::class,
        Proposal::class => ProposalPolicy::class,
        TdsRecord::class => TdsRecordPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        //
    }
}
