<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Freeligo') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700&family=poppins:400,500,600,700&display=swap" rel="stylesheet" />

        <!-- FontAwesome -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        <!-- Toast Notifications -->
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
        <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    </head>
    <body class="font-inter antialiased bg-secondary-50">
        <x-sidebar :user="auth()->user()" :title="$title ?? ''" :subtitle="$subtitle ?? ''">
            <!-- Flash Messages -->
            @if (session('success'))
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        Toastify({
                            text: "{{ session('success') }}",
                            duration: 4000,
                            gravity: "top",
                            position: "right",
                            backgroundColor: "#10b981",
                            className: "toast-success",
                        }).showToast();
                    });
                </script>
            @endif

            @if (session('error'))
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        Toastify({
                            text: "{{ session('error') }}",
                            duration: 5000,
                            gravity: "top",
                            position: "right",
                            backgroundColor: "#ef4444",
                            className: "toast-error",
                        }).showToast();
                    });
                </script>
            @endif

            @if (session('warning'))
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        Toastify({
                            text: "{{ session('warning') }}",
                            duration: 4000,
                            gravity: "top",
                            position: "right",
                            backgroundColor: "#f59e0b",
                            className: "toast-warning",
                        }).showToast();
                    });
                </script>
            @endif

            @if (session('info'))
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        Toastify({
                            text: "{{ session('info') }}",
                            duration: 4000,
                            gravity: "top",
                            position: "right",
                            backgroundColor: "#3b82f6",
                            className: "toast-info",
                        }).showToast();
                    });
                </script>
            @endif

            <!-- Validation Errors -->
            @if ($errors->any())
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        @foreach ($errors->all() as $error)
                            Toastify({
                                text: "{{ $error }}",
                                duration: 5000,
                                gravity: "top",
                                position: "right",
                                backgroundColor: "#ef4444",
                                className: "toast-error",
                            }).showToast();
                        @endforeach
                    });
                </script>
            @endif

            <!-- Page Content -->
            <div class="p-6 transition-all duration-300 ease-in-out">
                <div class="max-w-7xl mx-auto">
                    {{ $slot }}
                </div>
            </div>
        </x-sidebar>

        <!-- Custom Styles -->
        <style>
            .toast-success, .toast-error, .toast-warning, .toast-info {
                border-radius: 8px;
                font-family: 'Inter', sans-serif;
                font-weight: 500;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            }

            .animate-fadeIn {
                animation: fadeIn 0.3s ease-in-out;
            }

            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
            }
        </style>
    </body>
</html>
