@props([
    'trigger' => null,
    'position' => 'bottom-left',
    'width' => 'w-48',
    'maxHeight' => 'max-h-64'
])

@php
    $positions = [
        'bottom-left' => 'origin-top-left left-0 mt-2',
        'bottom-right' => 'origin-top-right right-0 mt-2',
        'top-left' => 'origin-bottom-left left-0 mb-2 bottom-full',
        'top-right' => 'origin-bottom-right right-0 mb-2 bottom-full',
    ];
    
    $positionClasses = $positions[$position] ?? $positions['bottom-left'];
@endphp

<div x-data="{ open: false }" class="relative inline-block text-left">
    <!-- Trigger -->
    <div @click="open = !open" class="cursor-pointer">
        {{ $trigger }}
    </div>

    <!-- Dropdown Panel -->
    <div x-show="open"
         @click.away="open = false"
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 scale-95"
         x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-150"
         x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-95"
         class="absolute z-50 {{ $width }} {{ $maxHeight }} {{ $positionClasses }}">
        
        <div class="bg-white rounded-2xl shadow-card-lg border border-secondary-200 py-2 overflow-y-auto scrollbar-thin">
            {{ $slot }}
        </div>
    </div>
</div>

<!-- Dropdown Item Component -->
@php
    // This can be used as <x-ui.dropdown.item>
@endphp
