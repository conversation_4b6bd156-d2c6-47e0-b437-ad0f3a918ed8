<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BusinessSettings extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'currency',
        'timezone',
        'date_format',
        'time_format',
        'fiscal_year_start',
        'invoice_prefix',
        'invoice_number_format',
        'quote_prefix',
        'quote_number_format',
        'payment_terms',
        'late_fee_percentage',
        'tax_settings',
        'email_settings',
        'notification_settings',
        'branding_settings',
        'feature_settings',
        'integration_settings',
        'workflow_settings',
        'security_settings',
    ];

    protected function casts(): array
    {
        return [
            'tax_settings' => 'array',
            'email_settings' => 'array',
            'notification_settings' => 'array',
            'branding_settings' => 'array',
            'feature_settings' => 'array',
            'integration_settings' => 'array',
            'workflow_settings' => 'array',
            'security_settings' => 'array',
            'late_fee_percentage' => 'decimal:2',
        ];
    }

    /**
     * Get the business that owns the settings.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get default settings for a business type.
     */
    public static function getDefaultSettings(string $businessType): array
    {
        $baseSettings = [
            'currency' => config('services.currency.code', 'USD'),
            'timezone' => config('app.timezone', 'UTC'),
            'date_format' => 'Y-m-d',
            'time_format' => 'H:i',
            'fiscal_year_start' => '01-04', // April 1st
            'invoice_prefix' => 'INV',
            'invoice_number_format' => '{prefix}-{year}-{number:4}',
            'quote_prefix' => 'QUO',
            'quote_number_format' => '{prefix}-{year}-{number:4}',
            'payment_terms' => 30,
            'late_fee_percentage' => 0.00,
            'tax_settings' => [
                'default_tax_rate' => 0,
                'tax_inclusive' => false,
                'tax_label' => 'Tax',
            ],
            'email_settings' => [
                'from_name' => '',
                'from_email' => '',
                'reply_to' => '',
                'signature' => '',
            ],
            'notification_settings' => [
                'invoice_created' => true,
                'payment_received' => true,
                'invoice_overdue' => true,
                'project_updates' => true,
                'client_messages' => true,
            ],
            'branding_settings' => [
                'primary_color' => '#10b981',
                'secondary_color' => '#059669',
                'logo_url' => '',
                'favicon_url' => '',
                'custom_css' => '',
            ],
            'security_settings' => [
                'two_factor_required' => false,
                'session_timeout' => 120,
                'password_expiry_days' => 0,
            ],
        ];

        // Customize settings based on business type
        switch ($businessType) {
            case 'freelancer':
                $baseSettings['feature_settings'] = [
                    'time_tracking' => true,
                    'project_management' => true,
                    'expense_tracking' => true,
                    'client_portal' => false,
                    'team_collaboration' => false,
                    'advanced_reporting' => false,
                    'api_access' => false,
                ];
                break;

            case 'startup':
                $baseSettings['feature_settings'] = [
                    'time_tracking' => true,
                    'project_management' => true,
                    'expense_tracking' => true,
                    'client_portal' => true,
                    'team_collaboration' => true,
                    'advanced_reporting' => true,
                    'api_access' => false,
                ];
                break;

            case 'small_business':
                $baseSettings['feature_settings'] = [
                    'time_tracking' => true,
                    'project_management' => true,
                    'expense_tracking' => true,
                    'client_portal' => true,
                    'team_collaboration' => true,
                    'advanced_reporting' => true,
                    'api_access' => true,
                ];
                break;
        }

        return $baseSettings;
    }

    /**
     * Get a specific setting value.
     */
    public function getSetting(string $key, $default = null)
    {
        return data_get($this->toArray(), $key, $default);
    }

    /**
     * Update a specific setting.
     */
    public function updateSetting(string $key, $value): void
    {
        $keys = explode('.', $key);
        $field = array_shift($keys);

        if (empty($keys)) {
            $this->update([$field => $value]);
        } else {
            $currentValue = $this->{$field} ?? [];
            data_set($currentValue, implode('.', $keys), $value);
            $this->update([$field => $currentValue]);
        }
    }

    /**
     * Get formatted currency symbol.
     */
    public function getCurrencySymbol(): string
    {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'INR' => '₹',
            'JPY' => '¥',
            'CAD' => 'C$',
            'AUD' => 'A$',
        ];

        return $symbols[$this->currency] ?? $this->currency;
    }

    /**
     * Get next invoice number.
     */
    public function getNextInvoiceNumber(): string
    {
        $lastInvoice = $this->business->invoices()
            ->whereYear('created_at', now()->year)
            ->orderBy('id', 'desc')
            ->first();

        $nextNumber = $lastInvoice ? ($lastInvoice->invoice_number_sequence + 1) : 1;

        return str_replace(
            ['{prefix}', '{year}', '{number:4}'],
            [$this->invoice_prefix, now()->year, str_pad($nextNumber, 4, '0', STR_PAD_LEFT)],
            $this->invoice_number_format
        );
    }
}
