<?php

namespace App\Mail;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class PaymentFailedMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public User $user;
    public array $paymentDetails;
    public string $planName;

    /**
     * Create a new message instance.
     */
    public function __construct(User $user, array $paymentDetails, string $planName = 'Pro')
    {
        $this->user = $user;
        $this->paymentDetails = $paymentDetails;
        $this->planName = $planName;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: "Payment Issue - Let's Get This Sorted Out",
            to: [$this->user->email],
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.payment-failed',
            with: [
                'user' => $this->user,
                'paymentDetails' => $this->paymentDetails,
                'planName' => $this->planName,
                'header_title' => 'Payment Issue',
                'header_subtitle' => 'Don\'t worry - we\'ll help you get this resolved quickly',
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
