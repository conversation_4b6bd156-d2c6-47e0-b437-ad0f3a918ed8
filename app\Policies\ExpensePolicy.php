<?php

namespace App\Policies;

use App\Models\Expense;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class ExpensePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view expenses');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Expense $expense): bool
    {
        // Check permission first
        if (!$user->can('view expenses')) {
            return false;
        }

        // Admin can view all expenses, users can only view their own
        return $user->hasRole('admin') || $expense->user_id === $user->id;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create expenses');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Expense $expense): bool
    {
        // Check permission first
        if (!$user->can('edit expenses')) {
            return false;
        }

        // Admin can update all expenses, users can only update their own
        if ($user->hasRole('admin')) {
            return true;
        }

        // Users can only update their own expenses
        if ($expense->user_id !== $user->id) {
            return false;
        }

        // Check if expense can be edited based on status
        return $expense->canBeEdited();
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Expense $expense): bool
    {
        // Check permission first
        if (!$user->can('delete expenses')) {
            return false;
        }

        // Admin can delete all expenses, users can only delete their own
        if ($user->hasRole('admin')) {
            return true;
        }

        // Users can only delete their own expenses
        if ($expense->user_id !== $user->id) {
            return false;
        }

        // Check if expense can be deleted based on status
        return $expense->canBeDeleted();
    }

    /**
     * Determine whether the user can submit the expense for approval.
     */
    public function submit(User $user, Expense $expense): bool
    {
        return $user->can('submit expenses')
            && $expense->user_id === $user->id
            && $expense->status === 'draft';
    }

    /**
     * Determine whether the user can approve the expense.
     */
    public function approve(User $user, Expense $expense): bool
    {
        return $user->hasRole('admin') && $user->can('approve expenses');
    }

    /**
     * Determine whether the user can reject the expense.
     */
    public function reject(User $user, Expense $expense): bool
    {
        return $user->hasRole('admin') && $user->can('reject expenses');
    }

    /**
     * Determine whether the user can export expenses.
     */
    public function export(User $user): bool
    {
        return $user->can('export expenses');
    }

    /**
     * Determine whether the user can view expense reports.
     */
    public function viewReports(User $user): bool
    {
        return $user->can('view expense reports');
    }

    /**
     * Determine whether the user can export expense reports.
     */
    public function exportReports(User $user): bool
    {
        return $user->can('export expense reports');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Expense $expense): bool
    {
        return $user->hasRole('admin');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Expense $expense): bool
    {
        return $user->hasRole('admin');
    }
}
